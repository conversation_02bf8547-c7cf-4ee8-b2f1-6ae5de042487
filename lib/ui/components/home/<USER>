import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class GetStartedWidget extends StatefulWidget {
  const GetStartedWidget({
    super.key,
  });

  @override
  State<GetStartedWidget> createState() => _GetStartedWidgetState();
}

class _GetStartedWidgetState extends State<GetStartedWidget> {
  bool closeGetStarted = false;
  @override
  Widget build(BuildContext context) {
    final authVm = context.watch<AuthUserVM>();

    // Calculate completion percentage based on isDone status of tasks
    final List<bool> taskCompletionStatus = [
      (authVm.user?.kycStep ?? 0) >= 2, // Complete your profile
      authVm.userIsVerified, // Upload your ID document
      authVm.user?.hasCreditTransaction ?? false, // Fund your wallet
      authVm.user?.hasDebitTransaction ?? false, // Send money
      authVm.user?.hasReferrals ?? false, // Invite a friend
    ];

    final completedTasks =
        taskCompletionStatus.where((isDone) => isDone).length;
    final totalTasks = taskCompletionStatus.length;
    final completionPercent =
        totalTasks > 0 ? completedTasks / totalTasks : 0.0;

    // Find the index of the next incomplete step
    final nextIncompleteStepIndex =
        taskCompletionStatus.indexWhere((isDone) => !isDone);

    printty("completedTasks $completedTasks");

    return authVm.closeGetStarted && completedTasks > 4
        ? const SizedBox.shrink()
        : Container(
            padding: EdgeInsets.all(Sizer.radius(16)),
            margin: EdgeInsets.only(
              top: Sizer.height(20),
              left: Sizer.width(24),
              right: Sizer.width(24),
            ),
            decoration: BoxDecoration(
              color: AppColors.white,
              border: Border.all(
                color: AppColors.grayAEC,
              ),
              borderRadius: BorderRadius.circular(
                Sizer.radius(12),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.gray88.withValues(alpha: 0.06),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Get started",
                      style: AppTypography.text16.semiBold,
                    ),
                    Spacer(),
                    if (completedTasks >= 4)
                      InkWell(
                        onTap: () {
                          authVm.setCloseGetStarted(true);
                        },
                        child: Icon(
                          Icons.close,
                          size: Sizer.width(14),
                          color: AppColors.gray93,
                        ),
                      )
                  ],
                ),
                Text(
                  "Finish setting up your account by completing the following",
                  style: AppTypography.text12.copyWith(
                    fontWeight: FontWeight.w300,
                    color: AppColors.mainBlack,
                  ),
                ),
                YBox(20),
                LinearPercentIndicator(
                  animation: true,
                  animationDuration: 2000,
                  lineHeight: Sizer.height(4),
                  percent: completionPercent,
                  backgroundColor: AppColors.grayFO,
                  progressColor: AppColors.primaryBlue,
                  animateFromLastPercent: true,
                  restartAnimation: false,
                ),
                YBox(20),
                if (completedTasks < 4)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GetStartedRowTile(
                        title: "Complete your profile",
                        isDone: (authVm.user?.kycStep ?? 0) >= 2,
                        nextStepInline: nextIncompleteStepIndex == 0,
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.completeProfileScreen);
                        },
                      ),
                      YBox(16),
                      GetStartedRowTile(
                        title: "Upload your ID document",
                        status: authVm.user?.status,
                        isDone: authVm.userIsVerified,
                        nextStepInline: nextIncompleteStepIndex == 1,
                        onTap: () {
                          final status = authVm.user?.status?.toLowerCase();
                          // if (status == UserVerificationStatus.pending) {
                          //   BsWrapper.bottomSheet(
                          //     context: context,
                          //     widget: VerifyIdentifyModal(),
                          //   );
                          // }
                          if (status == UserVerificationStatus.processing) {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: DocumentPendingVerificationDialog(),
                            );
                            return;
                          }
                          if (status == UserVerificationStatus.failed) {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: FailedVerificationDialog(),
                            );
                            return;
                          }
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: VerifyIdentifyModal(),
                          );
                        },
                      ),
                      YBox(16),
                      GetStartedRowTile(
                        title: "Fund your wallet",
                        isDone: authVm.user?.hasCreditTransaction ?? false,
                        nextStepInline: nextIncompleteStepIndex == 2,
                        onTap: () async {
                          final authVm = context.read<AuthUserVM>();
                          if (authVm.statusProcessing) {
                            BsWrapper.showCustomDialog(
                              context,
                              child: DocumentPendingVerificationDialog(),
                            );
                            return;
                          }

                          if (!authVm.userIsVerified) {
                            showWarningToast(
                                "Complete your KYC to fund your wallet");
                            return;
                          }

                          final res = await BsWrapper.bottomSheet(
                            context: context,
                            widget: SelectWalletModal(),
                          );

                          if (res is Wallet) {
                            if (context.mounted) {
                              BsWrapper.bottomSheet(
                                context: context,
                                widget: FundYourWalletModal(wallet: res),
                              );
                            }
                          }
                        },
                      ),
                      YBox(16),
                      GetStartedRowTile(
                        title: "Send money",
                        isDone: authVm.user?.hasDebitTransaction ?? false,
                        nextStepInline: nextIncompleteStepIndex == 3,
                        onTap: () {
                          final authVm = context.read<AuthUserVM>();
                          final walletVm = context.read<WalletVM>();
                          if (authVm.statusProcessing) {
                            BsWrapper.showCustomDialog(
                              context,
                              child: DocumentPendingVerificationDialog(),
                            );
                            return;
                          }

                          if (!authVm.userIsVerified) {
                            showWarningToast("Complete your KYC to send money");
                            return;
                          }

                          if (authVm.user?.hasCreditTransaction == false) {
                            showWarningToast("Fund your wallet to send money");
                            return;
                          }

                          /// if user has freq dest country set
                          /// then use it as default
                          final fromCurrencyId =
                              walletVm.activeWallet?.currency?.id ?? 0;
                          final toCurrencyId =
                              authVm.user?.frequentDestinationCurrency?.id ??
                                  walletVm.inactiveWallet?.currency?.id ??
                                  0;

                          Navigator.pushNamed(
                            context,
                            RoutePath.sendMoneyScreen,
                            arguments: SendMoneyArg(
                              fromCurrencyId: fromCurrencyId,
                              toCurrencyId: toCurrencyId,
                            ),
                          );
                        },
                      ),
                      YBox(16),
                    ],
                  ),
                GetStartedRowTile(
                  title:
                      "Invite a friend and earn ${authVm.referralRule?.currency.symbol}${authVm.referralRule?.referredBonus ?? "0"}",
                  isDone: authVm.user?.hasReferrals ?? false,
                  onTap: () {
                    final authVm = context.read<AuthUserVM>();
                    if (authVm.statusProcessing) {
                      BsWrapper.showCustomDialog(
                        context,
                        child: DocumentPendingVerificationDialog(),
                      );
                      return;
                    }

                    if (!authVm.userIsVerified) {
                      showWarningToast(
                          "Complete your KYC to invite family and friends to earn  ${authVm.referralRule?.currency.symbol}${authVm.referralRule?.referredBonus ?? "0"}");
                      return;
                    }

                    Navigator.pushNamed(
                      context,
                      RoutePath.dashboardNav,
                      arguments: 3,
                    );
                  },
                )
              ],
            ),
          );
  }
}

class GetStartedRowTile extends StatelessWidget {
  const GetStartedRowTile({
    super.key,
    this.isDone = false,
    this.nextStepInline = false,
    this.status,
    required this.title,
    this.onTap,
  });

  final bool isDone;
  final bool nextStepInline;
  final String title;
  final String? status;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isDone ? null : onTap,
      child: Row(
        children: [
          Container(
            height: Sizer.height(24),
            width: Sizer.width(24),
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.grayAEC,
              ),
              borderRadius: BorderRadius.circular(Sizer.radius(24)),
            ),
            child: isDone ? SvgPicture.asset(AppSvgs.circleCheck) : null,
          ),
          XBox(12),
          Text(
            title,
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w300,
              color: isDone
                  ? AppColors.mainBlack.withValues(alpha: 0.5)
                  : AppColors.mainBlack,
            ),
          ),
          if (status != null)
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(4),
                vertical: Sizer.height(3),
              ),
              margin: EdgeInsets.only(
                left: Sizer.width(12),
              ),
              decoration: BoxDecoration(
                color: _getTextColor(status ?? "").withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(
                  Sizer.radius(4),
                ),
              ),
              child: Text(
                status ?? "",
                style: AppTypography.text10.copyWith(
                  color: _getTextColor(status ?? ""),
                ),
              ),
            ),
          Spacer(),
          if (nextStepInline)
            Icon(
              Iconsax.arrow_right_3,
              size: Sizer.width(20),
            )
        ],
      ),
    );
  }

  Color _getTextColor(String status) {
    final normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case UserVerificationStatus.verified:
        return AppColors.green43;
      case UserVerificationStatus.processing:
        return AppColors.yellow;
      case UserVerificationStatus.failed:
        return AppColors.red00;
      default:
        return AppColors.red00;
    }
  }
}
