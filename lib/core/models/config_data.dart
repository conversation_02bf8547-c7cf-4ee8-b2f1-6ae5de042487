import 'dart:convert';

ConfigData configDataFromJson(String str) =>
    ConfigData.fromJson(json.decode(str));

String configDataToJson(ConfigData data) => json.encode(data.toJson());

class ConfigData {
  String? nigerianVirtualAccountProvider;
  String? referralBonusAmount;
  String? transactionAmountForReferralBonus;
  String? phoneNumber;
  String? emailAddress;
  String? interacDepositEmail;
  String? facebookUrl;
  String? twitterUrl;
  String? instagramUrl;
  String? linkedinUrl;
  String? appStoreUrl;
  String? playStoreUrl;
  String? marketplaceMinimumAmount;
  String? marketplaceMaximumAmount;
  String? newCustomersRateMinimumAmount;
  String? iosAppVersion;
  String? androidAppVersion;

  ConfigData({
    this.nigerianVirtualAccountProvider,
    this.referralBonusAmount,
    this.transactionAmountForReferralBonus,
    this.phoneNumber,
    this.emailAddress,
    this.interacDepositEmail,
    this.facebookUrl,
    this.twitterUrl,
    this.instagramUrl,
    this.linkedinUrl,
    this.appStoreUrl,
    this.playStoreUrl,
    this.marketplaceMinimumAmount,
    this.marketplaceMaximumAmount,
    this.newCustomersRateMinimumAmount,
    this.iosAppVersion,
    this.androidAppVersion,
  });

  factory ConfigData.fromJson(Map<String, dynamic> json) => ConfigData(
        nigerianVirtualAccountProvider:
            json["nigerian_virtual_account_provider"],
        referralBonusAmount: json["referral_bonus_amount_for_referrer"],
        transactionAmountForReferralBonus:
            json["transaction_amount_for_referral_bonus"],
        phoneNumber: json["phone_number"],
        emailAddress: json["email_address"],
        interacDepositEmail: json["interac_deposit_email"],
        facebookUrl: json["facebook_url"],
        twitterUrl: json["twitter_url"],
        instagramUrl: json["instagram_url"],
        linkedinUrl: json["linkedin_url"],
        appStoreUrl: json["app_store_url"],
        playStoreUrl: json["play_store_url"],
        marketplaceMinimumAmount: json["marketplace_minimum_amount"],
        marketplaceMaximumAmount: json["marketplace_maximum_amount"],
        newCustomersRateMinimumAmount:
            json["new_customers_rate_minimum_amount"],
        iosAppVersion: json["ios_app_version"],
        androidAppVersion: json["android_app_version"],
      );

  Map<String, dynamic> toJson() => {
        "nigerian_virtual_account_provider": nigerianVirtualAccountProvider,
        "referral_bonus_amount": referralBonusAmount,
        "transaction_amount_for_referral_bonus":
            transactionAmountForReferralBonus,
        "phone_number": phoneNumber,
        "email_address": emailAddress,
        "interac_deposit_email": interacDepositEmail,
        "facebook_url": facebookUrl,
        "twitter_url": twitterUrl,
        "instagram_url": instagramUrl,
        "linkedin_url": linkedinUrl,
        "app_store_url": appStoreUrl,
        "play_store_url": playStoreUrl,
        "marketplace_minimum_amount": marketplaceMinimumAmount,
        "marketplace_maximum_amount": marketplaceMaximumAmount,
        "new_customers_rate_minimum_amount": newCustomersRateMinimumAmount,
        "ios_app_version": iosAppVersion,
        "android_app_version": androidAppVersion,
      };
}
