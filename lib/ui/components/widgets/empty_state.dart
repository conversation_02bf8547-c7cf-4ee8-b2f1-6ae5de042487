import 'package:korrency/core/core.dart';

class EmptyState extends StatelessWidget {
  const EmptyState({
    super.key,
    required this.title,
    this.subtitle,
  });

  final String title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          AppSvgs.empty,
          height: Sizer.height(160),
        ),
        YBox(28),
        Text(
          title,
          style: AppTypography.text18.semiBold,
        ),
        YBox(8),
        Text(
          subtitle ?? "",
          textAlign: TextAlign.center,
          style: AppTypography.text15.copyWith(
            color: AppColors.gray808,
          ),
        ),
      ],
    );
  }
}
