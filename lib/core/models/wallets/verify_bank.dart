import 'dart:convert';

VerifyBank verifyBankFromJson(String str) =>
    VerifyBank.fromJson(json.decode(str));

String verifyBankToJson(VerifyBank data) => json.encode(data.toJson());

class VerifyBank {
  String? accountNumber;
  String? accountName;
  String? fees;
  String? destinationBankUuid;

  VerifyBank({
    this.accountNumber,
    this.accountName,
    this.fees,
    this.destinationBankUuid,
  });

  factory VerifyBank.fromJson(Map<String, dynamic> json) => VerifyBank(
        accountNumber: json["account_number"],
        accountName: json["account_name"],
        fees: json["fees"],
        destinationBankUuid: json["destination_bank_uuid"],
      );

  Map<String, dynamic> toJson() => {
        "account_number": accountNumber,
        "account_name": accountName,
        "fees": fees,
        "destination_bank_uuid": destinationBankUuid,
      };
}
