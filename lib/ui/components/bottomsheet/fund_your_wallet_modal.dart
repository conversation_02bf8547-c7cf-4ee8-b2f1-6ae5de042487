import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class FundYourWalletModal extends StatefulWidget {
  const FundYourWalletModal({super.key, required this.wallet});

  final Wallet wallet;

  @override
  State<FundYourWalletModal> createState() => _FundYourWalletModalState();
}

class _FundYourWalletModalState extends State<FundYourWalletModal> {
  FundWallletType? fundWallletType;
  String get currencyCode => widget.wallet.currency?.code ?? "";
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, walletVm, _) {
      return Padding(
        padding: EdgeInsets.all(Sizer.radius(12)),
        child: SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    YB<PERSON>(20),
                    image<PERSON>elper(
                      AppImages.fundWallet,
                      height: Sizer.height(191),
                    ),
                    YBox(20),
                    Text(
                      "Fund your Wallet",
                      style: AppTypography.text22.semiBold,
                    ),
                    YBox(4),
                    Text(
                      "Choose a preferred method to add funds to your wallet",
                      textAlign: TextAlign.center,
                      style: AppTypography.text16.copyWith(
                        color: AppColors.gray93,
                      ),
                    ),
                    YBox(32),
                    if (currencyCode == CurrencyConst.cadCurrency)
                      FundWalletColumnTile(
                        margin: 20,
                        isChecked: fundWallletType == FundWallletType.interac,
                        title: "Popular",
                        method: "Interac e-Transfer",
                        onCheck: () {
                          fundWallletType = FundWallletType.interac;
                          setState(() {});
                        },
                      ),
                    if (currencyCode == CurrencyConst.nairaCurrency ||
                        currencyCode == CurrencyConst.gbpCurrency ||
                        currencyCode == CurrencyConst.eurCurrency)
                      FundWalletColumnTile(
                        margin: 20,
                        isChecked:
                            fundWallletType == FundWallletType.bankTransfer,
                        title: "Popular",
                        method: "Bank Transfer",
                        iconPath: AppSvgs.bank,
                        onCheck: () {
                          fundWallletType = FundWallletType.bankTransfer;
                          setState(() {});
                        },
                      ),
                    FundWalletColumnTile(
                      isChecked: fundWallletType == FundWallletType.username,
                      title: "Other Methods",
                      method: "Share your Korrency Username",
                      iconPath: AppSvgs.share,
                      onCheck: () {
                        fundWallletType = FundWallletType.username;
                        setState(() {});
                      },
                    ),
                    YBox(40),
                    CustomBtn.solid(
                      online: fundWallletType != null,
                      text: "Continue",
                      borderRadius: BorderRadius.circular(20),
                      onTap: () {
                        switch (fundWallletType) {
                          case FundWallletType.interac:
                            Navigator.pushNamed(
                                context, RoutePath.interacTransferScreen);
                            break;
                          case FundWallletType.bankTransfer:
                            if (currencyCode == CurrencyConst.nairaCurrency) {
                              Navigator.pushNamed(
                                  context, RoutePath.accountDetailsScreen);
                            } else {
                              Navigator.pushNamed(
                                context,
                                RoutePath.eurAccountDetailsScreen,
                                arguments: widget.wallet,
                              );
                            }
                            break;
                          case FundWallletType.username:
                            Navigator.pushNamed(
                                context, RoutePath.shareByUsernameScreen);
                            break;
                          default:
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: 'Please select a method',
                            );
                        }
                      },
                    ),
                    YBox(30),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}

class FundWalletColumnTile extends StatelessWidget {
  const FundWalletColumnTile({
    super.key,
    this.isChecked = false,
    required this.title,
    required this.method,
    this.iconPath,
    this.onCheck,
    this.margin,
  });

  final bool isChecked;
  final String title;
  final String method;
  final String? iconPath;
  final Function()? onCheck;
  final double? margin;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onCheck,
      child: Container(
        margin: EdgeInsets.only(
          bottom: margin ?? 0,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                title,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray93,
                ),
              ),
            ),
            YBox(8),
            Row(
              children: [
                SizedBox(
                  height: Sizer.height(24),
                  width: Sizer.width(24),
                  child: iconPath != null
                      ? Container(
                          decoration: BoxDecoration(
                            color: AppColors.blue100,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: SvgPicture.asset(
                              iconPath ?? AppSvgs.share,
                              height: Sizer.height(16),
                              width: Sizer.width(16),
                            ),
                          ),
                        )
                      : Image.asset(AppImages.interac),
                ),
                XBox(8),
                Expanded(
                  child: Text(
                    method,
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
                XBox(10),
                CustomCheckRounded(
                  isSelected: isChecked,
                  onTap: onCheck,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
