import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:skeletonizer/skeletonizer.dart';

class UserListTile extends StatelessWidget {
  const UserListTile({
    super.key,
    required this.name,
    this.avater,
    this.subTitle,
    this.trailingWidget,
    this.showTrailing = false,
    this.showSubTitle = false,
    this.useImageNetwork = false,
    this.onTap,
  });

  final String name;
  final String? avater;
  final String? subTitle;
  final Widget? trailingWidget;
  final bool showTrailing;
  final bool showSubTitle;
  final bool useImageNetwork;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Skeleton.replace(
            replacement: const Bone.circle(
              size: 50,
            ),
            child: Container(
              height: Sizer.height(36),
              width: Sizer.width(36),
              decoration: BoxDecoration(
                color: AppColors.primaryLightBlue,
                borderRadius: BorderRadius.circular(30),
              ),
              child: avater != null
                  ? useImageNetwork
                      ? Image.network(
                          avater ?? '',
                          height: Sizer.height(36),
                          width: Sizer.width(36),
                        )
                      : SvgPicture.network(
                          avater ?? '',
                          height: Sizer.height(36),
                          width: Sizer.width(36),
                        )
                  : imageHelper(
                      AppImages.user,
                    ),
            ),
          ),
          const XBox(12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                name,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textBlack100,
                ),
              ),
              if (showSubTitle) const YBox(4),
              if (showSubTitle)
                Text(
                  subTitle ?? '',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.gray500,
                  ),
                ),
            ],
          ),
          const Spacer(),
          if (showTrailing)
            Container(
              child: trailingWidget ??
                  Icon(
                    Iconsax.arrow_right_3,
                    size: Sizer.radius(24),
                    color: AppColors.iconBlack800,
                  ),
            ),
        ],
      ),
    );
  }
}
