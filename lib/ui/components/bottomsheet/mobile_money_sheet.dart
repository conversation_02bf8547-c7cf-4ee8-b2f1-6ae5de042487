import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class MobileMoneySheet extends StatefulWidget {
  const MobileMoneySheet({
    Key? key,
    this.title,
  }) : super(key: key);

  final String? title;

  @override
  State<MobileMoneySheet> createState() => _MobileMoneySheetState();
}

class _MobileMoneySheetState extends State<MobileMoneySheet> {
  int currentIndex = -1;
  @override
  Widget build(BuildContext context) {
    return Consumer<BankVM>(builder: (context, vm, _) {
      return ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.5,
        child: Column(
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(10),
            Expanded(
              child: ListView(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                  top: Sizer.height(10),
                ),
                children: [
                  // CustomTextField(
                  //   borderRadius: Sizer.height(4),
                  //   prefixIcon: Icon(
                  //     Iconsax.search_normal_1,
                  //     color: AppColors.black600,
                  //     size: Sizer.radius(20),
                  //   ),
                  //   hintText: 'Search bank name',
                  //   onChanged: (val) {
                  //     vm.searchBanks(val);
                  //   },
                  // ),
                  const YBox(24),
                  Builder(builder: (context) {
                    if (vm.mobileMoneyList.isEmpty) {
                      return SizedBox(
                        height: Sizer.height(400),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Iconsax.bank,
                                size: Sizer.radius(40),
                              ),
                              const YBox(10),
                              Text(
                                "No Data Available",
                                style: AppTypography.text16.copyWith(
                                  color: AppColors.textBlack600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (ctx, i) {
                        var m = vm.mobileMoneyList[i];
                        return InkWell(
                          onTap: () {
                            currentIndex = i;
                            // if (selectBankType == SelectBankType.beneficiary) {
                            //   context.read<BeneficiaryVM>().setBank(bank);
                            // } else {
                            //   context.read<SendMoneyVM>().setBank(bank);
                            // }
                            Navigator.pop(context);
                          },
                          child: ContainerWithBluewishBg(
                            padding: EdgeInsets.symmetric(
                              vertical: Sizer.height(10),
                              horizontal: Sizer.width(16),
                            ),
                            child: WalletListTile(
                              title: m.name ?? "",
                              trailingIconSize: 16,
                              isSelected:
                                  currentIndex == i || widget.title == m.name,
                              onTap: () {
                                Navigator.pop(context, m);
                              },
                            ),
                          ),
                        );
                      },
                      separatorBuilder: (ctx, _) => const YBox(16),
                      itemCount: vm.mobileMoneyList.length,
                    );
                  }),
                  const YBox(100),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
