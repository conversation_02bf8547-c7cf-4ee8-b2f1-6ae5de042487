import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:korrency/ui/screens/dashboard/menu/beneficiary/bank_transfer_screen.dart';

// This screen is for other african countries excpt NGN

class IbanTransferScreen extends StatefulWidget {
  const IbanTransferScreen({
    super.key,
    required this.arg,
  });

  final BeneficiaryPaymentArg arg;

  @override
  State<IbanTransferScreen> createState() => _IbanTransferScreenState();
}

class _IbanTransferScreenState extends State<IbanTransferScreen> {
  final firstNameC = TextEditingController();
  final lastNameC = TextEditingController();
  final ibanC = TextEditingController();

  // verifying iban return true or false;
  bool ibanIsVerified = false;
  String? errorMessage;

  // Timer for debouncing validation
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    firstNameC.dispose();
    lastNameC.dispose();
    ibanC.dispose();
    _debounceTimer?.cancel();

    super.dispose();
  }

  verifyIban() async {
    final sendMoneyVm = context.read<SendMoneyVM>();
    final res = await sendMoneyVm.verifyIBAN(
      ibanNUM: ibanC.text,
      firstName: firstNameC.text,
      lastName: lastNameC.text,
      currencyId: widget.arg.currency.id ?? 0,
      amount: "100",
    );

    ibanIsVerified = false;
    errorMessage = null;
    if (!res.success) {
      errorMessage = res.message;
    } else {
      ibanIsVerified = true;
    }

    setState(() {});

    // handleApiResponse(
    //   response: res,
    //   onSuccess: () {
    //     ibanIsVerified = true;
    //     setState(() {});
    //   },
    // );
  }

  // Check if all required fields are filled
  bool get _areAllFieldsFilled =>
      ibanC.text.trim().isNotEmpty &&
      firstNameC.text.trim().isNotEmpty &&
      lastNameC.text.trim().isNotEmpty;

  // Listener for field changes with debounce
  void _onFieldChanged() {
    ibanIsVerified = false;
    // Update UI immediately for field enabling/disabling
    setState(() {});

    // Set up debounced validation
    _debounceTimer = Timer(const Duration(milliseconds: 800), () {
      // Auto-call verifyIban when all fields are filled for the first time
      if (_areAllFieldsFilled && !ibanIsVerified) {
        verifyIban();
      }
    });
  }

  // Check if IBAN is filled (required before other fields)
  bool get _isIbanFilled => ibanC.text.trim().isNotEmpty;

  String get accountName =>
      "${firstNameC.text.trim()} ${lastNameC.text.trim()}";

  bool get isFormValid {
    return firstNameC.text.isNotEmpty &&
        lastNameC.text.isNotEmpty &&
        ibanC.text.isNotEmpty &&
        accountName.isNotEmpty &&
        ibanIsVerified;
  }

  @override
  Widget build(BuildContext context) {
    final sendVm = context.watch<SendMoneyVM>();
    return Consumer<BeneficiaryVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'New Beneficiary',
            onBackBtnTap: () {
              vm.clearData();
              context.read<BankVM>().resetData();
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              children: [
                YBox(20),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextField(
                      controller: ibanC,
                      labelText: "IBAN",
                      showLabelHeader: true,
                      hintText: 'Type beneficiary IBAN',
                      borderRadius: Sizer.height(12),
                      suffixIcon: sendVm.busy(verifyingBankState)
                          ? const CupertinoActivityIndicator()
                          : null,
                      onChanged: (val) => setState(() {}),
                      onTap: () {},
                    ),
                    YBox(20),
                    CustomTextField(
                      controller: firstNameC,
                      labelText: 'First Name',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(12),
                      isReadOnly: !_isIbanFilled,
                      prefixIcon: Icon(
                        Iconsax.user,
                        color: AppColors.gray500,
                        size: Sizer.height(20),
                      ),
                      suffixIcon: sendVm.busy(verifyingBankState)
                          ? const CupertinoActivityIndicator()
                          : null,
                      onChanged: (p0) {
                        AppUtils.handleTextCapitalization(firstNameC, p0);
                        _onFieldChanged();
                      },
                      onTap: () {
                        if (!_isIbanFilled) {
                          showWarningToast('Please enter IBAN first');
                        }
                      },
                    ),
                    const YBox(16),
                    CustomTextField(
                      controller: lastNameC,
                      labelText: 'Last Name',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(12),
                      isReadOnly: !_isIbanFilled,
                      prefixIcon: Icon(
                        Iconsax.user,
                        color: AppColors.gray500,
                        size: Sizer.height(20),
                      ),
                      suffixIcon: sendVm.busy(verifyingBankState)
                          ? const CupertinoActivityIndicator()
                          : null,
                      onChanged: (p0) {
                        AppUtils.handleTextCapitalization(lastNameC, p0);
                        _onFieldChanged();
                      },
                      onTap: () {
                        if (!_isIbanFilled) {
                          showWarningToast('Please enter IBAN first');
                        }
                      },
                    ),
                    if (errorMessage != null)
                      Padding(
                        padding: EdgeInsets.only(
                          top: Sizer.height(8),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: AppColors.red,
                            ),
                            const XBox(8),
                            Expanded(
                              child: Text(
                                errorMessage!,
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    else if (ibanIsVerified)
                      AccountNameWidget(accountName: accountName),
                  ],
                ),
                const Spacer(),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final result = await BsWrapper.bottomSheet(
                      context: context,
                      widget: BeneficiaryScamSheet(),
                    );
                    if (result is bool && result) {
                      _saveBeneficiary();
                    }
                  },
                  online: isFormValid,
                  text: "Save Beneficiary",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  _saveBeneficiary() {
    var beneficiaryVM = context.read<BeneficiaryVM>();
    PostBeneficiaryArgs arg = PostBeneficiaryArgs(
      currencyId: widget.arg.currency.id ?? 0,
      accountName: accountName,
      accountIdentifier: ibanC.text.trim(),
      institutionCode: "iban",
      firstName: firstNameC.text.trim(),
      lastName: lastNameC.text.trim(),
      transferMethod: TransferMethod.iban,
    );

    beneficiaryVM
        .saveBeneficiary(
      arg: arg,
      transferMethodType: TransferMethodType.iban,
    )
        .then((value) {
      if (value.success) {
        firstNameC.clear();
        lastNameC.clear();

        beneficiaryVM
          ..clearData()
          ..getBeneficiaries();
        _showComfirmationScreen(msg: value.message.toString());
      } else {
        _showComfirmationScreen(
          isFailed: true,
          msg: value.message.toString(),
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Beneficiary Saved\n Failed"
                : "Beneficiary Saved\n Successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
          if (!isFailed) {
            _pop();
            _pop();
            _pop();
            Navigator.pushNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.beneficiaryScreen,
            );
          }
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
