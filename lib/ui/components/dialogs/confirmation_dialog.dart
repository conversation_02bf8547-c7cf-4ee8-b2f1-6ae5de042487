// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConfirmationDialogArgs {
  final String title;
  final String? content;
  final String btnText;
  final bool isImage;
  final Function() onTap;

  ConfirmationDialogArgs({
    required this.title,
    required this.btnText,
    this.content,
    this.isImage = false,
    required this.onTap,
  });
}

class ConfirmationDialog extends StatefulWidget {
  const ConfirmationDialog({super.key, required this.args});

  final ConfirmationDialogArgs args;

  @override
  State<ConfirmationDialog> createState() => _ConfirmationDialogState();
}

class _ConfirmationDialogState extends State<ConfirmationDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _iconController;
  late AnimationController _contentController;
  late AnimationController _buttonController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _iconAnimation;
  late Animation<double> _contentAnimation;
  late Animation<double> _buttonAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _iconController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _contentController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Create animations
    _scaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _iconAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));

    _contentAnimation = CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeIn,
    );

    _buttonAnimation = CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeOutBack,
    );

    // Start animations with staggered timing
    _startAnimations();
  }

  void _startAnimations() {
    _scaleController.forward();

    Future.delayed(const Duration(milliseconds: 200), () {
      _iconController.forward();
    });

    Future.delayed(const Duration(milliseconds: 500), () {
      _contentController.forward();
    });

    Future.delayed(const Duration(milliseconds: 800), () {
      _buttonController.forward();
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _iconController.dispose();
    _contentController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return FadeTransition(
        opacity: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.symmetric(
            horizontal: Sizer.width(12),
          ),
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: Sizer.screenWidth,
              decoration: BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const YBox(28),
                  ScaleTransition(
                    scale: _iconAnimation,
                    child: widget.args.isImage
                        ? Image.asset(
                            AppImages.lTrash,
                            fit: BoxFit.cover,
                            // height: Sizer.height(140),
                          )
                        : SvgPicture.asset(
                            AppSvgs.tickOn,
                            height: Sizer.height(140),
                          ),
                  ),
                  YBox(20),
                  FadeTransition(
                    opacity: _contentAnimation,
                    child: Column(
                      children: [
                        Text(
                          widget.args.title,
                          textAlign: TextAlign.center,
                          style: FontTypography.text22.semiBold,
                        ),
                        if (widget.args.content != null)
                          Padding(
                            padding: EdgeInsets.only(
                              top: Sizer.height(4),
                            ),
                            child: Text(
                              widget.args.content ?? "",
                              textAlign: TextAlign.center,
                              style: FontTypography.text16.withCustomColor(
                                AppColors.gray93,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  const YBox(28),
                  SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 1),
                      end: Offset.zero,
                    ).animate(_buttonAnimation),
                    child: CustomBtn.solid(
                      width: Sizer.width(280),
                      onTap: widget.args.onTap,
                      borderRadius: BorderRadius.circular(Sizer.radius(14)),
                      text: widget.args.btnText,
                    ),
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
