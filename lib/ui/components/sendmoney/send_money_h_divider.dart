import 'package:korrency/core/core.dart';

class SendMoneyHDivider extends StatelessWidget {
  const SendM<PERSON>HDivider({
    super.key,
    this.height,
  });

  final double? height;

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(left: Sizer.width(20)),
      height: Sizer.height(height ?? 10),
      width: Sizer.width(1),
      color: AppColors.grayACB,
    );
  }
}
