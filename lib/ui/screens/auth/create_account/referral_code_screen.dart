import 'dart:io';

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReferralCodeScreen extends StatefulWidget {
  const ReferralCodeScreen({super.key});

  @override
  State<ReferralCodeScreen> createState() => _ReferralCodeScreenState();
}

class _ReferralCodeScreenState extends State<ReferralCodeScreen> {
  final FocusNode _referralFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OnBoardVM>().getDeepLinkValueFromStorage();
    });
  }

  @override
  void dispose() {
    _referralFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Referral Code',
            onBackBtnTap: () {
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Please enter a referral code if applicable ",
                          style: AppTypography.text15
                              .copyWith(color: AppColors.gray93),
                        ),
                        const YBox(40),
                        CustomTextField(
                          labelText: "Referral Code (optional)",
                          showLabelHeader: true,
                          focusNode: _referralFocusNode,
                          controller: vm.referralC,
                          borderRadius: Sizer.height(12),
                          onChanged: (val) => setState(() {}),
                          onSubmitted: (p0) {
                            _referralFocusNode.unfocus();
                          },
                        ),
                        const YBox(60),
                        Align(
                          child: InkWell(
                            onTap: () {
                              _verifyOtp(skipReferrals: true);
                            },
                            child: Text(
                              "Skip",
                              style: AppTypography.text16.medium
                                  .copyWith(color: AppColors.primaryBlue),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                CustomBtn.withChild(
                  onTap: () {
                    _verifyOtp();
                  },
                  online: vm.referralC.text.trim().isNotEmpty,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(
                      isOnline: vm.referralC.text.trim().isNotEmpty),
                ),
                const YBox(30),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _verifyOtp({bool skipReferrals = false}) {
    final onBoardVM = context.read<OnBoardVM>();

    onBoardVM.signUp(skipReferrals: skipReferrals).then((value) async {
      if (value.success) {
        // Track user registration completion
        try {
          await UnifiedAnalyticsManager.instance.trackCreateAccount(
            userId: value.data?['user']?['id']?.toString() ?? 'unknown',
            method: 'email',
            additionalParameters: {
              'platform': Platform.isIOS ? 'ios' : 'android',
              'registration_timestamp': DateTime.now().toIso8601String(),
              'has_referral_code': onBoardVM.referralC.text.trim().isNotEmpty,
            },
          );
        } catch (e) {
          printty('❌ Error tracking registration: $e');
        }

        onBoardVM.removeDeepLinkValueFromStorage();
        _moveToFreqDesc();
      } else {
        // If sign up fails, clear OTP
        _displayErrorToast(value.message ?? '');
        _clearOtp();
      }
    });
  }

  _displayErrorToast(String e) {
    FlushBarToast.fLSnackBar(
      message: e.toString(),
    );
  }

  _clearOtp() {
    context.read<OnBoardVM>().emailOtp.clear();
  }

  _moveToFreqDesc() {
    _clearOtp();
    Navigator.pushReplacementNamed(
        context, RoutePath.frequentRecipientCountryScreen);
  }
}
