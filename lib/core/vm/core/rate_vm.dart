import 'package:korrency/core/core.dart';

class RateVm extends BaseVM {
  NewCustomerRateModel? _newCustomerRateModel;
  NewCustomerRateModel? get newCustomerRateModel => _newCustomerRateModel;

  // Both has to true to show new rate modal
  bool get showNewRateModal =>
      _newCustomerRateModel?.newCustomerRateActive == true &&
      _newCustomerRateModel?.userIsEligible == true;

  RateData? get rateData => _newCustomerRateModel?.rateData;

  Future<ApiResponse> getNewCustomerRate(int fromId, int toId) async {
    return await performApiCall(
      url: "/currencies/new-customer-rate-eligibility/$fromId/$toId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _newCustomerRateModel = NewCustomerRateModel.fromJson(data["data"]);
        return apiResponse;
      },
    );
  }
}
