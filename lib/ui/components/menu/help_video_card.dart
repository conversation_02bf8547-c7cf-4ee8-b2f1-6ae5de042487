import 'dart:ui';

import 'package:korrency/core/core.dart';

class HelpVideoCard extends StatelessWidget {
  const HelpVideoCard({
    super.key,
    this.height,
    this.width,
    this.margin,
    this.onTap,
  });

  final double? height;
  final double? width;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            margin: margin,
            height: Sizer.height(height ?? 186),
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(3, 4),
                  // spreadRadius: 2,
                )
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: imageHelper(
                AppImages.edu,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 80,
            left: 60,
            child: svgHelper(
              AppSvgs.play,
              height: Sizer.height(32),
              width: Sizer.width(32),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: ClipRRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                child: Container(
                  padding: EdgeInsets.only(
                    left: Sizer.width(8),
                    right: Sizer.width(8),
                    top: Sizer.height(8),
                    bottom: Sizer.height(8),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.black1A.withOpacity(0.54),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(Sizer.width(8)),
                      bottomRight: Radius.circular(Sizer.width(8)),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Wallet",
                        style: AppTypography.text8.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        "How to Create Exchange Offers",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w500,
                          height: 1.2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
