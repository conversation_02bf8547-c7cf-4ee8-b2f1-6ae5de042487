// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class CompleteProfileAddressPageView extends StatefulWidget {
//   const CompleteProfileAddressPageView({
//     super.key,
//     required this.kycVm,
//   });

//   final KycVM kycVm;

//   @override
//   State<CompleteProfileAddressPageView> createState() =>
//       _CompleteProfileAddressPageViewState();
// }

// class _CompleteProfileAddressPageViewState
//     extends State<CompleteProfileAddressPageView> {
//   final FocusNode _addressFocusNode = FocusNode();

//   @override
//   void dispose() {
//     _addressFocusNode.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Use MediaQuery to get the screen height and subtract the keyboard height
//     final screenHeight = MediaQuery.of(context).size.height;
//     final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
//     final availableHeight = screenHeight - keyboardHeight;

//     return Consumer<AddressSuggestionVM>(builder: (context, vm, _) {
//       return SingleChildScrollView(
//         child: ConstrainedBox(
//           constraints: BoxConstraints(
//             minHeight:
//                 availableHeight, // Ensure the Column takes up the available height
//           ),
//           child: IntrinsicHeight(
//             child: Container(
//               height: Sizer.screenHeight * 0.8,
//               padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
//               // color: AppColors.white,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const AuthTextSubTitle(
//                     title: "Your Address Details",
//                     subtitle: "Let’s get to know more about you",
//                   ),
//                   const YBox(20),
//                   const InfoContainer(
//                       text:
//                           "Please enter your details as it appears on your government issued identity document."),
//                   const YBox(20),
//                   CustomTextField(
//                     labelText: "Country of Residence",
//                     showLabelHeader: true,
//                     isReadOnly: true,
//                     borderRadius: Sizer.height(4),
//                     prefixIcon: Container(
//                       padding: EdgeInsets.all(Sizer.radius(10)),
//                       child: imageHelper(
//                         AppImages.cad,
//                         height: Sizer.height(14),
//                         width: Sizer.width(20),
//                       ),
//                     ),
//                     // onTap: () => BsWrapper.bottomSheet(
//                     //   context: context,
//                     //   widget: const SelectCountrySheet(),
//                     // ),
//                     hintText: 'Canada',
//                     showSuffixIcon: true,
//                     suffixIcon: Icon(
//                       Icons.expand_more,
//                       color: AppColors.gray500,
//                       size: Sizer.height(26),
//                     ),
//                     // controller: vm.emailController,
//                     onChanged: (val) {},
//                   ),
//                   const YBox(20),
//                   CustomTextField(
//                     labelText: "Address",
//                     focusNode: _addressFocusNode,
//                     showLabelHeader: true,
//                     borderRadius: Sizer.height(4),
//                     controller: widget.kycVm.addressC,
//                     onSubmitted: (val) => _addressFocusNode.unfocus(),
//                     onChanged: (val) async {
//                       if (val.isNotEmpty) {
//                         await context
//                             .read<AddressSuggestionVM>()
//                             .getPlacePredictions(val);
//                       } else {
//                         vm.updatePredictions([]);
//                       }
//                     },
//                   ),
//                   Builder(
//                     builder: (context) {
//                       if (vm.predictionViewState == ViewState.busy) {
//                         return SizedBox(
//                           height: Sizer.height(100),
//                           child: const Center(
//                             child: CircularProgressIndicator(),
//                           ),
//                         );
//                       }
//                       if (vm.predictionViewState == ViewState.error) {
//                         return const SizedBox.shrink();
//                       }

//                       if (vm.predictions.isEmpty) {
//                         return const SizedBox.shrink();
//                       }

//                       return Container(
//                         padding: EdgeInsets.symmetric(
//                           vertical: Sizer.height(20),
//                         ),
//                         height: Sizer.height(250),
//                         child: ListView.separated(
//                           shrinkWrap: true,
//                           separatorBuilder: (context, index) => Divider(
//                             color: AppColors.gray500.withOpacity(0.2),
//                           ),
//                           itemCount: vm.predictions.length,
//                           itemBuilder: (context, index) {
//                             printty(vm.predictions.length,
//                                 level: "from listView");
//                             var prediction = vm.predictions[index];
//                             return ListTile(
//                               leading: const Icon(
//                                 Icons.location_on,
//                                 color: AppColors.blue700,
//                               ),
//                               title: Text(
//                                 prediction.description ?? "",
//                                 style: AppTypography.text14.copyWith(
//                                   color: AppColors.gray600,
//                                 ),
//                               ),
//                               onTap: () async {
//                                 widget.kycVm.addressC.text =
//                                     vm.predictions[index].description ?? '';
//                                 if (prediction.placeId != null) {
//                                   final placeDetails = await vm
//                                       .getPlaceDetails(prediction.placeId!);
//                                   printty("placeDetails: ${prediction.placeId}",
//                                       level: "placeDetails");

//                                   if (placeDetails.success &&
//                                       placeDetails.data
//                                           is List<AddressComponent>) {
//                                     final addressC = placeDetails.data
//                                         as List<AddressComponent>;
//                                     widget.kycVm.cityC.text = addressC
//                                             .firstWhere(
//                                               (component) =>
//                                                   (component.types ?? [])
//                                                       .contains('locality'),
//                                               orElse: () => AddressComponent(
//                                                   types: [],
//                                                   longName: '',
//                                                   shortName: ''),
//                                             )
//                                             .longName ??
//                                         '';
//                                     widget.kycVm.stateC.text = addressC
//                                             .firstWhere(
//                                               (component) => (component.types ??
//                                                       [])
//                                                   .contains(
//                                                       'administrative_area_level_1'),
//                                               orElse: () => AddressComponent(
//                                                   types: [],
//                                                   longName: '',
//                                                   shortName: ''),
//                                             )
//                                             .longName ??
//                                         '';
//                                     widget.kycVm.postalCodeC.text = addressC
//                                             .firstWhere(
//                                               (component) =>
//                                                   (component.types ?? [])
//                                                       .contains('postal_code'),
//                                               orElse: () => AddressComponent(
//                                                   types: [],
//                                                   longName: '',
//                                                   shortName: ''),
//                                             )
//                                             .longName ??
//                                         '';
//                                   }
//                                   vm.updatePredictions([]);
//                                   setState(() {});
//                                 }
//                                 _addressFocusNode.unfocus();
//                               },
//                             );
//                           },
//                         ),
//                       );
//                     },
//                   ),
//                   const YBox(20),
//                   CustomTextField(
//                     labelText: "State/Region/Province",
//                     showLabelHeader: true,
//                     borderRadius: Sizer.height(4),
//                     hintText: 'Ontario',
//                     isReadOnly: true,
//                     // showSuffixIcon: true,
//                     // suffixIcon: Icon(
//                     //   Icons.expand_more,
//                     //   color: AppColors.gray500,
//                     //   size: Sizer.height(26),
//                     // ),
//                     // onTap: () => BsWrapper.bottomSheet(
//                     //   context: context,
//                     //   widget: const StateProvinceSheet(),
//                     // ),
//                     controller: widget.kycVm.stateC,
//                     onChanged: (val) {},
//                   ),
//                   const YBox(20),
//                   CustomTextField(
//                     labelText: "City",
//                     showLabelHeader: true,
//                     borderRadius: Sizer.height(4),
//                     hintText: 'Toronto',
//                     controller: widget.kycVm.cityC,
//                     isReadOnly: true,
//                     // shozwSuffixIcon: true,
//                     // suffixIcon: Icon(
//                     //   Icons.expand_more,
//                     //   color: AppColors.gray500,
//                     //   size: Sizer.height(26),
//                     // ),
//                     onChanged: (val) {},
//                   ),
//                   const YBox(20),
//                   CustomTextField(
//                     labelText: "Postal Code",
//                     showLabelHeader: true,
//                     borderRadius: Sizer.height(4),
//                     hintText: 'M2N 4R5',
//                     controller: widget.kycVm.postalCodeC,
//                     isReadOnly: true,
//                     onChanged: (val) {},
//                   ),
//                   const YBox(40),
//                   CustomBtn.solid(
//                     onTap: () {
//                       widget.kycVm.kycStep3().then((value) {
//                         if (value.success) {
//                           // showToast(value.message.toString(), true);
//                           context.read<AuthUserVM>().setUser(value.data);
//                           Navigator.pop(context);
//                           widget.kycVm.clearKyc3();
//                         } else {
//                           showToast(value.message.toString(), false);
//                         }
//                       });
//                     },
//                     online:
//                         widget.kycVm.iskycStep3Active && !widget.kycVm.isBusy,
//                     text: "Continue",
//                   ),
//                   const YBox(50),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       );
//     });
//   }

//   showToast(String m, bool isSuccess) {
//     FlushBarToast.fLSnackBar(
//       message: m,
//       snackBarType: isSuccess ? SnackBarType.success : SnackBarType.warning,
//     );
//   }
// }
