import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class InviteOthersAndEarnScreen extends StatefulWidget {
  const InviteOthersAndEarnScreen({super.key});

  @override
  State<InviteOthersAndEarnScreen> createState() =>
      _InviteOthersAndEarnScreenState();
}

class _InviteOthersAndEarnScreenState extends State<InviteOthersAndEarnScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ReferralVM>()
        ..getReferralEarnings()
        ..getReferrals();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authVm = context.watch<AuthUserVM>();

    final referralVm = context.watch<ReferralVM>();
    return SizedBox(
      height: Sizer.screenHeight,
      width: Sizer.screenWidth,
      child: Busy<PERSON>verlay(
        show: referralVm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              children: [
                SizedBox(
                  height: Sizer.screenHeight * 0.48,
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Container(
                        height: Sizer.screenHeight * 0.3,
                        width: Sizer.screenWidth,
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                        ),
                        child: Column(
                          children: [
                            YBox(60),
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: EdgeInsets.only(left: Sizer.width(24)),
                                child: InkWell(
                                  onTap: () => Navigator.pop(context),
                                  child:
                                      SvgPicture.asset(AppSvgs.arrowBackCircle),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        top: Sizer.screenHeight * 0.15,
                        child: Container(
                          width: Sizer.screenWidth,
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          child: Container(
                            padding: EdgeInsets.all(Sizer.radius(16)),
                            decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(Sizer.radius(24)),
                              image: DecorationImage(
                                image: AssetImage(AppImages.referrals),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Invite your Friends',
                                  style: AppTypography.text26.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  'Share Korrency with friends, and when they send ${authVm.referralRule?.currency.symbol}${authVm.referralRule?.transactionAmount ?? "0"}+ on their first transfer, you both earn ${authVm.referralRule?.currency.symbol}${authVm.referralRule?.referredBonus ?? "0"} each',
                                  textAlign: TextAlign.center,
                                  style: AppTypography.text16.copyWith(
                                    color: AppColors.gray79,
                                  ),
                                ),
                                // Text(
                                //   'Invite a friend and family and both earn \$${configVm.referralBonusAmount} when they use Korrency',
                                //   textAlign: TextAlign.center,
                                //   style: AppTypography.text16.copyWith(
                                //     color: AppColors.gray79,
                                //   ),
                                // ),
                                const YBox(20),
                                CustomTextField(
                                  borderRadius: Sizer.height(12),
                                  controller: TextEditingController(
                                      text: !authVm.userIsVerified
                                          ? ""
                                          : authVm.user?.referralCode),
                                  isReadOnly: true,
                                  fillColor: AppColors.white,
                                  hideBorder: true,
                                  suffixIcon: !authVm.userIsVerified
                                      ? SizedBox.shrink()
                                      : Padding(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: Sizer.width(12),
                                          ),
                                          child: InkWell(
                                            onTap: () async {
                                              if (!authVm.userIsVerified) {
                                                showWarningToast(
                                                    "Please verify your identity to invite friends");
                                                return;
                                              }

                                              await Clipboard.setData(
                                                  ClipboardData(
                                                text:
                                                    authVm.user?.referralCode ??
                                                        '',
                                              ));
                                              showSuccessToastMessage(
                                                  "Referral code copied");
                                            },
                                            child:
                                                SvgPicture.asset(AppSvgs.copy),
                                          ),
                                        ),
                                ),
                                const YBox(16),
                                CustomBtn.solid(
                                  onTap: () async {
                                    var configVM = context.read<ConfigVM>();
                                    if (!authVm.userIsVerified) {
                                      showWarningToast(
                                          "Please verify your identity to invite friends");
                                      return;
                                    }
                                    await Share.share(
                                      referalShare(
                                        refCode: authVm.user?.referralCode,
                                        refLink:
                                            '${AppUtils.deeplinkUrl}/referrer/${authVm.user?.referralCode}',
                                        refBonusAmt:
                                            authVm.referralRule?.referredBonus,
                                        refamtForReferral: authVm
                                            .referralRule?.transactionAmount,
                                        symbol: authVm
                                            .referralRule?.currency.symbol,
                                      ),
                                      subject: "Korrency",
                                    );

                                    MixpanelService().track(
                                        'Referral Code Shared',
                                        properties: {
                                          "referral_code":
                                              authVm.user?.referralCode,
                                          "referral_link":
                                              '${AppUtils.deeplinkUrl}/referrer/${authVm.user?.referralCode}',
                                          "referral_amount":
                                              configVM.referralBonusAmount,
                                          "time":
                                              DateTime.now().toIso8601String()
                                        });
                                  },
                                  borderRadius:
                                      BorderRadius.circular(Sizer.radius(20)),
                                  text: "Share",
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Column(
                    children: [
                      // const YBox(20),
                      // InkWell(
                      //   onTap: () {
                      //     Navigator.pushNamed(
                      //       context,
                      //       RoutePath.createFreshDeskTicketWebview,
                      //       arguments: WebViewArg(
                      //         webURL: AppUtils.referralTermsAndCondition,
                      //       ),
                      //     );
                      //   },
                      //   child: Text(
                      //     'Terms and Conditions apply',
                      //     textAlign: TextAlign.center,
                      //     style: AppTypography.text12.copyWith(
                      //       color: AppColors.primaryBlue,
                      //     ),
                      //   ),
                      // ),
                      const YBox(20),
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.referralScreen);
                        },
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              AppSvgs.giftTrack,
                              height: Sizer.height(70),
                            ),
                            XBox(10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Track your Invites',
                                    style: AppTypography.text18.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textBlack800,
                                    ),
                                  ),
                                  Text(
                                    'See who you’ve invited and track if they’ve completed their journey',
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.gray93,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            XBox(10),
                            Icon(
                              Iconsax.arrow_right_3,
                              color: AppColors.gray93,
                            ),
                          ],
                        ),
                      ),
                      YBox(20),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(28),
                          vertical: Sizer.height(16),
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.blue5FF,
                          ),
                          borderRadius: BorderRadius.circular(Sizer.radius(6)),
                        ),
                        child: IntrinsicHeight(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Center(
                                  child: ReferralColText(
                                    title: 'Total Earned',
                                    subtitle:
                                        '${authVm.referralRule?.currency.symbol}${referralVm.refEarnings?.bonusesEarned ?? 0}',
                                  ),
                                ),
                              ),
                              VerticalDivider(
                                color: AppColors.blue5FF,
                              ),
                              Expanded(
                                child: Center(
                                  child: ReferralColText(
                                    title: 'Invited friends',
                                    subtitle: '${referralVm.allRefList.length}',
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      if (authVm.user?.hasDebitTransaction == false)
                        InkWell(
                          onTap: () async {
                            final res = await BsWrapper.showCustomDialog(
                              context,
                              child: const ReferralDialog(),
                            );

                            if (res is bool && res) {
                              BsWrapper.showCustomDialog(
                                context,
                                child: const ReferralCodeDialog(),
                              );
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.only(
                              top: Sizer.height(16),
                            ),
                            child: Row(
                              children: [
                                SvgPicture.asset(
                                  AppSvgs.thumbs,
                                  height: Sizer.height(32),
                                ),
                                XBox(12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Enter Referral code',
                                        style: AppTypography.text18.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.primaryBlue,
                                        ),
                                      ),
                                      Text(
                                        'Enter the code of the person who referred you to earn rewards',
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.gray93,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                XBox(10),
                                Icon(
                                  Iconsax.arrow_right_3,
                                  color: AppColors.gray93,
                                ),
                              ],
                            ),
                          ),
                        ),
                      YBox(20),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "Recent invites",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray79,
                          ),
                        ),
                      ),
                      YBox(16),
                      Builder(builder: (context) {
                        final recentInvites =
                            referralVm.completedRefList.take(3).toList();
                        if (recentInvites.isEmpty) {
                          return SizedBox(
                            height: Sizer.height(100),
                            child: Center(
                              child: Text(
                                "No recent invites",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.gray79,
                                ),
                              ),
                            ),
                          );
                        }
                        return ListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, i) {
                            Referral claimedRef = recentInvites[i];
                            return RefListTile(
                              name: claimedRef.referredUserFullName ?? '',
                              status: claimedRef.status ?? '',
                              date: claimedRef.createdAt,
                              onTap: () {},
                            );
                          },
                          separatorBuilder: (context, index) => const YBox(30),
                          itemCount: recentInvites.length,
                        );
                      }),
                    ],
                  ),
                ),
                YBox(50)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
