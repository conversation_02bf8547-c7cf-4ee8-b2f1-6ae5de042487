import 'package:flutter/foundation.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:korrency/core/core.dart';

class RatingService {
  static final InAppReview _inAppReview = InAppReview.instance;

  /// Request in-app review with fallback handling
  static Future<void> requestReview() async {
    try {
      // Check if in-app review is available
      if (await _inAppReview.isAvailable()) {
        printty('📱 In-app review is available');

        // Request the review
        await _inAppReview.requestReview();
        printty('✅ In-app review requested successfully');

        // In development, the review dialog won't show
        // In production (Play Store install), it will show
      } else {
        printty('❌ In-app review not available, opening store');
        await _openStoreListing();
      }
    } catch (e) {
      printty('❌ Error requesting review: $e');
      // Fallback to store listing
      await _openStoreListing();
    }
  }

  /// Open store listing as fallback
  static Future<void> _openStoreListing() async {
    try {
      await _inAppReview.openStoreListing(
        appStoreId: 'your-app-store-id', // Only needed for iOS
      );
    } catch (e) {
      printty('❌ Error opening store listing: $e');
    }
  }

  /// Test function for development - shows what would happen
  static Future<void> testReviewFlow() async {
    if (kDebugMode) {
      printty('🧪 Testing review flow...');

      final isAvailable = await _inAppReview.isAvailable();
      printty('📊 Is available: $isAvailable');

      if (isAvailable) {
        printty('🎯 Would show in-app review in production');
        // In development, just log what would happen
        printty(
            '📝 Note: Review dialog only shows when installed from Play Store');
      } else {
        printty('🔗 Would open store listing');
      }
    }

    // Still call the actual method to test the flow
    await requestReview();
  }
}
