import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';

class CopyShareBtn extends StatelessWidget {
  const CopyShareBtn({
    super.key,
    this.color,
    this.icon,
    this.title,
    this.onTap,
  });

  final Color? color;
  final IconData? icon;
  final String? title;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            title ?? 'Copy',
            style: AppTypography.text14.copyWith(
              fontWeight: FontWeight.w600,
              color: color ?? AppColors.textGray500,
            ),
          ),
          const XBox(12),
          Icon(
            icon ?? Iconsax.copy,
            size: Sizer.height(16),
            color: color ?? AppColors.textGray500,
          ),
        ],
      ),
    );
  }
}

class CopyWithIcon extends StatelessWidget {
  const CopyWithIcon({
    super.key,
    this.onPressed,
    this.margin,
    this.text,
  });

  final VoidCallback? onPressed;
  final EdgeInsetsGeometry? margin;
  final String? text;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: Sizer.width(70),
        height: Sizer.height(26),
        alignment: Alignment.center,
        margin: margin,
        decoration: BoxDecoration(
            color: AppColors.suffixBg,
            borderRadius: BorderRadius.circular(Sizer.radius(4))),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Iconsax.copy,
              size: 16,
              color: AppColors.textBlack800,
            ),
            const XBox(4),
            Text(
              text ?? "Copy",
              style: AppTypography.text12.copyWith(
                  color: AppColors.textGrey200, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}
