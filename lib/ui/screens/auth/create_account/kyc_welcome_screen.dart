import 'package:korrency/core/core.dart';

class KYCWelcomeScreen extends StatelessWidget {
  const KYCWelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: <PERSON><PERSON><PERSON>(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          child: Column(
            children: [
              YB<PERSON>(40),
              Center(
                child: Image.asset(
                  AppImages.kycWelcome,
                  height: Sizer.height(300),
                ),
              ),
              YBox(10),
              Text(
                "Welcome to Korrency",
                style: AppTypography.text26.semiBold.copyWith(
                  fontFamily: AppFont.outfit.family,
                ),
              ),
              Text(
                "Congratulations",
                style: AppTypography.text18.copyWith(
                  fontFamily: AppFont.outfit.family,
                  color: AppColors.gray79,
                ),
              ),
              Text(
                "Your account has been created successfully, finish setting up your account by completing the following:",
                textAlign: TextAlign.center,
                style: AppTypography.text14.copyWith(
                  fontFamily: AppFont.outfit.family,
                  color: AppColors.grayAB,
                ),
              ),
              YBox(26),
              _buildRowText(
                title: "Complete your profile",
                number: "1",
              ),
              YBox(16),
              _buildRowText(
                  title: "Upload your ID document", number: "2", status: 1),
              YBox(16),
              _buildRowText(
                title: "Fund your wallet",
                number: "3",
              ),
              YBox(16),
              _buildRowText(
                title: "Send money",
                number: "4",
              ),
              YBox(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pushNamedAndRemoveUntil(
                        context,
                        RoutePath.dashboardNav,
                        (route) => false,
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(20),
                        vertical: Sizer.height(10),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.primaryBlue,
                          width: Sizer.width(1.5),
                        ),
                        borderRadius: BorderRadius.circular(Sizer.radius(12)),
                      ),
                      child: Row(
                        children: [
                          Text(
                            "Do this later",
                            style: AppTypography.text14.medium.copyWith(
                              color: AppColors.primaryBlue,
                            ),
                          ),
                          XBox(Sizer.width(8)),
                          SvgPicture.asset(
                            AppSvgs.arrowRight,
                            colorFilter: ColorFilter.mode(
                              AppColors.primaryBlue,
                              BlendMode.srcIn,
                            ),
                          )
                        ],
                      ),
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRowText(
      {required String title,
      required String number,
      int status = 0 // 0 not set, 1 currently set, 2 completed
      }) {
    return Row(
      children: [
        Container(
          height: Sizer.height(28),
          width: Sizer.width(28),
          decoration: BoxDecoration(
            color: status == 1 ? AppColors.primaryBlue90 : null,
            borderRadius: BorderRadius.circular(Sizer.radius(30)),
            border: Border.all(
              color: AppColors.grayC3,
            ),
          ),
          child: Center(
            child: Text(
              number,
              style: AppTypography.text16.copyWith(
                color: status == 1 ? AppColors.white : AppColors.grayAB,
              ),
            ),
          ),
        ),
        XBox(16),
        Expanded(
          child: Text(
            title,
            style: AppTypography.text16.copyWith(
              color: AppColors.grayAB,
            ),
          ),
        ),
        if (status == 1)
          Icon(
            Iconsax.arrow_right_3,
            size: Sizer.width(20),
            color: AppColors.primaryBlue90,
          )
      ],
    );
  }
}
