import 'dart:async';

import 'package:dio/dio.dart';
import 'package:korrency/core/core.dart';

class AppInterceptors extends QueuedInterceptorsWrapper {
  Dio dio = Dio();
  CancelToken cancelToken = CancelToken();
  bool isTrustedDevice = true;

  @override
  FutureOr<dynamic> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    String? token = await StorageService.getString(StorageKey.accessToken);
    // printty("base url:===> ${options.baseUrl}");
    // printty("url path:===> ${options.path}");

    // printty("url headers:===> ${options.headers.toString()}");
    // printty(token.toString());

    if (token == null) {
      handler.reject(DioException(requestOptions: options));
      logout();
      return;
    }
    options.headers.addAll({"Accept": "application/json"});
    options.headers.addAll({"authorization": "Bearer $token"});
    handler.next(options);
    //return super.onRequest(options, handler);
  }

  @override
  FutureOr<dynamic> onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    handler.next(response);
  }

  @override
  FutureOr<dynamic> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401 || err.response?.statusCode == 403) {
      logout();
      return;
    }

    return handler.next(err);
  }

  Future<void> logout() async {
    await StorageService.logout();
    gotoNextScreen(RoutePath.korrencyWelcomeScreen);
  }

  gotoNextScreen(String route) {
    Navigator.pushNamedAndRemoveUntil(
        NavigatorKeys.appNavigatorKey.currentContext!, route, (r) => false);
  }
}
