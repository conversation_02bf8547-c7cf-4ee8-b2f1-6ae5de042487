import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AllTimeSheet extends StatefulWidget {
  const AllTimeSheet({super.key});

  @override
  State<AllTimeSheet> createState() => _AllTimeSheetState();
}

class _AllTimeSheetState extends State<AllTimeSheet> {
  DateTime _fromWhatDate = DateTime.now();
  DateTime _toWhatDate = DateTime.now();
  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Container(
        margin: EdgeInsets.all(Sizer.radius(12)),
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Select Date Range",
                  style: AppTypography.text22.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const YBox(25),
            CustomTextField(
              isReadOnly: true,
              labelText: "Date From",
              hintText: 'Select date',
              showLabelHeader: true,
              borderRadius: Sizer.height(12),
              showSuffixIcon: true,
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(10),
                ),
                child: Icon(
                  Iconsax.calendar_1,
                  color: AppColors.gray500,
                  size: Sizer.height(20),
                ),
              ),
              controller: vm.startDateFormattedC,
              onTap: () {
                showCupertinoDatePicker(
                  context,
                  onDateTimeChanged: (val) {
                    // Check if val is greater now
                    if (val.isAfter(DateTime.now())) {
                      _fromWhatDate = DateTime.now();
                      return;
                    }

                    _fromWhatDate = val;
                  },
                  onDone: () {
                    vm.startDateFormattedC.text =
                        AppUtils.dayWithSuffixMonthAndYear(_fromWhatDate);
                    vm.startDateC.text =
                        _fromWhatDate.toIso8601String().split("T").first;
                    vm.reBuildUI();
                    Navigator.pop(context);
                  },
                );
              },
              onChanged: (val) {},
            ),
            const YBox(30),
            CustomTextField(
              isReadOnly: true,
              labelText: "Date To",
              hintText: 'Select date',
              showLabelHeader: true,
              borderRadius: Sizer.height(12),
              showSuffixIcon: true,
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(10),
                ),
                child: Icon(
                  Iconsax.calendar_1,
                  color: AppColors.gray500,
                  size: Sizer.height(20),
                ),
              ),
              controller: vm.endDateFormattedC,
              onTap: () {
                showCupertinoDatePicker(
                  context,
                  minimumDate: _fromWhatDate,
                  onDateTimeChanged: (val) {
                    if (val.isAfter(DateTime.now())) {
                      _toWhatDate = DateTime.now();
                      return;
                    }
                    _toWhatDate = val;
                  },
                  onDone: () {
                    vm.endDateFormattedC.text =
                        AppUtils.dayWithSuffixMonthAndYear(_toWhatDate);
                    vm.endDateC.text =
                        _toWhatDate.toIso8601String().split("T").first;
                    vm.reBuildUI();
                    Navigator.pop(context);
                  },
                );
              },
              onChanged: (val) {},
            ),
            YBox(56),
            CustomBtn.solid(
              onTap: () {
                vm.getFilteredTransactions(
                  startDate: vm.startDateC.text,
                  endDate: vm.endDateC.text,
                );
                vm.setStatusFilter(clear: true);
                vm.setSelectedCurrency(null);
                Navigator.pop(context);
              },
              online: vm.dateBtnIsActive,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              text: "Apply Filter",
            ),
            const YBox(40),
          ],
        ),
      );
    });
  }
}
