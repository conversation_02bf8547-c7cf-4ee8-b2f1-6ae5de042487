import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ConfirmYourPinScreen extends StatefulWidget {
  const ConfirmYourPinScreen({super.key});

  @override
  State<ConfirmYourPinScreen> createState() => _ConfirmYourPinScreenState();
}

class _ConfirmYourPinScreenState extends State<ConfirmYourPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Confirm Transaction Pin',
            onBackBtnTap: () {
              Navigator.pop(context);
              vm.pinConfirmC.clear();
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            "Enter a new 4-digit PIN which would be required for all future transactions",
                        style: FontTypography.text16
                            .withCustomColor(AppColors.gray93)
                            .withCustomHeight(1.6),
                      ),
                    ],
                  ),
                ),
                const YBox(70),
                Center(
                  child: Pinput(
                    defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                    followingPinTheme: PinInputTheme.changePinTheme(),
                    focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                    submittedPinTheme: PinInputTheme.changePinTheme(),
                    length: 4,
                    controller: vm.pinConfirmC,
                    focusNode: pinFocusNode,
                    showCursor: true,
                    obscureText: true,
                    obscuringWidget: Container(
                      padding: EdgeInsets.only(top: Sizer.height(8)),
                      child: Text('*', style: AppTypography.text36),
                    ),
                    onChanged: (value) {
                      if (!vm.isPinMatched &&
                          vm.pinConfirmC.text.trim().length == 4) {
                        showWarningToast("Code doesn’t match");
                      }
                      vm.reBuildUI();
                    },
                    onCompleted: (pin) {},
                  ),
                ),
                const YBox(10),
                Visibility(
                  visible: !vm.isPinMatched &&
                      vm.pinConfirmC.text.trim().length == 4,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 1000),
                    opacity: (!vm.isPinMatched &&
                            vm.pinConfirmC.text.trim().length == 4)
                        ? 1
                        : 0,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 1000),
                      curve: Curves.easeInOutCubic,
                      alignment: Alignment.center,
                      child: Text(
                        "Code doesn’t match",
                        style: AppTypography.text10.copyWith(
                          color: AppColors.alertRed,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                CustomBtn.withChild(
                  online: vm.isPinMatched && vm.pinConfirmC.text.length == 4,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  onTap: () {
                    _setTransactionPin();
                  },
                  child: ContinueText(
                      isOnline:
                          vm.isPinMatched && vm.pinConfirmC.text.length == 4),
                ),
                const YBox(60),
              ],
            ),
          ),
        ),
      );
    });
  }

  _setTransactionPin() async {
    var vm = context.read<TransactionPinVM>();
    final pinRes = await vm.resetTransactionPin();

    handleApiResponse(
      response: pinRes,
      onSuccess: () {
        vm.clearData();
        Navigator.pop(context);
        Navigator.pop(context);
        // _openSuccessSheet();
      },
      onError: () {
        vm.clearData();
        Navigator.pop(context);
      },
    );
  }
}
