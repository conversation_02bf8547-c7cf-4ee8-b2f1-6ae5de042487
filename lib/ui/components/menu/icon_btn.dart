import 'package:korrency/core/core.dart';

class IconBtn extends StatelessWidget {
  const IconBtn({
    Key? key,
    this.onTap,
    required this.btnText,
    required this.icon,
  }) : super(key: key);

  final VoidCallback? onTap;
  final String btnText;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.width(8),
        ),
        decoration: BoxDecoration(
          color: AppColors.primaryBlue,
          borderRadius: BorderRadius.circular(40),
        ),
        child: Row(
          children: [
            Text(
              btnText,
              style: AppTypography.text12.copyWith(
                color: AppColors.bgWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
            const XBox(5),
            Icon(
              icon,
              color: AppColors.bgWhite,
              size: Sizer.radius(16),
            )
          ],
        ),
      ),
    );
  }
}
