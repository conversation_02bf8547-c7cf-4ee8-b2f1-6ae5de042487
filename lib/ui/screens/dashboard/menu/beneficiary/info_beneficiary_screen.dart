import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class InfoBeneficiaryScreen extends StatefulWidget {
  const InfoBeneficiaryScreen({super.key});

  @override
  State<InfoBeneficiaryScreen> createState() => _InfoBeneficiaryScreenState();
}

class _InfoBeneficiaryScreenState extends State<InfoBeneficiaryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Beneficiaries',
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      imageHelper(
                        AppImages.team,
                        height: Sizer.height(210),
                        width: Sizer.width(210),
                      ),
                      const YBox(10),
                      Text(
                        'Add and save all the important accounts you want to transfer to quickly.',
                        textAlign: TextAlign.center,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.textBlack600,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              CustomBtn.solid(
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.selectCountryScreen);
                },
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                online: true,
                text: "Add New Beneficiary",
              ),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}
