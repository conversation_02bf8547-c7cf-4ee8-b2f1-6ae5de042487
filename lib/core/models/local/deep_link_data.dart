import 'package:korrency/core/enums/deep_link_type.dart';

class DeepLinkTypeData {
  final DeepLinkType deeplinkType;
  final String? code;

  const DeepLinkTypeData({
    required this.deeplinkType,
    this.code,
  });
}

class DeepLinkData {
  final DeepLinkType type;
  final Map<String, dynamic> parameters;
  final String? referralCode;
  final String? targetRoute;
  final String? code;
  final Map<String, dynamic>? routeArguments;

  const DeepLinkData({
    required this.type,
    required this.parameters,
    this.referralCode,
    this.targetRoute,
    this.code,
    this.routeArguments,
  });

  @override
  String toString() {
    return 'DeepLinkData(type: $type, referralCode: $referralCode, targetRoute: $targetRoute, parameters: $parameters, code: $code, routeArguments: $routeArguments)';
  }
}
