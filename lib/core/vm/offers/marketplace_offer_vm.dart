import 'dart:convert';

import 'package:korrency/core/core.dart';

class MarketPlaceOfferVM extends BaseVM {
  final TextEditingController amtC = TextEditingController();

  String? _pin;
  String? get pin => _pin;
  Offers? _selectOffer;
  Offers? get selectOffer => _selectOffer;
  List<Offers> _buyAllOffers = [];
  List<Offers> get buyAllOffers => _buyAllOffers;
  List<Offers> _sellAllOffers = [];
  List<Offers> get sellAllOffers => _sellAllOffers;
  String _amountForThisExchange = "";
  String get amountForThisExchange =>
      AppUtils.formatAmountDoubleString(_amountForThisExchange);

  bool _amtIsGreaterThanBalance = false;
  bool get amtIsGreaterThanBalance => _amtIsGreaterThanBalance;
  bool _exchangeAmtGreaterThanWalletBalance = false;
  bool get exchangeAmtGreaterThanWalletBalance =>
      _exchangeAmtGreaterThanWalletBalance;

  int get offerId => _selectOffer?.id ?? 0;
  String get offerType => _selectOffer?.type?.toLowerCase() ?? "";
  String get offerCurrencyCode => offerType == OfferConst.sellOffer
      ? _selectOffer?.askingCurrency?.code ?? ""
      : _selectOffer?.tradingCurrency?.code ?? "";

  String get offerAmount => offerType == OfferConst.sellOffer
      ? _selectOffer?.askingAmount ?? ""
      : _selectOffer?.tradingAmount ?? "";

  String get fees =>
      "${AppUtils.formatAmountDoubleString(_selectOffer?.p2pFees ?? "0")} ${_selectOffer?.tradingCurrency?.code}";

  String get amountToReceive => offerType == OfferConst.sellOffer
      ? "${AppUtils.formatAmountDoubleString(amtC.text.trim())} ${_selectOffer?.tradingCurrency?.code}"
      : "${AppUtils.formatAmountDoubleString(amtC.text.trim())} ${_selectOffer?.askingCurrency?.code}";

  setPin(String value) {
    _pin = value;
    reBuildUI();
  }

  Future<ApiResponse> viewOffer(int id) async {
    return await performApiCall(
      url: "/marketplace/offers/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var offer = Offers.fromJson(data["data"]);
        _selectOffer = offer;
        return ApiResponse(success: true, data: offer);
      },
    );
  }

  Future<ApiResponse> getAllOffers() async {
    return await performApiCall(
      url: "/marketplace/offers",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var offers = offersFromJson(json.encode(data["datatable"]["data"]));
        _buyAllOffers = offers
            .where((element) => element.type?.toLowerCase() == "buy offer")
            .toList();
        _sellAllOffers = offers
            .where((element) => element.type?.toLowerCase() == "sell offer")
            .toList();
        return apiResponse;
      },
    );
  }

  void offerAmountForExchange(String amount, Offers offer,
      [String? walletBalance]) {
    printty("amount: $amount, offer: $offer, walletBalance $walletBalance",
        level: "offerAmountForExchange");
    if (amount.isEmpty) {
      _amountForThisExchange = "";
      reBuildUI();
      return;
    }
    var offerType = offer.type?.toLowerCase();
    var offerBalance = double.parse(offer.balance ?? "0").toInt();
    var amountInt = double.parse(amount).toInt();
    var rateInt = double.parse(offer.rateValue ?? "0").toInt();
    if (offerType == "sell offer") {
      amtC.text = amountInt.toString();
      _amountForThisExchange = (amountInt * rateInt).toString();
    } else {
      amtC.text = amountInt.toString();
      _amountForThisExchange = amount;
    }

    // TODO: Come back to this
    if (amountInt > (double.parse(offer.balance ?? "0").toInt())) {
      _amtIsGreaterThanBalance = true;
      if (offerType == "sell offer") {
        amtC.text = offerBalance.toString();
        _amountForThisExchange = (offerBalance * rateInt).toString();
      } else {
        amtC.text = offerBalance.toString();
        _amountForThisExchange = offerBalance.toString();
      }
    }

    _exchangeAmtGreaterThanWalletBalance =
        double.parse(_amountForThisExchange) >
            double.parse(walletBalance ?? "0");

    reBuildUI();
  }

  bool get isBtnActive =>
      !_exchangeAmtGreaterThanWalletBalance &&
      _amountForThisExchange.isNotEmpty &&
      amtC.text.isNotEmpty;

  Future<ApiResponse> acceptOffer() async {
    var body = {
      "amount": amtC.text.trim(),
      "pin": _pin,
    };

    printty("body: $body id: $offerId", level: "Accept Offer");
    return await performApiCall(
      url: "/marketplace/offers/$offerId/accept",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        // _createdOffer = Offers.fromJson(data["data"]);
        printty(data, level: "Accept Offer");
        printty(apiResponse, level: "Accept Offer apiResponse");
        return apiResponse;
      },
    );
  }

  clearData() {
    _buyAllOffers.clear();
    _sellAllOffers.clear();
    _selectOffer = null;
    _amountForThisExchange = "";
    _amtIsGreaterThanBalance = false;
    _exchangeAmtGreaterThanWalletBalance = false;
    amtC.clear();

    reBuildUI();
  }

  @override
  void dispose() {
    printty("MarketPlaceOfferVM disposed");

    amtC.dispose();
    super.dispose();
  }
}
