import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class HelpVideoScreen extends StatelessWidget {
  const HelpVideoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CustomAppbar(
        showHeaderTitle: true,
        headerText: 'Educational',
        rightWidget: Icon(
          Iconsax.search_normal_1,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const YBox(30),
            Text(
              'Explore our curated resources',
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.text3E,
              ),
            ),
            const YBox(30),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              // padding: EdgeInsets.zero,
              mainAxisSpacing: Sizer.height(16),
              crossAxisSpacing: Sizer.width(20),
              childAspectRatio: 0.75,
              children: [
                for (int i = 0; i < 4; i++) const HelpVideoCard(),
              ],
            )
          ],
        ),
      ),
    );
  }
}
