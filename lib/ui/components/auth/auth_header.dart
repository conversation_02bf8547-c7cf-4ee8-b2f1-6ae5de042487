import 'package:korrency/core/core.dart';

class AuthHeader extends StatelessWidget {
  const AuthHeader({
    super.key,
    this.showBackBtn = false,
    this.onTap,
  });

  final bool showBackBtn;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: Sizer.height(10)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (showBackBtn) BackPop(onTap: onTap),
          Expanded(
            child: Container(
              padding:
                  EdgeInsets.only(right: Sizer.width(showBackBtn ? 60 : 0)),
              child: Center(
                child: svgHelper(
                  AppSvgs.korrencyBlack,
                  height: Sizer.height(24),
                  width: Sizer.width(111),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BackPop extends StatelessWidget {
  const BackPop({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ??
          () {
            Navigator.pop(context);
          },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.arrow_back_ios,
            size: Sizer.width(17),
          ),
          // const XBox(4),
          Text(
            "Back",
            style: AppTypography.text16.copyWith(
                color: AppColors.black900, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}
