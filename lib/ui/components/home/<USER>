import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/auth.dart';

class HomeLowerPart extends StatelessWidget {
  const HomeLowerPart({
    super.key,
    this.pullToRefresh,
  });

  final VoidCallback? pullToRefresh;

  @override
  Widget build(BuildContext context) {
    var authUserVM = context.watch<AuthUserVM>();
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return ContainerWithTopBorderRadius(
        padding: EdgeInsets.zero,
        child: RefreshIndicator(
          onRefresh: () async {
            if (pullToRefresh != null) {
              pullToRefresh!();
            }
          },
          child: ListView(
            shrinkWrap: true,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              bottom: Sizer.height(50),
            ),
            children: [
              Visibility(
                  visible: authUserVM.user?.status ==
                      UserVerificationStatus.processing,
                  child: InfoContainer(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      text:
                          "Your ID verification is in progress. You will be notified when you have been verified")),
              const YBox(20),
              Skeletonizer(
                enabled: vm.busy(TransactionState.all) || authUserVM.isBusy,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Recent Transactions",
                        style: AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          // BsWrapper.bottomSheet(
                          //   context: context,
                          //   color: AppColors.transparent,
                          //   widget: const CompleteKycScreen(),
                          // );
                        },
                        child: InkWell(
                          onTap: () =>
                              context.read<DashboardVM>().changeScreenIndex(1),
                          child: Text(
                            "View all",
                            style: AppTypography.text14.copyWith(
                              color: AppColors.gray500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const YBox(25),
              Builder(
                builder: (ctx) {
                  if (vm.busy(TransactionState.all) || authUserVM.isBusy) {
                    return Skeletonizer(
                      child: ListView.separated(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(24),
                        ),
                        itemBuilder: (ctx, i) {
                          return TransactionCard(
                            type: 'credit',
                            category: 'Food',
                            isCredit: true,
                            title: 'Lunch',
                            subTitle: 'Jan 25, 2024 | 14:34',
                            amount: '₦ 2,000',
                            onTap: () {},
                          );
                        },
                        separatorBuilder: (ctx, _) => const HDivider(),
                        itemCount: 3,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                      ),
                    );
                  }
                  return vm.recentTransactions.isEmpty
                      ? SizedBox(
                          height: Sizer.height(150),
                          child: Center(
                            child: Text(
                              "No recent transactions",
                              style: AppTypography.text16.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.gray500),
                            ),
                          ),
                        )
                      : ListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (ctx, i) {
                            Transaction transaction = vm.recentTransactions[i];
                            return TransactionCard(
                              type: transaction.type!,
                              category: transaction.category ?? "",
                              isCredit: transaction.type == 'credit',
                              title: transaction.description ?? "",
                              subTitle: AppUtils.formatDateTime(
                                  (transaction.createdAt ?? DateTime.now())
                                      .toLocal()
                                      .toString()),
                              // subTitle: "Jan 25, 2024 | 14:34",
                              amount:
                                  "${AppUtils.formatAmountDoubleString(transaction.amount ?? "")} ${transaction.currency?.code ?? ""}",
                              onTap: () {
                                Navigator.of(context).pushNamed(
                                    RoutePath.transactionDetailsScreen,
                                    arguments: TransactionArg(
                                        transaction: transaction));
                              },
                            );
                          },
                          separatorBuilder: (ctx, _) => const YBox(20),
                          itemCount: vm.recentTransactions.length,
                        );
                },
              ),
              const YBox(30),
              Skeletonizer(
                enabled: vm.busy(TransactionState.all) || authUserVM.isBusy,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Text(
                    "More Actions",
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const YBox(25),
              Builder(builder: (context) {
                if (vm.busy(TransactionState.all) || authUserVM.isBusy) {
                  return Skeletonizer(
                    child: SizedBox(
                      height: Sizer.height(50),
                      child: ListView.separated(
                          padding: EdgeInsets.only(
                            left: Sizer.width(24),
                          ),
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          itemBuilder: (ctx, i) {
                            return CurrencyCard(
                              svgPath: AppSvgs.trendUp,
                              title: "Exchange Rate",
                              onTap: () {},
                            );
                          },
                          separatorBuilder: (ctx, _) => const XBox(30),
                          itemCount: 5),
                    ),
                  );
                }
                return SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: Sizer.width(24),
                    ),
                    child: Row(
                      children: [
                        CurrencyCard(
                          svgPath: AppSvgs.trendUp,
                          title: "Exchange Rate",
                          onTap: () {
                            if (authUserVM.statusProcessing) {
                              FlushBarToast.fLSnackBar(
                                message: 'Your ID verification is in progress',
                                snackBarType: SnackBarType.success,
                              );
                              return;
                            }
                            Navigator.pushNamed(
                                context, RoutePath.exchangeRateScreen);
                          },
                        ),
                        // CurrencyCard(
                        //   width: Sizer.width(90),
                        //   svgPath: AppSvgs.bankNote,
                        //   title: "My Offers",
                        //   onTap: () {
                        //     context.read<DashboardVM>().changeScreenIndex(5);
                        //   },
                        // ),
                        CurrencyCard(
                          width: Sizer.width(90),
                          svgPath: AppSvgs.currencyExchange,
                          title: "Convert",
                          onTap: () {
                            if (authUserVM.statusProcessing) {
                              FlushBarToast.fLSnackBar(
                                message: 'Your ID verification is in progress',
                                snackBarType: SnackBarType.success,
                              );
                              return;
                            }
                            if (authUserVM.secQuestCheck) {
                              Navigator.pushNamed(
                                context,
                                RoutePath.securityQuestionScreen,
                              );
                              return;
                            }
                            if (authUserVM.transactionPinCheck) {
                              BsWrapper.bottomSheet(
                                context: context,
                                canDismiss: false,
                                widget: const SetupPinScreen(),
                              );

                              return;
                            }

                            if (context.read<WalletVM>().walletList.length <
                                2) {
                              BsWrapper.bottomSheet(
                                context: context,
                                widget: ConfirmationSheet(
                                  // title: 'Warning',
                                  message:
                                      "You need at least two wallets to convert currency",
                                  firstBtnText: "Create Wallet",
                                  firstBtnTap: () {
                                    Navigator.pop(context);
                                    BsWrapper.bottomSheet(
                                      context: context,
                                      widget: const AllWalletsSheet(),
                                    );
                                  },
                                ),
                              );

                              return;
                            }
                            Navigator.pushNamed(
                                context, RoutePath.convertCurrencyScreen);
                          },
                        ),
                        Consumer<BeneficiaryVM>(builder: (context, beneVM, _) {
                          return CurrencyCard(
                            width: Sizer.width(90),
                            svgPath: AppSvgs.users,
                            title: "Beneficiaries",
                            onTap: () {
                              // if (!authUserVM.userIsVerified) {
                              //   BsWrapper.bottomSheet(
                              //     canDismiss: false,
                              //     context: context,
                              //     widget: const CompleteKycScreen(),
                              //   );

                              //   return;
                              // }
                              Navigator.pushNamed(
                                  context, RoutePath.beneficiaryScreen);
                            },
                          );
                        }),
                      ],
                    ),
                  ),
                );
              })
            ],
          ),
        ),
      );
    });
  }
}
