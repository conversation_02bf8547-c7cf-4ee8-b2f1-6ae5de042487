import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/menu/offer_market_details_card.dart';

class BuyOfferListTab extends StatelessWidget {
  const BuyOfferListTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<MarketPlaceOfferVM>(builder: (context, vm, _) {
      return ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.only(
          top: Sizer.height(20),
          bottom: Sizer.height(100),
        ),
        itemBuilder: (context, i) {
          var offer = vm.buyAllOffers[i];
          return OfferMarketDetailsCard(
            onTap: () {
              Navigator.of(context).pushNamed(RoutePath.offerDetailsScreen,
                  arguments: OfferDetailArg(offer.id ?? 0));
            },
            title1:
                "\$${AppUtils.formatAmountDoubleString(offer.tradingAmount ?? "0")}",
            title2: offer.askingCurrency?.code ?? "",
            rate: "${offer.rate}",
            isCompleted: offer.status == MarketOffer.completed,
            expiresAt: offer.expiresAt,
            createAt: offer.createdAt,
          );
        },
        separatorBuilder: (ctx, _) => const YBox(20),
        itemCount: vm.buyAllOffers.length,
      );
    });
  }
}
