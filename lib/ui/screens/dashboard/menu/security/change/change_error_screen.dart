import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ChangeErrorScreen extends StatelessWidget {
  const ChangeErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(36)),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                AppSvgs.emailError,
                height: Sizer.height(292),
              ),
              // YBox(36),
              Text(
                'Uh Oh!',
                style: FontTypography.text26.semiBold,
              ),
              YBox(4),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: 'Attempt unsuccessful!',
                      style: FontTypography.text16.semiBold
                          .withCustomColor(AppColors.gray79)
                          .withCustomHeight(1.6),
                    ),
                    TextSpan(
                      text:
                          '\nSorry we are unable to change your email at this time, please try later',
                      style: FontTypography.text16
                          .withCustomColor(AppColors.grayAB)
                          .withCustomHeight(1.5),
                    ),
                  ],
                ),
              ),
              YBox(40),
              CustomBtn.solid(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                text: "Back to Settings",
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
