import 'package:korrency/core/core.dart';
import 'package:korrency/ui/screens/screens.dart';

class NavigateExchangeOffers extends StatelessWidget {
  const NavigateExchangeOffers({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<DashboardVM>(
      builder: (context, vm, _) {
        return vm.hasSeenOfferHowitWorksScreen
            ? const CreateOfferScreen()
            : const HowItWorksScreen();
      },
    );
  }
}
