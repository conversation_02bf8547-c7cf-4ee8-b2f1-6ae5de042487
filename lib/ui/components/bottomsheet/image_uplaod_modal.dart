import 'package:korrency/core/core.dart';

class ImageUploadModal extends StatefulWidget {
  const ImageUploadModal({super.key});

  @override
  State<ImageUploadModal> createState() => _ImageUploadModalState();
}

class _ImageUploadModalState extends State<ImageUploadModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                YBox(20),
                Text(
                  "Select an Option",
                  style: AppTypography.text18.copyWith(
                    color: AppColors.mainBlack,
                  ),
                ),
                YBox(36),
                _buildListTile(
                  icon: AppSvgs.gallery,
                  title: "Pick from Gallery",
                  onTap: () {
                    Navigator.pop(context, ImageUploadType.gallary);
                  },
                ),
                YBox(20),
                _buildListTile(
                  icon: AppSvgs.userSquare,
                  title: "Choose an Avatar",
                  onTap: () {
                    Navigator.pop(context, ImageUploadType.avatar);
                  },
                ),
                YBox(70),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListTile({
    required String icon,
    required String title,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(12)),
            decoration: BoxDecoration(color: AppColors.blue100),
            child: SvgPicture.asset(
              icon,
              height: Sizer.height(20),
              // colorFilter:
              //     ColorFilter.mode(AppColors.lightBlue, BlendMode.srcIn),
            ),
          ),
          XBox(16),
          Expanded(
            child: Text(
              title,
              style: AppTypography.text14.copyWith(
                color: AppColors.primaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
