import 'dart:async';

import 'package:korrency/core/core.dart';

class InactivityVM extends ChangeNotifier {
  Timer? _timer;
  bool _userHasLoggedIn = false;
  bool _pauseAction = false;
  bool get pauseAction => _pauseAction;
  Duration? _timeDuration;

  setPauseAction(bool val) {
    _pauseAction = val;
    notifyListeners();
  }

  setUserHasLoggedIn(bool val) {
    _userHasLoggedIn = val;
    notifyListeners();
  }

  setTimeDuration(dynamic val) {
    _timeDuration = val;
  }

  void startTimer() {
    // printty("start timer");
    if (_timer != null) _timer?.cancel();
    _timer = Timer(_timeDuration!, _onIdle);
  }

  isActive() async {
    // printty("isActive: ${await _userIsLoggedIn()}");
    if (await _userIsLoggedIn()) {
      if (_timer != null && !_timer!.isActive) {
        _onIdle();
      } else {
        startTimer();
      }
    }
  }

  void _onIdle() async {
    if (_pauseAction) return;
    printty("Idle: logout ================");
    if (await _userIsLoggedIn() && !_pauseAction) {
      _pauseAction = true;
      _userHasLoggedIn = false;
      Navigator.pushNamed(NavigatorKeys.appNavigatorKey.currentContext!,
          RoutePath.welcomeBackScreen,
          arguments: false);
    }
  }

  void onActivity() async {
    if (await _userIsLoggedIn()) {
      // printty("onactivity: started timer");
      startTimer();
    } else {
      // printty("onactivity: timer cancelled");
      if (_timer != null) _timer?.cancel();
    }
  }

  resetValues() async {
    _timer = null;
  }

  Future<bool> _userIsLoggedIn() async {
    AuthUser? user = await StorageService.getUser();
    return user != null && user.hasTransactionPin == true && _userHasLoggedIn;
  }
}
