<svg width="36" height="32" viewBox="0 0 36 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_963_74684)">
<path d="M4.46248 11.4624C4.73767 10.3626 5.30737 8.91128 6.01231 7.99405C6.71725 7.07683 7.60886 6.2985 8.63623 5.7035C9.66359 5.1085 10.8066 4.70849 12 4.5263C13.1934 4.34411 14.4138 4.38331 15.5915 4.64167C16.7692 4.90002 17.7775 5.49655 18.7602 6.15612C19.5821 6.83515 20.2552 7.62402 20.6207 8.63041C20.9862 9.63681 21.1457 10.5655 20.9258 11.8322C20.706 13.0989 20.4033 13.4631 19.7948 14.3984C19.9892 13.6216 20.0166 12.8253 19.8786 12.0422C19.7406 11.2591 19.4389 10.5089 18.9907 9.83434C18.5425 9.15982 18.0503 8.55649 17.36 8.09324C16.6698 7.62999 15.9243 7.24328 15.117 6.99559C14.3021 6.74559 13.5704 6.6207 12.6534 6.62673C11.7364 6.63275 10.9086 6.89175 10.0201 7.08869C9.29376 7.24968 8.61364 7.52605 7.81855 7.95944C6.97975 8.41665 4.68609 10.6921 4.46248 11.4624Z" fill="url(#paint0_linear_963_74684)"/>
</g>
<g filter="url(#filter1_dii_963_74684)">
<path d="M6.51417 23.9534C5.6982 23.1214 4.7377 21.8636 4.3248 20.8017C3.91191 19.7399 3.72669 18.6118 3.77972 17.4818C3.83275 16.3517 4.12299 15.242 4.63388 14.2159C5.14477 13.1897 5.86629 12.2673 6.75726 11.5012C7.64822 10.7352 8.7423 10.2907 9.8777 9.90132C10.9398 9.64741 12.0149 9.57057 13.1091 9.85413C14.2032 10.1377 15.1128 10.537 16.1072 11.4202C17.1017 12.3033 17.2528 12.7425 17.7359 13.7399C17.1597 13.1523 16.4721 12.6828 15.7032 12.3488C14.9342 12.0148 14.1021 11.8261 13.2544 11.7933C12.4067 11.7605 11.597 11.8018 10.7996 12.0753C10.0021 12.3487 9.24115 12.708 8.56808 13.1934C7.88876 13.6833 7.36667 14.1793 6.85582 14.8924C6.34496 15.6055 6.10768 16.3919 5.78142 17.1904C5.51472 17.8432 5.37587 18.5251 5.31092 19.3844C5.24239 20.2909 5.96006 23.3468 6.51417 23.9534Z" fill="#919094"/>
</g>
<g filter="url(#filter2_dii_963_74684)">
<path d="M19.1013 29.4301C17.9133 29.6268 16.2642 29.7085 15.0861 29.4634C13.9079 29.2182 12.7925 28.7577 11.8036 28.1081C10.8147 27.4585 9.97159 26.6325 9.32243 25.6774C8.67327 24.7222 8.23078 23.6566 8.02021 22.5412C7.80965 21.4259 7.99782 20.3232 8.25791 19.2176C8.60049 18.2426 9.10955 17.3526 9.95333 16.6414C10.7971 15.9302 11.6458 15.427 12.9758 15.1169C14.3058 14.8069 14.7826 14.9238 15.9404 15.0789C15.1014 15.2177 14.3089 15.508 13.5946 15.9353C12.8804 16.3627 12.2629 16.918 11.7775 17.5696C11.292 18.2212 10.8938 18.8825 10.7111 19.659C10.5285 20.4356 10.4427 21.2295 10.5177 22.0219C10.5935 22.8216 10.7593 23.5005 11.1266 24.2873C11.4939 25.0741 12.0744 25.6843 12.6179 26.3713C13.0622 26.9329 13.6015 27.4092 14.3402 27.9227C15.1196 28.4643 18.257 29.5413 19.1013 29.4301Z" fill="#919094"/>
</g>
<g filter="url(#filter3_dii_963_74684)">
<path d="M30.8258 20.3474C30.5764 21.4526 30.0408 22.9152 29.3574 23.8467C28.6741 24.7781 27.8009 25.5746 26.7877 26.1905C25.7745 26.8064 24.6411 27.2297 23.4523 27.4364C22.2634 27.643 21.0424 27.6288 19.8589 27.3946C18.6754 27.1605 17.6535 26.5848 16.6556 25.9455C15.8179 25.2835 15.1266 24.5086 14.7377 23.5099C14.3487 22.5113 14.1676 21.5861 14.3577 20.3152C14.5479 19.0442 14.842 18.6739 15.4285 17.7265C15.2523 18.507 15.2435 19.3037 15.3998 20.0838C15.556 20.8639 15.8752 21.6077 16.3391 22.2729C16.803 22.9381 17.3092 23.5312 18.0101 23.9801C18.711 24.4291 19.4654 24.8005 20.2783 25.0316C21.0988 25.2648 21.8332 25.3746 22.7499 25.3498C23.6666 25.325 24.4881 25.0491 25.3718 24.834C26.0942 24.6581 26.7677 24.3679 27.5524 23.9183C28.3803 23.444 30.6202 21.1221 30.8258 20.3474Z" fill="#919094"/>
</g>
<g filter="url(#filter4_dii_963_74684)">
<path d="M28.455 7.52218C29.29 8.33746 30.2794 9.57548 30.7168 10.6287C31.1542 11.6819 31.3654 12.806 31.3386 13.9368C31.3117 15.0677 31.0472 16.183 30.5602 17.2193C30.0731 18.2556 29.3731 19.1924 28.5001 19.9764C27.627 20.7603 26.5435 21.2269 25.4174 21.6392C24.3615 21.9146 23.2883 22.0132 22.1878 21.7519C21.0874 21.4906 20.1688 21.1098 19.1542 20.2471C18.1395 19.3843 17.9782 18.9482 17.4722 17.9609C18.0619 18.5367 18.7602 18.9921 19.5367 19.3104C20.3132 19.6288 21.1494 19.8006 21.9977 19.8162C22.846 19.8317 23.6545 19.774 24.4454 19.4844C25.2364 19.1949 25.9888 18.8202 26.6505 18.3213C27.3184 17.8177 27.8289 17.3113 28.3231 16.588C28.8173 15.8647 29.0364 15.0736 29.3441 14.2687C29.5956 13.6107 29.7186 12.9261 29.7637 12.0657C29.8112 11.158 29.023 8.11743 28.455 7.52218Z" fill="#919094"/>
</g>
<g filter="url(#filter5_dii_963_74684)">
<path d="M14.7988 2.73595C15.9483 2.39327 17.5712 2.10711 18.7737 2.20352C19.9761 2.29992 21.147 2.61761 22.2193 3.13842C23.2916 3.65924 24.2445 4.37299 25.0234 5.23893C25.8023 6.10487 26.3921 7.10604 26.7591 8.18527C27.126 9.26449 27.096 10.3808 26.9951 11.5091C26.7939 12.518 26.4157 13.4635 25.6803 14.2733C24.9449 15.0832 24.1751 15.6875 22.9009 16.1603C21.6267 16.633 21.1375 16.5764 19.968 16.5668C20.7798 16.3248 21.5241 15.9385 22.1714 15.4261C22.8187 14.9136 23.3519 14.2864 23.7406 13.5802C24.1293 12.8741 24.4301 12.1691 24.501 11.3767C24.5718 10.5843 24.5442 9.7867 24.3574 9.0107C24.1688 8.22749 23.9082 7.57525 23.4325 6.84114C22.9567 6.10703 22.2949 5.5744 21.6587 4.96111C21.1387 4.45978 20.5366 4.05474 19.7315 3.63778C18.8822 3.1979 15.6197 2.52076 14.7988 2.73595Z" fill="#919094"/>
</g>
<defs>
<filter id="filter0_dii_963_74684" x="2.96482" y="2.91599" width="20.1579" height="13.5791" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<filter id="filter1_dii_963_74684" x="2.27317" y="8.17576" width="17.5593" height="17.8745" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<filter id="filter2_dii_963_74684" x="6.43186" y="13.4248" width="14.7661" height="18.2871" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<filter id="filter3_dii_963_74684" x="12.7842" y="16.229" width="20.1381" height="13.4492" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<filter id="filter4_dii_963_74684" x="15.9746" y="6.02439" width="17.4629" height="17.9765" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<filter id="filter5_dii_963_74684" x="13.3012" y="0.686986" width="15.8569" height="17.9834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74684"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74684" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74684"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74684" result="effect3_innerShadow_963_74684"/>
</filter>
<linearGradient id="paint0_linear_963_74684" x1="21.2777" y1="10.5201" x2="5.67511" y2="6.61607" gradientUnits="userSpaceOnUse">
<stop stop-color="#1384B4"/>
<stop offset="1" stop-color="#30A2D2"/>
</linearGradient>
</defs>
</svg>
