import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SheetComfirmation extends StatelessWidget {
  const SheetComfirmation({super.key});

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          imageHelper(
            AppImages.success,
            height: Sizer.height(190),
            width: Sizer.width(190),
          ),
          const YBox(16),
          Text(
            'Beneficiary Added',
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(47),
          CustomBtn.solid(
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(
                context,
                RoutePath.sendMoneyScreen,
              );
            },
            online: true,
            text: "Continue",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
