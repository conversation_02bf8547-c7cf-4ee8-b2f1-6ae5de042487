import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SetRateAlertScreen extends StatelessWidget {
  const SetRateAlertScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "Set Rate Alerts",
                subtitle:
                    "Receive alerts for offers matching your target rate on the marketplace",
              ),
              const YBox(40),
              Container(
                alignment: Alignment.center,
                child: imageHelper(
                  AppImages.rateAlert,
                  height: Sizer.height(249),
                ),
              ),
              const Spacer(),
              const TextPoints(
                text:
                    "You will receive notification(s) when an offer on the marketplace reaches your set target rate and/or amount",
              ),
              const YBox(5),
              const TextPoints(
                text: "You can create a maximum of 5 alerts.",
              ),
              const YBox(5),
              const TextPoints(
                text: "Alerts will be deleted automatically after 30 days.",
              ),
              const YBox(45),
              CustomBtn.solid(
                height: 56,
                onTap: () {
                  Navigator.of(context).pushNamed(RoutePath.createAlertScreen);
                },
                // online: vm.btnIsValid(),
                text: "Add Alert",
              ),
              const YBox(40),
            ],
          ),
        ),
      ),
    );
  }
}

class TextPoints extends StatelessWidget {
  const TextPoints({
    Key? key,
    required this.text,
  }) : super(key: key);

  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: Sizer.width(6),
          height: Sizer.height(6),
          margin: EdgeInsets.only(
            top: Sizer.height(8),
            right: Sizer.width(10),
          ),
          decoration: BoxDecoration(
            color: AppColors.gray700,
            borderRadius: BorderRadius.circular(100),
          ),
        ),
        Expanded(
          child: Text(
            text,
            style: AppTypography.text14.copyWith(
              color: AppColors.gray700,
            ),
          ),
        ),
      ],
    );
  }
}
