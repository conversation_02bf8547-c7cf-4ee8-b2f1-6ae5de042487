import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class OfferTransactionStatusScreen extends StatelessWidget {
  const OfferTransactionStatusScreen({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            children: [
              const YBox(20),
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              imageHelper(
                AppImages.success,
                height: Sizer.height(200),
                width: Sizer.width(200),
              ),
              const YBox(24),
              Text(
                'Offer Posted Successfully',
                style: AppTypography.text20.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(16),
              Text(
                'Funds are securely reserved until',
                style: AppTypography.text16.copyWith(
                  color: AppColors.textBlack800,
                ),
              ),
              Text(
                'the offer is accepted or expires',
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textBlue500,
                ),
              ),
              const YBox(50),
              ContainerWithBluewishBg(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'Offers are active for 24 hrs\n',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.primaryBlue,
                              fontFamily: 'Inter',
                            ),
                          ),
                          TextSpan(
                            text: 'Adjust ',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.w700,
                              fontFamily: 'Inter',
                            ),
                          ),
                          TextSpan(
                            text: 'to a shorter time (Optional)',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.primaryBlue,
                              fontFamily: 'Inter',
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        BsWrapper.bottomSheet(
                            context: context, widget: const AdjustTimeSheet());
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                          vertical: Sizer.height(4),
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Text(
                          "Adjust",
                          style: AppTypography.text12.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              const Spacer(),
              CustomBtn.withChild(
                onTap: () async {
                  await Share.share(
                      "Hello, I'm using Korrency, a fast and secure way to send money to your loved ones. Sign up with my link and get a free transfer.",
                      subject: "Korrency");
                },
                online: true,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Share',
                      style: AppTypography.text18.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.white,
                      ),
                    ),
                    const XBox(8),
                    const Icon(
                      Icons.share,
                      color: AppColors.white,
                      size: 20,
                    ),
                  ],
                ),
              ),
              const YBox(16),
              CustomBtn.solid(
                onTap: () {
                  Navigator.pushNamedAndRemoveUntil(
                      context, RoutePath.dashboardNav, (route) => false);
                },
                online: true,
                onlineColor: AppColors.userBg,
                textColor: AppColors.primaryBlue,
                text: "Home",
              ),
              const YBox(30),
            ],
          ),
        ),
      ),
    );
  }
}
