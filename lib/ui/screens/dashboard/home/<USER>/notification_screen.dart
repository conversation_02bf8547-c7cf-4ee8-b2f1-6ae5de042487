import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/dashboard.dart';

import 'widget/notification_header.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    MixpanelService().trackScreen("In-App Message Page Viewed");
    _addScrollListener();
    _getNotifcations();

    super.initState();
  }

  void _addScrollListener() {
    printty("Adding scroll listener");
    var notifVM = context.read<NotificationVM>();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (!notifVM.gettingMore && notifVM.hasMore) {
          printty("Getting more notifications");
          notifVM.getPaginatedNotifications();
        }
      }
    });
  }

  void _getNotifcations() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationVM>().getNotifications();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    printty('hasMore ${context.watch<NotificationVM>().hasMore}');
    printty('Gettingmore ${context.watch<NotificationVM>().gettingMore}');
    return Consumer<NotificationVM>(
      builder: (context, vm, _) {
        return Scaffold(
          body: SafeArea(
            bottom: false,
            child: RefreshIndicator(
              onRefresh: () async {
                _getNotifcations();
              },
              child: Column(
                children: [
                  const YBox(20),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ),
                    child: CustomHeader(
                      // showBackBtn: true,
                      showHeader: true,
                      isHeaderText: false,
                      headerWidget: NotificationHeader(
                        length: vm.totalUnreadNotifications.toString(),
                      ),
                    ),
                  ),
                  // const YBox(10),
                  Expanded(
                    child: ListView(
                      controller: _scrollController,
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                        vertical: Sizer.width(20),
                      ),
                      shrinkWrap: true,
                      children: [
                        Builder(builder: (context) {
                          if (vm.busy(LoadState.all)) {
                            return const NotiShimmer();
                          }
                          if (vm.allNotifications.isEmpty && !vm.isBusy) {
                            return SizedBox(
                              height: Sizer.height(380),
                              child: Center(
                                child: Text(
                                  "No notifications yet",
                                  style: AppTypography.text16.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.gray500),
                                ),
                              ),
                            );
                          }

                          return ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (ctx, i) {
                              var notification = vm.allNotifications[i];
                              return NotificationCard(
                                isRead: notification.readAt != null,
                                title: notification.subject ?? '',
                                subTitle: notification.message ?? '',
                                date: AppUtils.formatDateTime(
                                    (notification.createdAt ?? DateTime.now())
                                        .toLocal()
                                        .toString()),
                                // date: 'Feb 9, 2024 | 16:46',
                                onTap: () {
                                  printty(
                                      "IsRead ${notification.readAt != null}");
                                  printty(
                                      "IsRead State ${notification.readAt}");
                                  vm.viewNotifications(notification.id!);
                                  BsWrapper.bottomSheet(
                                    context: context,
                                    widget: NotificationDetailSheet(
                                      title: notification.subject ?? "",
                                      date: AppUtils.formatDateTime(
                                          notification.createdAt.toString()),
                                      description: notification.message ?? "",
                                    ),
                                  );
                                  MixpanelService().track(
                                      "In-App Message Viewed",
                                      properties: {
                                        "notification_id": notification.id,
                                        "title": notification.subject,
                                        "time": DateTime.now().toIso8601String()
                                      });
                                },
                              );
                            },
                            separatorBuilder: (ctx, _) => const HDivider(),
                            itemCount: vm.allNotifications.length,
                          );
                        }),
                        if (vm.gettingMore) const NotiShimmer(itemCount: 2),
                        const YBox(150)
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class NotiShimmer extends StatelessWidget {
  const NotiShimmer({
    super.key,
    this.itemCount,
  });

  final int? itemCount;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: ListView.separated(
        itemBuilder: (ctx, i) {
          return NotificationCard(
            isRead: true,
            // icon: AppSvgs.creditKorrency,
            title: 'New Offer Alert',
            subTitle:
                '<p>@ikponmwosaedomwande wants to sell 2CAD at the rate of 300</p>',
            date: 'Feb 9, 2024 | 16:46',
            onTap: () {},
          );
        },
        separatorBuilder: (ctx, _) => const HDivider(),
        itemCount: itemCount ?? 10,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
      ),
    );
  }
}
