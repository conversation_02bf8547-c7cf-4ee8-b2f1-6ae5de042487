import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:korrency/core/core.dart';

/// Service for Facebook Analytics with attribution-based tracking
/// Only tracks events for users acquired through Facebook Ads campaigns
class FacebookAnalyticsService {
  static FacebookAnalyticsService? _instance;
  static FacebookAnalyticsService get instance =>
      _instance ??= FacebookAnalyticsService._();

  FacebookAnalyticsService._();

  late FacebookAppEvents _facebookAppEvents;
  bool _isInitialized = false;

  /// Initialize Facebook Analytics
  Future<void> initialize() async {
    try {
      _facebookAppEvents = FacebookAppEvents();

      // Set app ID and client token from environment
      await _facebookAppEvents.setAdvertiserTracking(enabled: true);

      _isInitialized = true;
      printty('✅ Facebook Analytics initialized successfully');
      printty('📱 Facebook App ID: ${EnvConfig.facebookAppId}');
    } catch (e) {
      printty('❌ Error initializing Facebook Analytics: $e');
      rethrow;
    }
  }

  /// Set user ID for Facebook Analytics
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized');
      return;
    }

    try {
      await _facebookAppEvents.setUserID(userId);
      printty('👤 Facebook Analytics User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting Facebook Analytics user ID: $e');
    }
  }

  /// Set user data for enhanced matching
  Future<void> setUserData({
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? city,
    String? state,
    String? country,
    String? zipCode,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized');
      return;
    }

    try {
      final userData = <String, String>{};
      if (email != null) userData['em'] = email;
      if (firstName != null) userData['fn'] = firstName;
      if (lastName != null) userData['ln'] = lastName;
      if (phone != null) userData['ph'] = phone;
      if (city != null) userData['ct'] = city;
      if (state != null) userData['st'] = state;
      if (country != null) userData['country'] = country;
      if (zipCode != null) userData['zp'] = zipCode;

      await _facebookAppEvents.setUserData(
        email: email,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        city: city,
        state: state,
        country: country,
        zip: zipCode,
      );
      printty('📝 Facebook Analytics User Data set');
    } catch (e) {
      printty('❌ Error setting Facebook Analytics user data: $e');
    }
  }

  /// Track event only if user came from Facebook Ads
  Future<void> logEvent(String eventName,
      {Map<String, dynamic>? parameters, double? valueToSum}) async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized');
      return;
    }

    // Check attribution - only track for Facebook Ads users
    if (!AttributionService.instance.isFromFacebookAds()) {
      printty(
          '🚫 Skipping Facebook Analytics event "$eventName" - user not from Facebook Ads');
      return;
    }

    try {
      // Add attribution parameters to event
      final enrichedParameters = <String, dynamic>{
        ...?parameters,
        ...AttributionService.instance.getAttributionParameters(),
      };

      await _facebookAppEvents.logEvent(
        name: eventName,
        parameters: enrichedParameters,
        valueToSum: valueToSum,
      );

      printty('✅ Facebook Analytics event logged: $eventName');
      printty('📝 Parameters: $enrichedParameters');
      if (valueToSum != null) printty('💰 Value: $valueToSum');
    } catch (e) {
      printty('❌ Error logging Facebook Analytics event "$eventName": $e');
    }
  }

  /// Track Create Account event
  Future<void> logCreateAccount({
    required String userId,
    String? method,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      if (method != null) 'registration_method': method,
      'content_category': 'user_acquisition',
      ...?additionalParameters,
    };

    await logEvent('CompleteRegistration', parameters: parameters);
  }

  /// Track Complete KYC event
  Future<void> logCompleteKyc({
    required String userId,
    String? kycLevel,
    String? verificationMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      if (kycLevel != null) 'kyc_level': kycLevel,
      if (verificationMethod != null) 'verification_method': verificationMethod,
      'content_category': 'user_verification',
      ...?additionalParameters,
    };

    // Use custom event for KYC completion
    await logEvent('CompleteKYC', parameters: parameters);
  }

  /// Track Deposit Money event
  Future<void> logDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      'fb_currency': currency,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (corridor != null) 'corridor': corridor,
      'content_category': 'financial_action',
      'content_type': 'deposit',
      ...?additionalParameters,
    };

    // Log as purchase event for conversion tracking
    await logEvent('Purchase', parameters: parameters, valueToSum: amount);

    // Also log as custom deposit event
    await logEvent('DepositMoney', parameters: parameters, valueToSum: amount);
  }

  /// Track Send Money event
  Future<void> logSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      'fb_currency': fromCurrency,
      'from_currency': fromCurrency,
      'to_currency': toCurrency,
      'corridor': corridor,
      if (transactionId != null) 'transaction_id': transactionId,
      'content_category': 'financial_action',
      'content_type': 'money_transfer',
      ...?additionalParameters,
    };

    // Log as purchase event for conversion tracking
    await logEvent('Purchase', parameters: parameters, valueToSum: amount);

    // Also log as custom send money event
    await logEvent('SendMoney', parameters: parameters, valueToSum: amount);
  }

  /// Track Add Payment Info event
  Future<void> logAddPaymentInfo({
    required String userId,
    String? paymentMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      'content_category': 'financial_setup',
      ...?additionalParameters,
    };

    await logEvent('AddPaymentInfo', parameters: parameters);
  }

  /// Track Initiate Checkout event
  Future<void> logInitiateCheckout({
    required String userId,
    required double amount,
    required String currency,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      'fb_currency': currency,
      if (corridor != null) 'corridor': corridor,
      'content_category': 'financial_action',
      ...?additionalParameters,
    };

    await logEvent('InitiateCheckout',
        parameters: parameters, valueToSum: amount);
  }

  /// Track custom conversion events for Facebook Ads
  Future<void> logConversion({
    required String conversionName,
    double? value,
    String? currency,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'conversion_name': conversionName,
      if (currency != null) 'fb_currency': currency,
      'content_category': 'conversion',
      ...?additionalParameters,
    };

    await logEvent(conversionName, parameters: parameters, valueToSum: value);
  }

  /// Flush events to Facebook
  Future<void> flush() async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized');
      return;
    }

    try {
      await _facebookAppEvents.flush();
      printty('🚀 Facebook Analytics events flushed');
    } catch (e) {
      printty('❌ Error flushing Facebook Analytics events: $e');
    }
  }

  /// Get Facebook App Events instance for advanced usage
  FacebookAppEvents get appEvents {
    if (!_isInitialized) {
      throw Exception('Facebook Analytics not initialized');
    }
    return _facebookAppEvents;
  }

  /// Check if Facebook Analytics is initialized
  bool get isInitialized => _isInitialized;
}
