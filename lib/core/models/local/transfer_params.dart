class TransferParams {
  final String? destinationBankUuid;
  final String? destinationBankAccountNumber;
  final String? accountName;
  final String? transferMethod;
  final String? bankName;
  final bool? saveBeneficiary;

  // For IBAN
  final String? firstName;
  final String? lastName;

  TransferParams({
    this.destinationBankUuid,
    this.destinationBankAccountNumber,
    this.accountName,
    this.transferMethod,
    this.bankName,
    this.firstName,
    this.lastName,
    this.saveBeneficiary,
  });

  TransferParams copyWith({
    String? destinationBankUuid,
    String? destinationBankAccountNumber,
    String? accountName,
    String? transferMethod,
    String? bankName,
    bool? saveBeneficiary,
  }) {
    return TransferParams(
      destinationBankUuid: destinationBankUuid ?? this.destinationBankUuid,
      destinationBankAccountNumber:
          destinationBankAccountNumber ?? this.destinationBankAccountNumber,
      accountName: accountName ?? this.accountName,
      transferMethod: transferMethod ?? this.transferMethod,
      bankName: bankName ?? this.bankName,
      firstName: firstName,
      lastName: lastName,
      saveBeneficiary: saveBeneficiary ?? this.saveBeneficiary,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'destination_bank_uuid': destinationBankUuid,
      'destination_bank_account_number': destinationBankAccountNumber,
      'account_name': accountName,
      'transfer_method': transferMethod,
      'bank_name': bankName,
      'first_name': firstName,
      'last_name': lastName,
      'save': lastName,
    };
  }
}
