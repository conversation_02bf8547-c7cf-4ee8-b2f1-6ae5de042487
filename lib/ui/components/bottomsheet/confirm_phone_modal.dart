import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConfirmPhoneModal extends StatelessWidget {
  const ConfirmPhoneModal({
    super.key,
    required this.phoneNo,
    required this.countryCode,
  });

  final String phoneNo;
  final String countryCode;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  padding: EdgeInsets.all(Sizer.width(10)),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: AppColors.white.withValues(alpha: 0.15),
                    border: Border.all(
                      color: AppColors.white.withValues(alpha: 0.70),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.close,
                    size: Sizer.width(16),
                    color: AppColors.white,
                  ),
                ),
              ),
            ],
          ),
          const YBox(12),
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(30)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(28),
                SvgPicture.asset(
                  AppSvgs.confirmPhone,
                  height: Sizer.height(134),
                ),
                const YBox(16),
                Text(
                  'Confirm Phone Number',
                  style: FontTypography.text22.semiBold,
                ),
                YBox(4),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Please confirm this is your correct Number',
                        style: FontTypography.text16.withCustomColor(
                          AppColors.gray93,
                        ),
                      ),
                      TextSpan(
                        text:
                            "\n $countryCode ${AppUtils.formatPhoneNumber(phoneNo)}",
                        style: FontTypography.text16.copyWith(
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(37),
                Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        online: true,
                        isOutline: true,
                        textColor: AppColors.primaryBlue,
                        borderRadius: BorderRadius.circular(Sizer.radius(14)),
                        height: Sizer.height(44),
                        textStyle: FontTypography.text15.medium
                            .withCustomColor(AppColors.primaryBlue),
                        text: "Go back",
                      ),
                    ),
                    const XBox(16),
                    Expanded(
                      child: CustomBtn.solid(
                        onTap: () {
                          Navigator.pop(context, true);
                        },
                        online: true,
                        height: Sizer.height(44),
                        borderRadius: BorderRadius.circular(Sizer.radius(14)),
                        textStyle: FontTypography.text15.medium.withCustomColor(
                          AppColors.white,
                        ),
                        text: "Confirm",
                      ),
                    ),
                  ],
                ),
                const YBox(30),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
