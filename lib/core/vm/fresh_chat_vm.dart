import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:freshchat_sdk/freshchat_user.dart';
import 'package:korrency/core/core.dart';

class FreshChatVM extends BaseVM {
  bool _isInitialized = false; // Track initialization state
  Future<void> initializeFreshchat() async {
    // Check if already initialized
    if (_isInitialized) {
      printty("Freshchat already initialized, skipping");
      return;
    }

    printty("Initializing Freshchat");

    String appID = dotenv.env['FRESHCHAT_APP_ID'] ?? "";
    String appKey = dotenv.env['FRESHCHAT_APP_KEY'] ?? "";
    String domain = dotenv.env['FRESHCHAT_DOMAIN'] ?? "";

    Freshchat.init(
      appID,
      appKey,
      domain,
      teamMemberInfoVisible: true,
      cameraCaptureEnabled: true,
      gallerySelectionEnabled: true,
      responseExpectationEnabled: true,
      showNotificationBanneriOS: true,
    );

    _isInitialized = true; // Mark as initialized
  }

  void configureFreshchatUser(AuthUser? user) {
    FreshchatUser freshchatUser = FreshchatUser("1234", "1234");
    freshchatUser.setFirstName(user?.firstName ?? '');
    freshchatUser.setLastName(user?.lastName ?? '');
    freshchatUser.setEmail(user?.email ?? '');
    freshchatUser.setPhone("+234", user?.phone ?? '');

    Freshchat.setUser(freshchatUser);
  }
}
