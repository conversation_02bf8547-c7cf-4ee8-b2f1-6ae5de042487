import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:korrency/core/core.dart';

/// Enhanced translation helper with robust error handling and fallback mechanisms
class TranslationHelper {
  /// Enhanced translation method with comprehensive error handling
  static String tr(
    String key, {
    List<String>? args,
    Map<String, String>? namedArgs,
    String? fallback,
    bool logMissing = true,
  }) {
    try {
      final translated = key.tr(args: args, namedArgs: namedArgs);
      
      // Check if translation failed (returns key when missing)
      if (translated == key) {
        if (logMissing) {
          _logMissingTranslation(key);
        }
        return fallback ?? _generateFallback(key);
      }
      
      return translated;
    } catch (e) {
      _logTranslationError(key, e);
      return fallback ?? key;
    }
  }

  /// Safe translation method that never throws exceptions
  static String safeTr(
    String key, {
    List<String>? args,
    Map<String, String>? namedArgs,
    String? fallback,
  }) {
    return tr(
      key,
      args: args,
      namedArgs: namedArgs,
      fallback: fallback,
      logMissing: false,
    );
  }

  /// Translate with pluralization support
  static String plural(
    String key,
    int count, {
    List<String>? args,
    Map<String, String>? namedArgs,
    String? fallback,
  }) {
    try {
      return key.plural(count, args: args, namedArgs: namedArgs);
    } catch (e) {
      _logTranslationError(key, e);
      return fallback ?? _generateFallback(key);
    }
  }

  /// Check if a translation key exists
  static bool hasTranslation(String key) {
    try {
      final translated = key.tr();
      return translated != key;
    } catch (e) {
      return false;
    }
  }

  /// Get all missing translation keys (for debugging)
  static Set<String> get missingKeys => _missingKeys;
  static final Set<String> _missingKeys = <String>{};

  /// Clear missing keys cache
  static void clearMissingKeys() {
    _missingKeys.clear();
  }

  /// Log missing translation for debugging
  static void _logMissingTranslation(String key) {
    _missingKeys.add(key);
    
    if (kDebugMode) {
      printty('🌐 Missing translation: $key', level: 'i18n');
    }
    
    // Optional: Send to analytics in production for monitoring
    // AnalyticsService.logMissingTranslation(key);
  }

  /// Log translation errors
  static void _logTranslationError(String key, dynamic error) {
    if (kDebugMode) {
      printty('❌ Translation error for key "$key": $error', level: 'error');
    }
    
    // Optional: Send to crash reporting in production
    // CrashReportingService.logError('Translation error', error, {'key': key});
  }

  /// Generate a human-readable fallback from the translation key
  static String _generateFallback(String key) {
    try {
      // Convert 'auth.login.title' to 'Login Title'
      // Convert 'button_text' to 'Button Text'
      return key
          .split('.')
          .last
          .split('_')
          .map((word) => _capitalize(word))
          .join(' ');
    } catch (e) {
      return key;
    }
  }

  /// Capitalize first letter of a word
  static String _capitalize(String word) {
    if (word.isEmpty) return word;
    return word[0].toUpperCase() + word.substring(1).toLowerCase();
  }

  /// Validate translation completeness between locales
  static Future<Map<String, List<String>>> validateTranslations() async {
    final Map<String, List<String>> issues = {
      'missing_in_french': [],
      'missing_in_english': [],
      'empty_translations': [],
    };

    try {
      // This would need to be implemented based on your specific needs
      // For now, return the structure for future implementation
      return issues;
    } catch (e) {
      printty('Error validating translations: $e');
      return issues;
    }
  }
}

/// Extension methods for easier translation access
extension TranslationExtension on String {
  /// Enhanced tr() method using TranslationHelper
  String get trSafe => TranslationHelper.safeTr(this);
  
  /// Translation with fallback
  String trWithFallback(String fallback) => TranslationHelper.tr(this, fallback: fallback);
  
  /// Check if translation exists
  bool get hasTranslation => TranslationHelper.hasTranslation(this);
}
