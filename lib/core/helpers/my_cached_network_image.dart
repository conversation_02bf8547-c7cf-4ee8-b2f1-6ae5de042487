import 'package:cached_network_image/cached_network_image.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class MyCachedNetworkImage extends StatefulWidget {
  const MyCachedNetworkImage({
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.errorUrl,
    this.fadeInDuration,
    this.imageUrl,
  });
  final double? width, height;
  final BoxFit fit;
  final Duration? fadeInDuration;
  final String? imageUrl, errorUrl;
  @override
  State<MyCachedNetworkImage> createState() => _MyCachedNetworkImageState();
}

class _MyCachedNetworkImageState extends State<MyCachedNetworkImage> {
  @override
  Widget build(BuildContext context) {
    return (widget.imageUrl?.isEmpty ?? false)
        ? imageHelper(
            AppImages.noimage,
            fit: BoxFit.cover,
          )
        : CachedNetworkImage(
            cacheKey: widget.imageUrl,
            imageUrl: widget.imageUrl ?? '',
            fadeInDuration: const Duration(milliseconds: 50),
            fit: widget.fit,
            width: widget.width,
            height: widget.height,
            errorWidget: (context, url, error) => Image.asset(
              AppImages.noimage,
              fit: BoxFit.cover,
              width: MediaQuery.of(context).size.width,
            ),
            progressIndicatorBuilder: (context, url, downloadProgress) {
              return Center(
                child: LogoLoader(
                  h: Sizer.height(40),
                ),
              );
            },
          );
  }
}
