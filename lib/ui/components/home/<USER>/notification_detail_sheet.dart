import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NotificationDetailSheet extends StatelessWidget {
  const NotificationDetailSheet({
    Key? key,
    required this.title,
    required this.date,
    required this.description,
    this.isOutline = false,
    this.btnText,
    this.onTap,
  }) : super(key: key);

  final String title;
  final String date;
  final String description;
  final bool isOutline;
  final String? btnText;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          const YBox(20),
          Text(
            title,
            textAlign: TextAlign.center,
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(4),
          Text(
            date,
            style: AppTypography.text14.copyWith(
              color: AppColors.lightTextGray,
            ),
          ),
          const YBox(4),
          Text(
            description,
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          const YBox(86),
          CustomBtn.solid(
            height: Sizer.height(56),
            isOutline: true,
            textColor: AppColors.primaryBlue,
            onTap: onTap ??
                () {
                  Navigator.pop(context);
                },
            online: true,
            text: btnText ?? "Close",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
