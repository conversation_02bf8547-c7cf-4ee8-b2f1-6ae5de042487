import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReviewOfferScreen extends StatelessWidget {
  const ReviewOfferScreen({
    Key? key,
    required this.args,
  }) : super(key: key);

  final OfferTypeArg args;

  @override
  Widget build(BuildContext context) {
    return Consumer<CreateOfferVM>(builder: (context, vm, _) {
      return Scaffold(
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(20),
                CustomHeader(
                  // showBackBtn: true,
                  showHeader: true,
                  headerText:
                      'Review ${args.offerType == OfferType.sell ? "Sell" : "Buy"} Offer',
                ),
                const YBox(26),
                ContainerWithBluewishBg(
                  child: Column(
                    children: [
                      Text(
                        'Amount you’ll pay',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.blue800,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      if (args.offerType == OfferType.sell)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${AppUtils.formatAmountDoubleString(vm.sellAmountYouWillPay)} CAD',
                              style: AppTypography.text24.copyWith(
                                color: AppColors.blue800,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const XBox(8),
                            imageHelper(
                              AppImages.cadFlag,
                              height: Sizer.height(20),
                              width: Sizer.width(20),
                            ),
                          ],
                        ),
                      if (args.offerType == OfferType.buy)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${AppUtils.formatAmountDoubleString(vm.buyAmountYouWillPay)} NGN',
                              style: AppTypography.text24.copyWith(
                                color: AppColors.blue800,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const XBox(8),
                            imageHelper(
                              AppImages.ngnFlag,
                              height: Sizer.height(20),
                              width: Sizer.width(20),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
                const YBox(20),
                DottedBorder(
                  dashPattern: const [8, 5],
                  strokeWidth: 2,
                  borderType: BorderType.RRect,
                  radius: const Radius.circular(4),
                  color: AppColors.dottedColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(26),
                  ),
                  child: SizedBox(
                    width: Sizer.screenWidth,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Our Fee',
                          rightText: '${vm.ourFee} CAD',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Your Exchange Rate',
                          rightText: args.offerType == OfferType.sell
                              ? '1 CAD = ${vm.rateC.text.trim()} NGN'
                              : '${vm.rateC.text.trim()} NGN = 1 CAD',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Delivery Method',
                          rightText: args.offerType == OfferType.sell
                              ? 'NGN Wallet'
                              : 'CAD Wallet',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(16),
                        Text(
                          '- - - - - - - - - - - - - - - - - - - - - - - - - - - - ',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(8),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Amount you’ll receive',
                          rightText: args.offerType == OfferType.sell
                              ? '${vm.amountYouWillReceiveSellOffer} NGN'
                              : '${vm.fromC.text.trim()} CAD',
                          rightTextColor: AppColors.iconGreen,
                          rightFontWeight: FontWeight.w700,
                        ),
                      ],
                    ),
                  ),
                ),
                const YBox(30),
                if (vm.allowPartialAmt)
                  ContainerWithBluewishBg(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Minimum Exchange",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                        Text(
                          "${AppUtils.formatAmountDoubleString(vm.partialAmt ?? "0")} CAD",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                const Spacer(),
                Row(
                  children: [
                    Icon(
                      Iconsax.info_circle5,
                      color: AppColors.primaryBlue,
                      size: Sizer.radius(24),
                    ),
                    const XBox(10),
                    Expanded(
                      child: Text(
                        "Amount you’ll pay includes a ${vm.ourFee} CAD fee reserved and charged on exchange completion only",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                  ],
                ),
                const YBox(20),
                CustomBtn.solid(
                  onTap: () {
                    Navigator.pushNamed(context, RoutePath.authorizeTransaction,
                        arguments: OfferTypeArg(args.offerType));
                  },
                  online: true,
                  text: "Post Offer",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }
}
