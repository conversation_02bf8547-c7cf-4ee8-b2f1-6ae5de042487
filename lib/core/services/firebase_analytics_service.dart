import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:korrency/core/core.dart';

/// Service for Firebase Analytics with attribution-based tracking
/// Only tracks events for users acquired through Google Ads campaigns
class FirebaseAnalyticsService {
  static FirebaseAnalyticsService? _instance;
  static FirebaseAnalyticsService get instance =>
      _instance ??= FirebaseAnalyticsService._();

  FirebaseAnalyticsService._();

  late FirebaseAnalytics _analytics;
  bool _isInitialized = false;

  /// Initialize Firebase Analytics
  Future<void> initialize() async {
    try {
      _analytics = FirebaseAnalytics.instance;

      // Enable analytics collection
      await _analytics.setAnalyticsCollectionEnabled(true);

      _isInitialized = true;
      printty('✅ Firebase Analytics initialized successfully');
    } catch (e) {
      printty('❌ Error initializing Firebase Analytics: $e');
      rethrow;
    }
  }

  /// Set user ID for Firebase Analytics
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized');
      return;
    }

    try {
      await _analytics.setUserId(id: userId);
      printty('👤 Firebase Analytics User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting Firebase Analytics user ID: $e');
    }
  }

  /// Set user properties
  Future<void> setUserProperty(String name, String value) async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized');
      return;
    }

    try {
      await _analytics.setUserProperty(name: name, value: value);
      printty('📝 Firebase Analytics User Property set: $name = $value');
    } catch (e) {
      printty('❌ Error setting Firebase Analytics user property: $e');
    }
  }

  /// Track event only if user came from Google Ads
  Future<void> logEvent(
      String eventName, Map<String, dynamic>? parameters) async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized');
      return;
    }

    // Check attribution - only track for Google Ads users
    final attribution = AttributionService.instance.currentAttribution;
    if (!AttributionService.instance.isFromGoogleAds()) {
      printty(
          '🚫 Skipping Firebase Analytics event "$eventName" - user not from Google Ads');
      return;
    }

    try {
      // Add attribution parameters to event
      final enrichedParameters = <String, Object>{
        ...?parameters?.cast<String, Object>(),
        ...AttributionService.instance
            .getAttributionParameters()
            .cast<String, Object>(),
      };

      await _analytics.logEvent(
        name: eventName,
        parameters: enrichedParameters,
      );

      printty('✅ Firebase Analytics event logged: $eventName');
      printty('📝 Parameters: $enrichedParameters');
    } catch (e) {
      printty('❌ Error logging Firebase Analytics event "$eventName": $e');
    }
  }

  /// Track Create Account event
  Future<void> logCreateAccount({
    required String userId,
    String? method,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      if (method != null) 'method': method,
      'event_category': 'user_acquisition',
      'event_label': 'account_creation',
      ...?additionalParameters,
    };

    await logEvent('sign_up', parameters);
  }

  /// Track Complete KYC event
  Future<void> logCompleteKyc({
    required String userId,
    String? kycLevel,
    String? verificationMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      if (kycLevel != null) 'kyc_level': kycLevel,
      if (verificationMethod != null) 'verification_method': verificationMethod,
      'event_category': 'user_verification',
      'event_label': 'kyc_completion',
      ...?additionalParameters,
    };

    await logEvent('complete_kyc', parameters);
  }

  /// Track Deposit Money event
  Future<void> logDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      'value': amount,
      'currency': currency,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (corridor != null) 'corridor': corridor,
      'event_category': 'financial_action',
      'event_label': 'deposit',
      ...?additionalParameters,
    };

    // Also log as purchase event for conversion tracking
    await logEvent('purchase', parameters);
    await logEvent('deposit_money', parameters);
  }

  /// Track Send Money event
  Future<void> logSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'user_id': userId,
      'value': amount,
      'from_currency': fromCurrency,
      'to_currency': toCurrency,
      'corridor': corridor,
      if (transactionId != null) 'transaction_id': transactionId,
      'event_category': 'financial_action',
      'event_label': 'money_transfer',
      ...?additionalParameters,
    };

    // Log as both custom event and purchase for conversion tracking
    await logEvent('purchase', parameters);
    await logEvent('send_money', parameters);
  }

  /// Track screen views
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, dynamic>? additionalParameters,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized');
      return;
    }

    // Check attribution - only track for Google Ads users
    if (!AttributionService.instance.isFromGoogleAds()) {
      return;
    }

    try {
      final parameters = <String, Object>{
        'screen_name': screenName,
        if (screenClass != null) 'screen_class': screenClass,
        ...?additionalParameters?.cast<String, Object>(),
        ...AttributionService.instance
            .getAttributionParameters()
            .cast<String, Object>(),
      };

      await _analytics.logEvent(
        name: 'screen_view',
        parameters: parameters,
      );

      printty('📱 Firebase Analytics screen view logged: $screenName');
    } catch (e) {
      printty('❌ Error logging Firebase Analytics screen view: $e');
    }
  }

  /// Track custom conversion events for Google Ads
  Future<void> logConversion({
    required String conversionName,
    double? value,
    String? currency,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      'conversion_name': conversionName,
      if (value != null) 'value': value,
      if (currency != null) 'currency': currency,
      'event_category': 'conversion',
      ...?additionalParameters,
    };

    await logEvent('conversion', parameters);
  }

  /// Get Firebase Analytics instance for advanced usage
  FirebaseAnalytics get analytics {
    if (!_isInitialized) {
      throw Exception('Firebase Analytics not initialized');
    }
    return _analytics;
  }

  /// Check if Firebase Analytics is initialized
  bool get isInitialized => _isInitialized;
}
