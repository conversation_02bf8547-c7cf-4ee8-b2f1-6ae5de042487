// // ignore_for_file: use_build_context_synchronously

// import 'package:flutter/cupertino.dart';
// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class ChangeEmailPhoneScreen extends StatefulWidget {
//   const ChangeEmailPhoneScreen({
//     super.key,
//     required this.arg,
//   });

//   final ChangeInfoArg arg;

//   @override
//   State<ChangeEmailPhoneScreen> createState() => _ChangeEmailPhoneScreenState();
// }

// class _ChangeEmailPhoneScreenState extends State<ChangeEmailPhoneScreen> {
//   List<SecurityQuestion> secQuestions = [];
//   late PageController _pageController;
//   int _currentPageIndex = 0;
//   List<TextEditingController> _answerControllers = [];
//   List<bool> _answersValid = [];

//   @override
//   void initState() {
//     super.initState();
//     _pageController = PageController();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _loadQuestions();
//     });
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     for (var controller in _answerControllers) {
//       controller.dispose();
//     }
//     super.dispose();
//   }

//   _loadQuestions() async {
//     final r = await context.read<SecQuestVM>().getUserSecQuestion(count: 3);
//     if (r.success) {
//       secQuestions = r.data;
//       _initializeControllers();
//       setState(() {});
//     }
//   }

//   _initializeControllers() {
//     _answerControllers = List.generate(
//       secQuestions.length,
//       (index) => TextEditingController(),
//     );
//     _answersValid = List.generate(secQuestions.length, (index) => false);

//     // Add listeners to controllers for validation
//     for (int i = 0; i < _answerControllers.length; i++) {
//       _answerControllers[i].addListener(() => _validateAnswer(i));
//     }
//   }

//   _validateAnswer(int index) {
//     final isValid = _answerControllers[index].text.trim().isNotEmpty;
//     if (_answersValid[index] != isValid) {
//       _answersValid[index] = isValid;
//       setState(() {});
//     }
//   }

//   _checkForAnswers(int index) {
//     final secVm = context.read<SecQuestVM>();
//     secVm
//         .validateSecQuestions(
//       securityQuestionId: secQuestions[index].id ?? 0,
//       answer: _answerControllers[index].text,
//     )
//         .then((v) {
//       printty("value validateSecQuestions $v");
//       if (_currentPageIndex > 0) {
//         if (v.success && v.data?["data"]?["correct_answers"] >= 1) {
//           _navigateOnSuccess();
//           return;
//         } else if (_currentPageIndex == secQuestions.length - 1) {
//           if (v.success && v.data?["data"]?["correct_answers"] >= 2) {
//             _navigateOnSuccess();
//             return;
//           } else {
//             _navigateOnError();
//           }
//         } else {
//           _nextPage();
//         }
//         return;
//       }
//       _nextPage();
//     });
//   }

//   _navigateOnSuccess() {
//     if (widget.arg.isPhone) {
//       Navigator.pushNamed(
//         context,
//         RoutePath.newPhoneNumberScreen,
//       );
//     } else {
//       Navigator.pushNamed(
//         context,
//         RoutePath.newEmailScreen,
//       );
//     }
//   }

//   _navigateOnError() {
//     if (widget.arg.isPhone) {
//       Navigator.pushNamed(
//         context,
//         RoutePath.changeConfirmationScreen,
//         arguments: ChangeConfirmationArg(
//           title: "Uh Oh!",
//           subtitle: "Attempt unsuccessful!",
//           desc:
//               "Sorry we are unable to change your phone Number at this time, please try later",
//           isSuccess: false,
//         ),
//       );
//     } else {
//       Navigator.pushNamed(
//         context,
//         RoutePath.changeConfirmationScreen,
//         arguments: ChangeConfirmationArg(
//           title: "Uh Oh!",
//           subtitle: "Attempt unsuccessful!",
//           desc:
//               "Sorry we are unable to change your email at this time, please try later",
//           isSuccess: false,
//         ),
//       );
//     }
//   }

//   _nextPage() {
//     if (_currentPageIndex < secQuestions.length - 1) {
//       _pageController.nextPage(
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     }
//   }

//   bool _isCurrentAnswerValid() {
//     if (_currentPageIndex >= _answersValid.length) return false;
//     return _answersValid[_currentPageIndex];
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Consumer<SecQuestVM>(builder: (ctx, vm, _) {
//       return Scaffold(
//         backgroundColor: AppColors.white,
//         appBar: NewCustomAppbar(
//           showHeaderTitle: true,
//           headerText: 'Change ${widget.arg.isPhone ? "Phone Number" : "Email"}',
//         ),
//         body: Builder(builder: (context) {
//           if (vm.isBusy) {
//             return const Center(child: CupertinoActivityIndicator());
//           }

//           if (secQuestions.isEmpty) {
//             return Padding(
//               padding: EdgeInsets.symmetric(
//                 horizontal: Sizer.width(24),
//                 vertical: Sizer.height(10),
//               ),
//               child: Center(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Text(
//                       "Something went wrong, Please try again later",
//                       style: FontTypography.text14.medium
//                           .withCustomColor(AppColors.gray93),
//                     ),
//                     YBox(20),
//                     CustomBtn.solid(
//                       onTap: () {
//                         _loadQuestions();
//                       },
//                       borderRadius: BorderRadius.circular(Sizer.radius(20)),
//                       text: "Reload",
//                     ),
//                   ],
//                 ),
//               ),
//             );
//           }
//           return ListView(
//             children: [
//               Padding(
//                 padding: EdgeInsets.symmetric(
//                   horizontal: Sizer.width(24),
//                   vertical: Sizer.height(10),
//                 ),
//                 child: Text(
//                   "To change your ${widget.arg.isPhone ? "phone number" : "email"}, please provide the answer to the security questions you set",
//                   style:
//                       FontTypography.text16.withCustomColor(AppColors.gray93),
//                 ),
//               ),
//               YBox(10),
//               Container(
//                 height: Sizer.height(360),
//                 padding: EdgeInsets.only(
//                   top: Sizer.height(20),
//                 ),
//                 child: PageView.builder(
//                   controller: _pageController,
//                   physics: const NeverScrollableScrollPhysics(),
//                   onPageChanged: (index) {
//                     _currentPageIndex = index;
//                     setState(() {});
//                   },
//                   itemCount: secQuestions.length,
//                   itemBuilder: (context, index) {
//                     return BuildQuestionPage(
//                       index: index,
//                       question: secQuestions[index],
//                       answerControllers: _answerControllers,
//                     );
//                   },
//                 ),
//               ),
//               Padding(
//                 padding: EdgeInsets.only(
//                   left: Sizer.width(24),
//                   right: Sizer.width(24),
//                   top: Sizer.height(150),
//                   bottom: Sizer.height(30),
//                 ),
//                 child: CustomBtn.withChild(
//                   borderRadius: BorderRadius.circular(Sizer.radius(20)),
//                   online: _isCurrentAnswerValid() &&
//                       !vm.busy(validateSecQuestionsState),
//                   onTap: _isCurrentAnswerValid()
//                       ? () {
//                           _checkForAnswers(_currentPageIndex);
//                         }
//                       : null,
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       Text(
//                         'Continue',
//                         style: FontTypography.text15.medium
//                             .withCustomColor(AppColors.white),
//                       ),
//                       const XBox(8),
//                       const Icon(
//                         Icons.arrow_forward,
//                         color: AppColors.white,
//                         size: 20,
//                       ),
//                       if (vm.busy(validateSecQuestionsState)) const XBox(20),
//                       if (vm.busy(validateSecQuestionsState))
//                         const CupertinoActivityIndicator()
//                     ],
//                   ),
//                 ),
//               ),
//               YBox(30),
//             ],
//           );
//         }),
//       );
//     });
//   }
// }
