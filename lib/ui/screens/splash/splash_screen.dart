import 'package:korrency/core/core.dart';
import 'package:lottie/lottie.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _lottieController;

  @override
  void initState() {
    super.initState();
    _lottieController = AnimationController(vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final configVM = context.read<ConfigVM>();
      getPackageInfo();
      determinePosition();

      // Initialize FCM token service and set up refresh listener
      final fcmTokenService = FCMTokenService();
      fcmTokenService.getToken();
      fcmTokenService.setupTokenRefreshListener();

      // HeaderService().getDeviceInfo();

      context.read<AuthUserVM>().getUserFromStorage();
      configVM.getConfigurations().then((value) {
        printty(
            'Configurations BE: ${configVM.configData?.androidAppVersion}'); // Both for IOS and Android
        printty('Configurations Splash: ${configVM.appIsDueForUpdate}');
      });
    });

    Future.delayed(const Duration(seconds: 5), () {
      if (!mounted) return;

      if (context.read<ConfigVM>().appIsDueForUpdate) {
        Navigator.pushNamed(context, RoutePath.updateAvailableScreen);
        return;
      }

      // Check if app was opened via deep link
      if (DeepLinkHandler.instance.wasOpenedViaDeepLink) {
        printty(
            '🔗 App opened via deep link, waiting for AppsFlyer processing...');

        // Wait a bit longer for AppsFlyer to process the deep link
        Future.delayed(const Duration(seconds: 2), () {
          if (!mounted) return;

          // Clear the deep link flag
          DeepLinkHandler.instance.clearDeepLinkFlags();

          // If AppsFlyer hasn't handled the navigation yet, proceed normally
          _navigateBasedOnUserState();
        });
        return;
      }

      // Normal flow - navigate based on user state
      _navigateBasedOnUserState();
    });
  }

  void _navigateBasedOnUserState() {
    if (!mounted) return;

    Navigator.of(context).pushReplacementNamed(
      context.read<AuthUserVM>().user != null
          ? RoutePath.welcomeBackScreen
          : RoutePath.introScreen,
    );
  }

  getPackageInfo() {
    AppInitService().packageInfoInit().then((value) {
      if (value.isNotEmpty && mounted) {
        context.read<ConfigVM>().setMyAppCurrentVersion(value);
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splash,
      body: Center(
        child: SizedBox(
          width: Sizer.screenWidth,
          height: Sizer.screenHeight,
          child: Lottie.asset(
            AppGifs.splash,
            repeat: false,
            controller: _lottieController,
            onLoaded: (composition) {
              final desired = const Duration(seconds: 4);
              _lottieController
                ..duration = desired > composition.duration
                    ? desired
                    : composition.duration
                ..forward();
            },
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _lottieController.dispose();
    super.dispose();
  }
}
