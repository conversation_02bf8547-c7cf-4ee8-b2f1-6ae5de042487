// enum PaymentMethodType {
//   mobileMoney,
//   bankTransfer,
//   korrency,
//   interac,
// }

// extension PaymentMethodTypeExtension on PaymentMethodType {
//   String get text {
//     switch (this) {
//       case PaymentMethodType.mobileMoney:
//         return "Mobile Money";
//       case PaymentMethodType.bankTransfer:
//         return "Bank Transfer";
//       case PaymentMethodType.korrency:
//         return "Korrency";
//       case PaymentMethodType.interac:
//         return "Interac e-Transfer";
//     }
//   }
// }
