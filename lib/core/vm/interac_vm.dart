import 'package:korrency/core/core.dart';

class InteracVM extends BaseVM {
  List<String> _interacEmails = [];
  List<String> get interacEmails => _interacEmails;

  Future<ApiResponse> getInteracEmails() async {
    return await performApiCall(
      url: "/virtual-accounts/fetch-interac-emails",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        printty('data: $data');
        _interacEmails = List<String>.from(data['data']);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> requestInteracEmail(String email) async {
    return await performApiCall(
      url: "/virtual-accounts/update-interac-email",
      method: apiService.postWithAuth,
      body: {
        "interac_email": email,
      },
      onSuccess: (data) {
        printty('data: $data');
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyInteracEmail(String email, String code) async {
    return await performApiCall(
      url: "/virtual-accounts/verify-interac-email",
      method: apiService.postWithAuth,
      body: {
        "interac_email": email,
        "code": code,
      },
      onSuccess: (data) {
        printty('data: $data');
        return apiResponse;
      },
    );
  }
}
