import 'dart:convert';

List<CurrencyLimits> currencyLimitsFromJson(String str) =>
    List<CurrencyLimits>.from(
        json.decode(str).map((x) => CurrencyLimits.fromJson(x)));

class CurrencyLimits {
  CurrencyLimits({
    required this.id,
    required this.name,
    required this.code,
    required this.country,
    required this.category,
    required this.symbol,
    required this.flag,
    required this.isCreatable,
    required this.isFundable,
    required this.limits,
  });

  final int? id;
  final String? name;
  final String? code;
  final String? country;
  final String? category;
  final String? symbol;
  final String? flag;
  final bool? isCreatable;
  final bool? isFundable;
  final Limits? limits;

  factory CurrencyLimits.fromJson(Map<String, dynamic> json) {
    return CurrencyLimits(
      id: json["id"],
      name: json["name"],
      code: json["code"],
      country: json["country"],
      category: json["category"],
      symbol: json["symbol"],
      flag: json["flag"],
      isCreatable: json["is_creatable"],
      isFundable: json["is_fundable"],
      limits: json["limits"] == null ? null : Limits.fromJson(json["limits"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "country": country,
        "category": category,
        "symbol": symbol,
        "flag": flag,
        "is_creatable": isCreatable,
        "is_fundable": isFundable,
        "limits": limits?.toJson(),
      };

  @override
  String toString() {
    return "$id, $name, $code, $country, $category, $symbol, $flag, $limits, ";
  }
}

class Limits {
  Limits({
    required this.dailySpend,
    required this.weeklySpend,
    required this.monthlySpend,
    required this.deposit,
  });

  final LySpend? dailySpend;
  final LySpend? weeklySpend;
  final LySpend? monthlySpend;
  final String? deposit;

  factory Limits.fromJson(Map<String, dynamic> json) {
    return Limits(
      dailySpend: json["daily_spend"] == null
          ? null
          : LySpend.fromJson(json["daily_spend"]),
      weeklySpend: json["weekly_spend"] == null
          ? null
          : LySpend.fromJson(json["weekly_spend"]),
      monthlySpend: json["monthly_spend"] == null
          ? null
          : LySpend.fromJson(json["monthly_spend"]),
      deposit: json["deposit"],
    );
  }

  Map<String, dynamic> toJson() => {
        "daily_spend": dailySpend?.toJson(),
        "weekly_spend": weeklySpend?.toJson(),
        "monthly_spend": monthlySpend?.toJson(),
        "deposit": deposit,
      };

  @override
  String toString() {
    return "$dailySpend, $weeklySpend, $monthlySpend, $deposit, ";
  }
}

class LySpend {
  LySpend({
    required this.totalAmount,
    required this.amountUsed,
    required this.percentageUsed,
    required this.amountLeft,
  });

  final String? totalAmount;
  final String? amountUsed;
  final String? percentageUsed;
  final String? amountLeft;

  factory LySpend.fromJson(Map<String, dynamic> json) {
    return LySpend(
      totalAmount: json["total_amount"],
      amountUsed: json["amount_used"],
      percentageUsed: json["percentage_used"],
      amountLeft: json["amount_left"],
    );
  }

  Map<String, dynamic> toJson() => {
        "total_amount": totalAmount,
        "amount_used": amountUsed,
        "percentage_used": percentageUsed,
        "amount_left": amountLeft,
      };

  @override
  String toString() {
    return "$totalAmount, $amountUsed, $percentageUsed, $amountLeft, ";
  }
}

class CurrencyRateLimitModel {
  final String? limitType;
  final int? remainingAmount;

  CurrencyRateLimitModel({
    this.limitType,
    this.remainingAmount,
  });

  factory CurrencyRateLimitModel.fromJson(Map<String, dynamic> json) =>
      CurrencyRateLimitModel(
        limitType: json["limit_type"],
        remainingAmount: json["remaining_amount"],
      );
}
