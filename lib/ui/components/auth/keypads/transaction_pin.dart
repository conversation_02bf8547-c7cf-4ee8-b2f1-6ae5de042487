import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/auth/auth.dart';

class TransactionPIN extends StatefulWidget {
  final Function(String pin) onSubmit;
  final Function(String pin)? onChange;
  // final Function() onBioAuthenticate;

  const TransactionPIN({
    super.key,
    required this.onSubmit,
    this.onChange,
    // required this.onBioAuthenticate,
  });

  @override
  State<TransactionPIN> createState() => _TransactionPINState();
}

class _TransactionPINState extends State<TransactionPIN> {
  late Size size;
  String pinCode = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    size = MediaQuery.of(context).size;

    return SizedBox(
      width: size.width,
      // color: Colors.red,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  PINInputBox(
                    codeInput:
                        pinCode.isNotEmpty ? pinCode.substring(0, 1) : '',
                  ),
                  const XBox(8),
                  PINInputBox(
                    codeInput:
                        pinCode.length > 1 ? pinCode.substring(1, 2) : '',
                  ),
                  const XBox(8),
                  PINInputBox(
                    codeInput:
                        pinCode.length > 2 ? pinCode.substring(2, 3) : '',
                  ),
                  const XBox(8),
                  PINInputBox(
                    codeInput:
                        pinCode.length > 3 ? pinCode.substring(3, 4) : '',
                  ),
                ],
              )
            ],
          ),
          const YBox(50),
          NumberPad(
            onNumberSelected: (value) {
              setState(() {
                if (value != -1) {
                  if (pinCode.length < 5) {
                    pinCode = pinCode + value.toString();
                  }
                } else {
                  if (pinCode.isNotEmpty) {
                    pinCode = pinCode.substring(0, pinCode.length - 1);
                  }
                }
              });
              if (widget.onChange != null) {
                widget.onChange!(pinCode);
              }
              if (pinCode.length == 4) {
                widget.onSubmit(pinCode);
              }
              printty(pinCode);
            },
            // onBioAuthenticate: widget.onBioAuthenticate,
          )
        ],
      ),
    );
  }
}
