import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransferSuccessModal extends StatefulWidget {
  const TransferSuccessModal({
    super.key,
    this.interacSecAnswer,
    this.recipientName,
  });

  final String? interacSecAnswer;
  final String? recipientName;

  @override
  State<TransferSuccessModal> createState() => _TransferSuccessModalState();
}

class _TransferSuccessModalState extends State<TransferSuccessModal> {
  final interacC = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (mounted) {
        if (widget.interacSecAnswer != null && widget.interacSecAnswer != "") {
          interacC.text = widget.interacSecAnswer!;
          setState(() {});
        }
      }
    });
  }

  @override
  void dispose() {
    interacC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                height: Sizer.screenHeight * 0.88,
                padding: EdgeInsets.only(
                  left: Sizer.width(20),
                  right: Sizer.width(20),
                ),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const YBox(28),
                    SvgPicture.asset(
                      AppSvgs.sendMoneySuccess,
                      height: Sizer.height(189),
                    ),
                    const YBox(16),
                    Text(
                      'Transfer is on its way',
                      style: FontTypography.text22.semiBold,
                    ),
                    YBox(4),
                    Text(
                      "Recipient will get the money in a few minutes",
                      textAlign: TextAlign.center,
                      style: FontTypography.text16.withCustomColor(
                        AppColors.gray93,
                      ),
                    ),
                    if (widget.interacSecAnswer != null &&
                        widget.interacSecAnswer != "")
                      Padding(
                        padding: EdgeInsets.only(
                          top: Sizer.width(16),
                        ),
                        child: CustomTextField(
                          labelText: "Interac Security Answer",
                          showLabelHeader: true,
                          borderRadius: Sizer.height(12),
                          controller: interacC,
                          isReadOnly: true,
                          suffixIcon: InkWell(
                            onTap: () {},
                            child: Padding(
                              padding: EdgeInsets.only(
                                right: Sizer.width(12),
                              ),
                              child: SvgPicture.asset(
                                AppSvgs.copy,
                              ),
                            ),
                          ),
                        ),
                      ),
                    const YBox(16),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(16),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.bgWhite,
                        borderRadius: BorderRadius.circular(Sizer.radius(12)),
                        border: Border.all(color: AppColors.grayAEC),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TransferTimeLine(
                            title: "Transfer Submitted",
                            textOne: "We have received your transfer request.",
                          ),
                          TransferTimeLine(
                            title: "Transfer in Progress",
                            textOne:
                                "Your transfer is on its way to ${(AppUtils.pickName(widget.recipientName ?? 'the recipient')).capitalize()}",
                            boldWords: [
                              AppUtils.pickName(
                                  widget.recipientName ?? 'the recipient')
                            ],
                          ),
                          TransferTimeLine(
                            title: "Payment Completed",
                            textOne:
                                "${(AppUtils.pickName(widget.recipientName ?? 'the recipient')).capitalize()} should receive the money in a few minutes. We'll notify you as soon as it's delivered",
                            boldWords: [
                              AppUtils.pickName(
                                  widget.recipientName ?? 'the recipient')
                            ],
                            isEnd: true,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(20),
                  ),
                  child: CustomBtn.solid(
                    onTap: () {
                      _resetData();
                      Navigator.pushNamedAndRemoveUntil(
                        NavigatorKeys.appNavigatorKey.currentContext!,
                        RoutePath.dashboardNav,
                        (route) => false,
                      );

                      // Navigator.pushNamed(
                      //   NavigatorKeys.appNavigatorKey.currentContext!,
                      //   RoutePath.sendMoneyScreen,
                      // );
                    },
                    online: true,
                    borderRadius: BorderRadius.circular(Sizer.radius(14)),
                    textStyle: FontTypography.text15.medium.withCustomColor(
                      AppColors.white,
                    ),
                    text: "Done",
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _resetData() {
    final sendMoneyVM = context.read<SendMoneyVM>();
    final transactionVm = context.read<TransactionVM>();
    final bankVM = context.read<BankVM>();

    sendMoneyVM.resetData();
    bankVM.resetData();
    transactionVm.setSelectedReason(null);
    _getWalletCredential();
  }

  _getWalletCredential() {
    context.read<BankVM>().resetData();
    context.read<WalletVM>().getWallets();
    context.read<AuthUserVM>().getAuthUser();
    context.read<CurrencyVM>().getCurrencies();
  }
}
