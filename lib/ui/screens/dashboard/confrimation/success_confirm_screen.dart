// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:lottie/lottie.dart';

class SuccessConfirmArg {
  SuccessConfirmArg({
    this.showRatingPrompt = false,
    required this.btnText,
    this.imgPath,
    this.secondBtnText,
    required this.title,
    this.isInteracTransaction = false,
    this.interacSecurityAnswer,
    this.subTitle,
    this.btnTap,
    this.secondBtnTap,
  });

  final bool showRatingPrompt;
  final String? imgPath;
  final String btnText;
  final String? secondBtnText;
  final String title;
  final String? subTitle;
  final String? interacSecurityAnswer;
  final bool isInteracTransaction;
  final VoidCallback? btnTap;
  final VoidCallback? secondBtnTap;
}

class SuccessConfirmScreen extends StatefulWidget {
  const SuccessConfirmScreen({
    super.key,
    required this.arg,
  });

  final SuccessConfirmArg arg;

  @override
  State<SuccessConfirmScreen> createState() => _SuccessConfirmScreenState();
}

class _SuccessConfirmScreenState extends State<SuccessConfirmScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.arg.showRatingPrompt) {
        _getEligibily();
      }
    });
  }

  _getEligibily() async {
    final res = await context.read<RatingVm>().getEligibility();
    if (res.success) {
      if (context.read<RatingVm>().eligibilityModel?.eligibility ?? false) {
        BsWrapper.bottomSheet(
          context: context,
          widget: const RateExperienceModal(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              Container(
                alignment: Alignment.bottomLeft,
                child: ArrowBack(
                  onTap: widget.arg.btnTap,
                ),
              ),
              const YBox(50),
              Lottie.asset(
                widget.arg.imgPath ?? AppGifs.successLottie,
                height: Sizer.height(180),
              ),
              const YBox(82),
              Text(
                widget.arg.title,
                textAlign: TextAlign.center,
                style: AppTypography.text20.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(8),
              if (widget.arg.subTitle != null)
                widget.arg.isInteracTransaction
                    ? const InteracRichText()
                    : Text(
                        widget.arg.subTitle ?? "",
                        textAlign: TextAlign.center,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.textBlack800,
                        ),
                      ),
              const YBox(50),
              if (widget.arg.isInteracTransaction &&
                  widget.arg.interacSecurityAnswer?.isNotEmpty == true)
                CustomTextField(
                  labelText: 'Interac Security Answer',
                  showLabelHeader: true,
                  borderRadius: Sizer.height(4),
                  hintText: widget.arg.interacSecurityAnswer ?? "",
                  hintStyle: AppTypography.text14,
                  isReadOnly: true,
                  suffixIcon: CopyWithIcon(
                    margin: EdgeInsets.only(
                      top: Sizer.height(11),
                      bottom: Sizer.height(11),
                      right: Sizer.width(16),
                    ),
                    onPressed: () {
                      Clipboard.setData(
                        ClipboardData(
                          text: widget.arg.interacSecurityAnswer ?? "",
                        ),
                      );
                      FlushBarToast.fLSnackBar(
                        message: "Copied to clipboard",
                        snackBarType: SnackBarType.success,
                      );
                    },
                  ),
                ),
              const Spacer(),
              CustomBtn.solid(
                onTap: widget.arg.btnTap,
                online: true,
                text: widget.arg.btnText,
                borderRadius: BorderRadius.circular(20),
              ),
              const YBox(16),
              if (widget.arg.secondBtnTap != null)
                CustomBtn.solid(
                  height: Sizer.height(56),
                  borderRadius: BorderRadius.circular(20),
                  isOutline: true,
                  textColor: AppColors.primaryBlue,
                  onTap: widget.arg.secondBtnTap,
                  online: true,
                  text: widget.arg.secondBtnText ?? "Cancel",
                ),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}

// Message for Interac transaction
class InteracRichText extends StatelessWidget {
  const InteracRichText({super.key});

  @override
  Widget build(BuildContext context) {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        children: [
          TextSpan(
            text:
                "Copy the interac security answer below to complete your transaction.",
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          // TextSpan(
          //   text: "phone",
          //   style: AppTypography.text16.copyWith(
          //     fontWeight: FontWeight.bold,
          //     color: AppColors.textBlack800,
          //   ),
          // ),
        ],
      ),
    );
  }
}
