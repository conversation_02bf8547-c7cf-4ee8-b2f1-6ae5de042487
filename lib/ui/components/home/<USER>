import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class HomeHeader extends StatelessWidget implements PreferredSizeWidget {
  const HomeHeader({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(50)),
      child: Consumer<AuthUserVM>(
        builder: (context, vm, _) {
          return Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                            context, RoutePath.accountSettingScreen);
                      },
                      child: ClipOval(
                        child: Container(
                          height: Sizer.height(40),
                          width: Sizer.width(40),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(100),
                          ),
                          child: cacheNetWorkImage(
                            vm.user?.avatarUrl ?? "",
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    const XBox(10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Hey ",
                          style: AppTypography.text12.copyWith(
                            color: AppColors.gray51,
                          ),
                        ),
                        Text(
                          vm.user?.firstName ?? vm.user?.lastName ?? "",
                          style: AppTypography.text18.semiBold.copyWith(
                            color: AppColors.primaryBlue,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                            context, RoutePath.inviteAndEarnScreen);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(12),
                          vertical: Sizer.height(6),
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.grayEFE,
                          borderRadius: BorderRadius.circular(Sizer.radius(6)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Iconsax.gift5,
                              color: AppColors.primaryBlue,
                              size: Sizer.radius(16),
                            ),
                            XBox(4),
                            Text(
                              "Earn  ${vm.referralRule?.currency.symbol}${vm.referralRule?.referredBonus ?? "0"}",
                              style: AppTypography.text12.medium.copyWith(
                                color: AppColors.primaryBlue,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    XBox(12),
                    Consumer<NotificationVM>(
                        builder: (context, notificationVM, _) {
                      return NotificationWidget(
                        count:
                            notificationVM.totalUnreadNotifications.toString(),
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.notifcationScreen);
                        },
                      );
                    }),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(Sizer.height(50));
}
