import 'dart:convert';

EligibilityModel eligibilityModelFromJson(String str) =>
    EligibilityModel.fromJson(json.decode(str));

String eligibilityModelToJson(EligibilityModel data) =>
    json.encode(data.toJson());

class EligibilityModel {
  final bool? eligibility;
  final int? triggerId;
  final String? triggerName;
  final String? triggerDisplayName;
  final String? triggerMessage;

  EligibilityModel({
    this.eligibility,
    this.triggerId,
    this.triggerName,
    this.triggerDisplayName,
    this.triggerMessage,
  });

  factory EligibilityModel.fromJson(Map<String, dynamic> json) =>
      EligibilityModel(
        eligibility: json["eligibility"],
        triggerId: json["trigger_id"],
        triggerName: json["trigger_name"],
        triggerDisplayName: json["trigger_display_name"],
        triggerMessage: json["trigger_message"],
      );

  Map<String, dynamic> toJson() => {
        "eligibility": eligibility,
        "trigger_id": triggerId,
        "trigger_name": triggerName,
        "trigger_display_name": triggerDisplayName,
        "trigger_message": triggerMessage,
      };
}
