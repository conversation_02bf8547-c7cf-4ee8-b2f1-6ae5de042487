import 'package:dotted_border/dotted_border.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class MyOfferDetailsScreen extends StatefulWidget {
  const MyOfferDetailsScreen({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final OfferDetailArg arg;

  @override
  State<MyOfferDetailsScreen> createState() => _MyOfferDetailsScreenState();
}

class _MyOfferDetailsScreenState extends State<MyOfferDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewOffer();
    });
  }

  _viewOffer() async {
    await context.read<MyOfferVM>().viewOffer(widget.arg.id);
  }

  @override
  Widget build(BuildContext context) {
    // printty(widget.arg.offer.type, logLevel: "Offer Details Screen");
    // printty(widget.arg.offer, logLevel: "Offer ID");
    return Consumer<MyOfferVM>(builder: (context, vm, _) {
      var offer = vm.selectOffer;
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: ListView(
              shrinkWrap: true,
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              children: [
                const YBox(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ArrowBack(
                      onTap: () {
                        Navigator.pop(context);
                      },
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Offer Details',
                          style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const XBox(8),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(12),
                            vertical: Sizer.height(4),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Text(
                            offer?.type?.toLowerCase() == "sell offer"
                                ? "Sell"
                                : "Buy",
                            style: AppTypography.text12.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Icon(
                      Iconsax.more_circle,
                      size: Sizer.radius(24),
                    ),
                  ],
                ),
                const YBox(26),
                ContainerWithBluewishBg(
                  bgColor: AppColors.blu000,
                  child: Column(
                    children: [
                      Text(
                        'Amount',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.blue800,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        "${AppUtils.formatAmountDoubleString(offer?.tradingAmount ?? "0")} ${offer?.tradingCurrency?.code}",
                        style: AppTypography.text24.copyWith(
                          color: AppColors.blue800,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(16),
                ContainerWithBluewishBg(
                  bgColor: AppColors.bgGreen,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Amount i have gotten",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGreen,
                        ),
                      ),
                      Text(
                        "\$${AppUtils.formatAmountDoubleString(offer?.partialAmountPaid ?? "0")} ${offer?.tradingCurrency?.code}",
                        style: AppTypography.text16.copyWith(
                          color: AppColors.gray700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(20),
                DottedBorder(
                  dashPattern: const [8, 5],
                  strokeWidth: 2,
                  borderType: BorderType.RRect,
                  radius: const Radius.circular(4),
                  color: AppColors.dottedColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(26),
                  ),
                  child: SizedBox(
                    width: Sizer.screenWidth,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Fee',
                          rightText: vm.fees,
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Rate',
                          rightText: "${offer?.rate}",
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Delivery Method',
                          rightText: '${offer?.deliveryMethod}',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(16),
                        Text(
                          '- - - - - - - - - - - - - - - - - - - - - - - - - - - - ',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(8),
                        (offer?.type?.toLowerCase() == "sell offer")
                            ? CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Amount you’ll pay',
                                rightText:
                                    "${AppUtils.formatAmountDoubleString(offer?.askingAmount ?? "0")} ${offer?.askingCurrency?.code}",
                                rightTextColor: AppColors.iconGreen,
                                rightFontWeight: FontWeight.w700,
                              )
                            : CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Amount you’ll pay',
                                rightText:
                                    "${AppUtils.formatAmountDoubleString(offer?.tradingAmount ?? "0")} ${offer?.tradingCurrency?.code}",
                                rightTextColor: AppColors.iconGreen,
                                rightFontWeight: FontWeight.w700,
                              ),
                      ],
                    ),
                  ),
                ),
                const YBox(24),
                if (offer?.allowsPartialTrade ?? false)
                  ContainerWithBluewishBg(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Minimum Exchange",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                        Text(
                          "\$${AppUtils.formatAmountDoubleString(offer?.minimumPartialTradeAmount ?? "0")}",
                          style: AppTypography.text16.copyWith(
                            color: AppColors.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                const YBox(40),
                Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Iconsax.clock5,
                          size: Sizer.radius(24),
                        ),
                        const XBox(6),
                        Text(
                          "Expires in: 11hr 35 mins",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const YBox(8),
                    Container(
                      height: Sizer.height(7),
                      width: Sizer.screenWidth,
                      color: AppColors.opacityRed100,
                      child: Stack(
                        children: [
                          Container(
                            height: Sizer.height(7),
                            width: Sizer.width(237),
                            color: AppColors.iconRed,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
                const YBox(50),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    CurrencyCard(
                      svgPath: AppSvgs.cancel,
                      bgColor: AppColors.opacityRed100,
                      title: "End Offer",
                      onTap: () {
                        BsWrapper.bottomSheet(
                          context: context,
                          widget: ConfirmationSheet(
                            message: 'Are you sure you want to end this offer?',
                            secondBtnText: "End Offer",
                            // loading: true,
                            secendBtnTap: () {
                              Navigator.pop(context);
                              vm
                                  .endOffer(offerId: offer?.id ?? 0)
                                  .then((value) {
                                if (value.success) {
                                  FlushBarToast.fLSnackBar(
                                    message: value.message.toString(),
                                    snackBarType: SnackBarType.success,
                                  );
                                } else {
                                  FlushBarToast.fLSnackBar(
                                    message: value.message.toString(),
                                  );
                                }
                              });
                            },
                          ),
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.share,
                      title: "Share",
                      onTap: () async {
                        await Share.share(
                          "Hello, Korrency",
                          subject: "Korrency",
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.edit2,
                      bgColor: AppColors.litGrey,
                      title: "Edit Offer",
                      onTap: () {},
                    ),
                  ],
                ),
                const YBox(40),
              ],
            ),
          ),
        ),
      );
    });
  }
}
