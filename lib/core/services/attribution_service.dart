import 'dart:async';
import 'dart:convert';

import 'package:korrency/core/core.dart';

/// Enum representing different user acquisition sources
enum AcquisitionSource { facebook, google, appsflyer, organic, unknown }

/// Model for attribution data
class AttributionData {
  final AcquisitionSource source;
  final String? campaign;
  final String? adSet;
  final String? creative;
  final String? mediaSource;
  final Map<String, dynamic>? rawData;
  final DateTime timestamp;

  AttributionData({
    required this.source,
    this.campaign,
    this.adSet,
    this.creative,
    this.mediaSource,
    this.rawData,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'source': source.name,
      'campaign': campaign,
      'adSet': adSet,
      'creative': creative,
      'mediaSource': mediaSource,
      'rawData': rawData,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AttributionData.fromMap(Map<String, dynamic> map) {
    return AttributionData(
      source: AcquisitionSource.values.firstWhere(
        (e) => e.name == map['source'],
        orElse: () => AcquisitionSource.unknown,
      ),
      campaign: map['campaign'],
      adSet: map['adSet'],
      creative: map['creative'],
      mediaSource: map['mediaSource'],
      rawData: map['rawData'],
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}

/// Service to manage user attribution and determine acquisition sources
class AttributionService {
  static AttributionService? _instance;
  static AttributionService get instance =>
      _instance ??= AttributionService._();

  AttributionService._();

  AttributionData? _currentAttribution;
  final StreamController<AttributionData> _attributionController =
      StreamController<AttributionData>.broadcast();

  Stream<AttributionData> get attributionStream =>
      _attributionController.stream;
  AttributionData? get currentAttribution => _currentAttribution;

  /// Initialize attribution service and load stored attribution data
  Future<void> initialize() async {
    try {
      await _loadStoredAttribution();
      printty('✅ Attribution Service initialized');
    } catch (e) {
      printty('❌ Error initializing Attribution Service: $e');
    }
  }

  /// Process AppsFlyer conversion data to determine attribution
  Future<void> processAppsFlyerData(Map<String, dynamic> conversionData) async {
    try {
      printty('🔍 Processing AppsFlyer conversion data: $conversionData');

      final mediaSource = conversionData['media_source'] as String?;
      final campaign = conversionData['campaign'] as String?;
      final adSet = conversionData['adset'] as String?;
      final creative = conversionData['creative'] as String?;

      AcquisitionSource source = AcquisitionSource.organic;

      // Determine source based on media source
      if (mediaSource != null) {
        if (mediaSource.toLowerCase().contains('facebook') ||
            mediaSource.toLowerCase().contains('fb')) {
          source = AcquisitionSource.facebook;
        } else if (mediaSource.toLowerCase().contains('google') ||
            mediaSource.toLowerCase().contains('adwords')) {
          source = AcquisitionSource.google;
        } else {
          source = AcquisitionSource.appsflyer;
        }
      }

      final attribution = AttributionData(
        source: source,
        campaign: campaign,
        adSet: adSet,
        creative: creative,
        mediaSource: mediaSource,
        rawData: conversionData,
        timestamp: DateTime.now(),
      );

      await _setAttribution(attribution);
      printty('✅ Attribution set: ${source.name} - $mediaSource');
    } catch (e) {
      printty('❌ Error processing AppsFlyer data: $e');
    }
  }

  /// Manually set attribution (for testing or specific cases)
  Future<void> setAttribution(AttributionData attribution) async {
    await _setAttribution(attribution);
  }

  /// Check if user came from Facebook ads
  bool isFromFacebookAds() {
    return _currentAttribution?.source == AcquisitionSource.facebook;
  }

  /// Check if user came from Google ads
  bool isFromGoogleAds() {
    return _currentAttribution?.source == AcquisitionSource.google;
  }

  /// Check if user is organic (no paid attribution)
  bool isOrganic() {
    return _currentAttribution?.source == AcquisitionSource.organic ||
        _currentAttribution == null;
  }

  /// Get attribution parameters for event tracking
  Map<String, dynamic> getAttributionParameters() {
    if (_currentAttribution == null) {
      return {'acquisition_source': 'organic'};
    }

    return {
      'acquisition_source': _currentAttribution!.source.name,
      'campaign': _currentAttribution!.campaign,
      'ad_set': _currentAttribution!.adSet,
      'creative': _currentAttribution!.creative,
      'media_source': _currentAttribution!.mediaSource,
    };
  }

  /// Private method to set attribution and persist it
  Future<void> _setAttribution(AttributionData attribution) async {
    _currentAttribution = attribution;
    _attributionController.add(attribution);

    // Store attribution data for persistence
    await StorageService.storeStringItem(
      StorageKey.userAttribution,
      json.encode(attribution.toMap()),
    );
  }

  /// Load stored attribution data
  Future<void> _loadStoredAttribution() async {
    try {
      final storedData =
          await StorageService.getStringItem(StorageKey.userAttribution);
      if (storedData != null && storedData.isNotEmpty) {
        final attributionMap = json.decode(storedData) as Map<String, dynamic>;
        _currentAttribution = AttributionData.fromMap(attributionMap);
        printty(
            '📱 Loaded stored attribution: ${_currentAttribution!.source.name}');
      }
    } catch (e) {
      printty('⚠️ Error loading stored attribution: $e');
    }
  }

  /// Clear attribution data (for testing or user logout)
  Future<void> clearAttribution() async {
    _currentAttribution = null;
    await StorageService.removeItem(StorageKey.userAttribution);
    printty('🗑️ Attribution data cleared');
  }

  /// Dispose resources
  void dispose() {
    _attributionController.close();
  }
}
