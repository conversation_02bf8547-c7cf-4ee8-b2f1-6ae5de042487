import 'dart:async';

import 'package:dotted_border/dotted_border.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/marketplace/p2p_transfer_screen.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:share_plus/share_plus.dart';

class OfferDetailsScreen extends StatefulWidget {
  const OfferDetailsScreen({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final OfferDetailArg arg;

  @override
  State<OfferDetailsScreen> createState() => _OfferDetailsScreenState();
}

class _OfferDetailsScreenState extends State<OfferDetailsScreen> {
  Timer? _timer;
  int? _secondsRemaining;
  double _percentageExpiring = 0.0;

  @override
  void initState() {
    super.initState();
    printty(widget.arg.id, level: "Offer Details Screen");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewOffer();
    });
  }

  _viewOffer() async {
    var marketPlaceVM = context.read<MarketPlaceOfferVM>();
    await marketPlaceVM.viewOffer(widget.arg.id).then((value) {
      if (value.success) {
        if (marketPlaceVM.selectOffer?.status == MarketOffer.completed) {
          _timer?.cancel();
        } else {
          _startTimer();
        }
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var walletVM = context.watch<WalletVM>();
    return Consumer<MarketPlaceOfferVM>(builder: (context, vm, _) {
      printty('vm.selectOffer ${vm.selectOffer}');
      var offer = vm.selectOffer;
      var walletType = walletVM.getWalletType(offer?.type?.toLowerCase() ?? "");
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: ListView(
              shrinkWrap: true,
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              children: [
                const YBox(20),
                const CustomHeader(
                  // showBackBtn: true,
                  showHeader: true,
                  headerText: 'Offer Details',
                ),
                const YBox(26),
                ContainerWithBluewishBg(
                  bgColor: AppColors.primaryBlue,
                  child: Column(
                    children: [
                      Text(
                        'Amount',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.bgWhite,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        "${AppUtils.formatAmountDoubleString(offer?.tradingAmount ?? "0")} ${offer?.tradingCurrency?.code}",
                        style: AppTypography.text24.copyWith(
                          color: AppColors.bgWhite,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(20),
                DottedBorder(
                  dashPattern: const [8, 5],
                  strokeWidth: 2,
                  borderType: BorderType.RRect,
                  radius: const Radius.circular(4),
                  color: AppColors.dottedColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(26),
                  ),
                  child: SizedBox(
                    width: Sizer.screenWidth,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Fee',
                          rightText:
                              "${AppUtils.formatAmountDoubleString(offer?.tradingFees ?? "0")} ${offer?.tradingCurrency?.code}",
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Rate',
                          rightText: "${offer?.rate}",
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Delivery Method',
                          rightText: 'CAD Wallet',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(16),
                        Text(
                          '- - - - - - - - - - - - - - - - - - - - - - - - - - - - ',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(8),
                        (offer?.type?.toLowerCase() == "sell offer")
                            ? CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Amount you’ll pay',
                                rightText:
                                    "${AppUtils.formatAmountDoubleString(offer?.askingAmount ?? "0")} ${offer?.askingCurrency?.code}",
                                rightTextColor: AppColors.iconGreen,
                                rightFontWeight: FontWeight.w700,
                              )
                            : CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Amount you’ll pay',
                                rightText:
                                    "${AppUtils.formatAmountDoubleString(offer?.tradingAmount ?? "0")} ${offer?.tradingCurrency?.code}",
                                rightTextColor: AppColors.iconGreen,
                                rightFontWeight: FontWeight.w700,
                              ),
                      ],
                    ),
                  ),
                ),
                const YBox(24),
                if (offer?.allowsPartialTrade ?? false)
                  ContainerWithBluewishBg(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Minimum Exchange",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                        Text(
                          "\$${AppUtils.formatAmountDoubleString(offer?.minimumPartialTradeAmount ?? "0")}",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                const YBox(40),
                Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Iconsax.clock5,
                          size: Sizer.radius(24),
                        ),
                        const XBox(6),
                        offer?.status == MarketOffer.completed
                            ? Text(
                                'Completed',
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.gray700,
                                  fontWeight: FontWeight.w500,
                                ),
                              )
                            : Text(
                                _secondsRemaining != null
                                    ? AppUtils.expireAtFormatDuration(
                                        Duration(seconds: _secondsRemaining!))
                                    : "Expires in",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.gray700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                      ],
                    ),
                    const YBox(8),
                    LinearPercentIndicator(
                      animation: true,
                      animationDuration: 2000,
                      lineHeight: 7.0,
                      percent: _percentageExpiring,
                      backgroundColor: AppColors.opacityRed100,
                      progressColor: AppColors.iconRed,
                      animateFromLastPercent: true,
                      restartAnimation: false,
                    ),
                  ],
                ),
                const YBox(50),
                Consumer<MarketPlaceOfferVM>(
                    builder: (context, marketPlaceVM, _) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      CurrencyCard(
                        svgPath: AppSvgs.share,
                        title: "Share",
                        onTap: () async {
                          await Share.share(
                            "Hello, Korrency",
                            subject: "Korrency",
                          );
                        },
                      ),
                      CurrencyCard(
                        svgPath: AppSvgs.buyCrypto,
                        title: "Partial ${offer?.acceptingTradeText}",
                        onTap: () {
                          if (offer?.allowsPartialTrade ?? false) {
                            marketPlaceVM.offerAmountForExchange(
                              offer?.minimumPartialTradeAmount ?? "0",
                              offer ?? Offers(),
                              walletType?.balance,
                            );
                            Navigator.of(context).pushNamed(
                              RoutePath.p2ptransferScreen,
                              arguments: P2pTransferArg(
                                offer: offer ?? Offers(),
                              ),
                            );
                          } else {
                            FlushBarToast.fLSnackBar(
                              message:
                                  "Partial trade not allowed for this offer",
                            );
                          }
                        },
                      ),
                      CurrencyCard(
                        svgPath: AppSvgs.wallet,
                        bgColor: AppColors.primaryBlue,
                        title: "${offer?.acceptingTradeText} All",
                        onTap: () {
                          if (offer?.status != MarketOffer.active) {
                            FlushBarToast.fLSnackBar(
                              message: "Offer is not active",
                            );
                            return;
                          }
                          marketPlaceVM.offerAmountForExchange(
                            offer?.balance ?? "0",
                            offer ?? Offers(),
                            walletType?.balance,
                          );
                          Navigator.of(context).pushNamed(
                            RoutePath.p2ptransferScreen,
                            arguments: P2pTransferArg(
                              offer: offer ?? Offers(),
                            ),
                          );
                        },
                      ),
                    ],
                  );
                }),
                const YBox(40),
                if (offer?.user != null)
                  Text(
                    (offer?.type?.toLowerCase() == "sell offer")
                        ? "About the Seller"
                        : "About the Buyer",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.textGray500,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                const YBox(16),
                if (offer?.user != null)
                  ContainerWithBluewishBg(
                    child: UserListTile(
                      name: offer?.user?.userName ?? "",
                      avater: offer?.user?.avatarUrl,
                      showTrailing: true,
                    ),
                  ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  _startTimer() {
    var offer = context.read<MarketPlaceOfferVM>().selectOffer;
    DateTime expiresDateTime = offer?.expiresAt ?? DateTime.now();
    DateTime createdDateTime = offer?.createdAt ?? DateTime.now();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Calculate total duration and current elapsed time
      Duration totalDuration = expiresDateTime.difference(createdDateTime);
      Duration elapsedTime = DateTime.now().difference(createdDateTime);

      // Calculate percentage
      double percentageRemaining =
          ((totalDuration - elapsedTime).inSeconds / totalDuration.inSeconds);
      _secondsRemaining = expiresDateTime.difference(DateTime.now()).inSeconds;

      if (percentageRemaining < 0.0 || percentageRemaining > 1.0) {
        // Adjust the value to be within the range
        // For example, clamping it between 0.0 and 1.0
        percentageRemaining = percentageRemaining.clamp(0.0, 1.0);
      }

      _percentageExpiring = percentageRemaining;

      setState(() {
        printty('_secondsRemaining: $_secondsRemaining');
        printty('Percentage Remaining: $percentageRemaining%');
        if ((_secondsRemaining ?? 0) <= 0) {
          printty('Timer is done');
          _timer?.cancel();
          // _showBottomSheet();
          return;
        }
      });
    });
  }
}
