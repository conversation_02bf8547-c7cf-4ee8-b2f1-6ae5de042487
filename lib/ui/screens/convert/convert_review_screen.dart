// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConvertReviewScreen extends StatefulWidget {
  const ConvertReviewScreen({super.key});

  @override
  State<ConvertReviewScreen> createState() => _ConvertReviewScreenState();
}

class _ConvertReviewScreenState extends State<ConvertReviewScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ConvertMoneyVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.busy(convertMoneyLoading),
          child: Scaffold(
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: "Review Details",
            ),
            body: ListView(
              padding: EdgeInsets.only(
                top: Sizer.height(24),
                left: Sizer.width(24),
                right: Sizer.width(24),
              ),
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                    vertical: Sizer.height(20),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.grayFE,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.grayAEC,
                    ),
                  ),
                  child: Column(
                    children: [
                      ReviewRowWidget(
                        iconPath: vm.fromConvertWallet?.currency?.flag ?? '',
                        code: vm.fromConvertWallet?.currency?.code ?? '',
                        title: 'Converting from',
                        amount: AppUtils.formatAmountDoubleString(vm.fromC.text
                            .trim()
                            .toString()
                            .replaceAll(',', '')),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: Sizer.height(12)),
                        child: HDivider(color: AppColors.grayAEC),
                      ),
                      ReviewRowWidget(
                        iconPath: vm.toConvertWallet?.currency?.flag ?? '',
                        code: vm.toConvertWallet?.currency?.code ?? '',
                        title: 'Converting to',
                        amount: vm.toC.text,
                      ),
                    ],
                  ),
                ),
                const YBox(24),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.radius(12),
                    vertical: Sizer.height(16),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.grayAEC,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BuildColText(
                        title: 'Our Fee',
                        subTitle: "0.00 CAD",
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: Sizer.height(16)),
                        child: BuildColText(
                          title: 'Exchange Rate',
                          subTitle: vm.rateFormat,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: Sizer.height(16)),
                        child: BuildColText(
                          title: 'Estimated Delivery',
                          subTitle: "Instant",
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(160),
              ],
            ),
            bottomSheet: Container(
              padding: EdgeInsets.only(
                left: Sizer.width(24),
                right: Sizer.width(24),
                top: Sizer.height(10),
                bottom: Sizer.height(50),
              ),
              color: AppColors.white,
              child: CustomBtn.withChild(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                child: ContinueText(isOnline: true),
                onTap: () async {
                  _convert();
                },
                online: true,
              ),
            ),
          ),
        );
      },
    );
  }

  _convert() async {
    var convertMoneyVM = context.read<ConvertMoneyVM>();
    final res = await convertMoneyVM.convertMoney();
    if (res.success) {
      BsWrapper.bottomSheet(
        canDismiss: false,
        context: context,
        widget: ConversionSuccessModal(),
      );
    } else {
      convertMoneyVM.resetData();
      BsWrapper.bottomSheet(
        context: context,
        widget: ConversionErrorModal(
          message: res.message,
        ),
      );
    }
  }

  // _getWalletCredential() {
  //   context.read<WalletVM>().getWallets();
  //   context.read<AuthUserVM>().getAuthUser();
  //   context.read<CurrencyVM>().getCurrencies();
  //   context.read<TransactionVM>().getTransactions();
  // }
}
