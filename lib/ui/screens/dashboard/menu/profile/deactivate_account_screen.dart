import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DeactivateAccountScreen extends StatefulWidget {
  const DeactivateAccountScreen({super.key});

  @override
  State<DeactivateAccountScreen> createState() =>
      _DeactivateAccountScreenState();
}

class _DeactivateAccountScreenState extends State<DeactivateAccountScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthUserVM>().accountCheck();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const ArrowBack(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Deactivate Account",
                    subtitle:
                        "Before you continue, please ensure all the following is done",
                  ),
                  const YBox(24),

                  // Note: For the checks here, if its true mark it as X
                  SecurityTile(
                    title: 'No Funds in Account',
                    // subTitle: 'Send OTP to registered email',
                    showTrailing: true,
                    trailIconColor:
                        vm.hasFundInAccount ? AppColors.opacityRed100 : null,
                    trailingWidget: vm.hasFundInAccount
                        ? const Icon(
                            Icons.cancel,
                            size: 24,
                            color: AppColors.iconRed,
                          )
                        : const Icon(
                            Icons.check_circle,
                            size: 24,
                            color: AppColors.blue800,
                          ),
                    isSelected: !vm.hasFundInAccount,
                    leadingIcon: AppSvgs.money1,
                  ),
                  const YBox(24),
                  SecurityTile(
                    leadingIcon: AppSvgs.currencyExchange,
                    title: 'No Open Offers ',
                    // subTitle: 'Send OTP to registered phone number',
                    showTrailing: true,
                    trailIconColor:
                        vm.hasOpenOffers ? AppColors.opacityRed100 : null,
                    trailingWidget: vm.hasOpenOffers
                        ? const Icon(
                            Icons.cancel,
                            size: 24,
                            color: AppColors.iconRed,
                          )
                        : const Icon(
                            Icons.check_circle,
                            size: 24,
                            color: AppColors.blue800,
                          ),
                    // isSelected: !vm.hasOpenOffers,
                    onTap: () {},
                  ),
                  const Spacer(),
                  CustomBtn.solid(
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    onTap: () {
                      Navigator.pushNamed(
                          context, RoutePath.deactivateAccountReasonScreen);
                    },
                    online: vm.enableDeactiveBtn,
                    text: "Continue",
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
