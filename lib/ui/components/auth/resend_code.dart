import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:korrency/core/core.dart';

class ResendCode extends StatefulWidget {
  const ResendCode({
    super.key,
    required this.onResendCode,
  });

  final Function onResendCode;

  @override
  State<ResendCode> createState() => _ResendCodeState();
}

class _ResendCodeState extends State<ResendCode> {
  int _secondsRemaining = 60 * 5;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_secondsRemaining > 0) {
          _secondsRemaining--;
        } else {
          _timer?.cancel();
        }
      });
    });
  }

  void _restartTimer() {
    setState(() {
      _secondsRemaining = 60 * 5;
      _startTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isTimedOut = _secondsRemaining == 0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        isTimedOut
            ? RichText(
                text: TextSpan(
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryBlue90,
                    fontWeight: FontWeight.w500,
                    fontFamily: AppFont.outfit.family,
                  ),
                  children: [
                    TextSpan(
                      text: "Didn’t receive code? ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.gray79,
                        fontFamily: AppFont.outfit.family,
                      ),
                    ),
                    TextSpan(
                      text: "Resend",
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          _restartTimer();
                          widget.onResendCode();
                        },
                    ),
                  ],
                ),
              )
            : RichText(
                text: TextSpan(
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryBlue90,
                    fontWeight: FontWeight.w500,
                    fontFamily: AppFont.outfit.family,
                  ),
                  children: [
                    TextSpan(
                      text: "Code expires in ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.gray79,
                        fontFamily: AppFont.outfit.family,
                      ),
                    ),
                    TextSpan(text: "${_formatTime(_secondsRemaining)}s"),
                  ],
                ),
              ),
      ],
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    String formattedMinutes = (minutes < 10) ? '0$minutes' : '$minutes';
    String formattedSeconds =
        (remainingSeconds < 10) ? '0$remainingSeconds' : '$remainingSeconds';
    return '$formattedMinutes:$formattedSeconds';
  }

  // getDurationInMins() {
  //   seconds = (duration % 60).toInt().toString();
  //   min = (duration ~/ 60).toInt().toString();
  // }
}
