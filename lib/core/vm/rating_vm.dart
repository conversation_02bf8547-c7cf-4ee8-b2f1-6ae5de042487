import 'package:korrency/core/core.dart';

class RatingVm extends BaseVM {
  EligibilityModel? _eligibilityModel;
  EligibilityModel? get eligibilityModel => _eligibilityModel;

  Future<ApiResponse> getEligibility() async {
    return await performApiCall(
      url: "/ratings/prompt-eligibility",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _eligibilityModel = EligibilityModel.fromJson(data['data']);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> submitReview({
    required int userRatingPromptId,
    required int rating,
    String? feedback,
  }) async {
    final body = {
      "rating": rating,
      "feedback": feedback, // nullable
    };
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/ratings/submit-review/$userRatingPromptId",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}
