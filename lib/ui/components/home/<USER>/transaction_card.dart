import 'package:korrency/core/core.dart';

class TransactionCard extends StatelessWidget {
  const TransactionCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.amount,
    required this.type,
    required this.category,
    this.isCredit = false,
    this.subTitleWithColor = false,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String amount;
  final String type;
  final String category;
  final bool isCredit;
  final bool subTitleWithColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(5),
        ),
        child: Row(
          children: [
            Skeleton.replace(
              replacement: const Bone.circle(
                size: 50,
              ),
              child: Container(
                padding: EdgeInsets.all(Sizer.radius(6)),
                decoration: BoxDecoration(
                  // color: isCredit ? AppColors.opacityRed : AppColors.opacityGreen,
                  color: _getIconColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(
                    Sizer.width(40),
                  ),
                ),
                child: svgHelper(
                  _getSvg(),
                  height: Sizer.height(24),
                  width: Sizer.width(24),
                  color: _getIconColor(),
                  // color: isCredit ? AppColors.iconRed : AppColors.iconGreen,
                ),
              ),
            ),
            const XBox(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.textBlack100,
                    ),
                  ),
                  const YBox(2),
                  subTitleWithColor
                      ? Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(8),
                            vertical: Sizer.height(1),
                          ),
                          decoration: BoxDecoration(
                            // color: AppColors.opacityGreen,
                            color: _getTextColor(subTitle.toLowerCase())
                                .withOpacity(0.08),
                            borderRadius: BorderRadius.circular(
                              Sizer.radius(12),
                            ),
                          ),
                          child: Text(
                            subTitle,
                            style: AppTypography.text14.copyWith(
                              color: _getTextColor(subTitle.toLowerCase()),
                            ),
                          ),
                        )
                      : Text(
                          subTitle,
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                ],
              ),
            ),
            const XBox(18),
            Text(
              "${type == "credit" ? "+" : "-"} $amount",
              style: AppTypography.text14.copyWith(
                color: AppColors.gray700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSvg() {
    final normalizedCategory = category.toLowerCase();
    if (normalizedCategory == "p2p" || normalizedCategory == "conversion") {
      return AppSvgs.arrowSwap;
    }
    switch (type) {
      case "credit":
        return AppSvgs.received;
      case "debit":
        return AppSvgs.send;
      default:
        return AppSvgs.arrowSwap;
    }
  }

  Color _getIconColor() {
    final normalizedCategory = category.toLowerCase();
    final isP2P =
        normalizedCategory == "p2p" || normalizedCategory == "conversion";
    switch (type) {
      case "credit":
        return isP2P ? AppColors.iconDarkBlue : AppColors.iconGreen;
      case "debit":
        return isP2P ? AppColors.iconDarkBlue : AppColors.iconRed;
      default:
        return AppColors.iconDarkBlue;
    }
  }

  _getTextColor(String status) {
    switch (status) {
      case "successful":
        return AppColors.iconGreen;
      case "pending":
        return AppColors.pending;
      case "sent":
        return AppColors.sent;
      case "reversed":
        return AppColors.blueCD3;
      default:
        return AppColors.failed;
    }
  }
}
