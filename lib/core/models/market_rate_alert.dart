import 'dart:convert';

List<MarketRateAlert> marketRateAlertFromJson(String str) =>
    List<MarketRateAlert>.from(
        json.decode(str).map((x) => MarketRateAlert.fromJson(x)));

String marketRateAlertToJson(List<MarketRateAlert> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class MarketRateAlert {
  int? id;
  String? offerType;
  int? rate;
  String? minimumAmount;
  String? maximumAmount;
  String? duration;
  DateTime? createdAt;

  MarketRateAlert({
    this.id,
    this.offerType,
    this.rate,
    this.minimumAmount,
    this.maximumAmount,
    this.duration,
    this.createdAt,
  });

  factory MarketRateAlert.fromJson(Map<String, dynamic> json) =>
      MarketRateAlert(
        id: json["id"],
        offerType: json["offer_type"],
        rate: json["rate"],
        minimumAmount: json["minimum_amount"],
        maximumAmount: json["maximum_amount"],
        duration: json["duration"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "offer_type": offerType,
        "rate": rate,
        "minimum_amount": minimumAmount,
        "maximum_amount": maximumAmount,
        "duration": duration,
        "created_at": createdAt?.toIso8601String(),
      };
}
