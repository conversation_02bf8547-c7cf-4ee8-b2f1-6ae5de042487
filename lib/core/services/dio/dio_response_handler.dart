// ignore_for_file: unused_catch_stack

import 'package:dio/dio.dart';
import 'package:korrency/core/core.dart';

class DioResponseHandler {
  static ApiResponse parseResponse(Response res) {
    try {
      return ApiResponse(
        code: res.statusCode,
        success: res.statusCode! >= 200 && res.statusCode! < 300,
        data: res.data,
        message: res.data["message"] ?? "Operation successful",
      );
    } catch (e) {
      return ApiResponse(
        code: res.statusCode,
        data: res.data,
        success: false,
        message: e.toString(),
      );
    }
  }

  static ApiResponse dioErrorHandler(DioException e, [Response? res]) {
    final dioError = e;
    printty("Dio Error dioErrorHandler: $dioError ,,,,, ${dioError.type}.");

    switch (dioError.type) {
      case DioExceptionType.badResponse:
        return ApiResponse(
          code: dioError.response?.statusCode,
          data: res?.data,
          success: false,
          message: dioError.response?.data["message"] is List
              ? dioError.response?.data["message"].join(", ")
              : dioError.response?.data["message"] ??
                  dioError.response?.statusMessage ??
                  AppText.errorMsg,
        );

      case DioExceptionType.cancel:
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.unknown:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.connectionError:
      case DioExceptionType.receiveTimeout:
        return ApiResponse(
          code: 500,
          data: res?.data,
          success: false,
          message: _getErrorMsg(dioError.type),
        );
      default:
        return ApiResponse(
          code: 500,
          data: res?.data,
          success: false,
          message: AppText.errorMsg,
        );
    }
  }

  static String _getErrorMsg(DioExceptionType type) {
    switch (type) {
      case DioExceptionType.cancel:
        return "Operation cancelled";
      case DioExceptionType.connectionTimeout:
        return "Connection Timed Out";
      case DioExceptionType.unknown:
        return AppText.errorMsg;
      case DioExceptionType.sendTimeout:
        return "Sender Connection Timed Out";
      case DioExceptionType.connectionError:
        return "Connection Error, Please check your internet connection";
      case DioExceptionType.receiveTimeout:
        return "Reciever Connection Timed Out";
      default:
        return AppText.errorMsg;
    }
  }
}
