import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/transactions/transactions.dart';

class TransactionReceipt extends StatefulWidget {
  const TransactionReceipt({
    super.key,
    this.sender,
    required this.transactionArg,
  });

  final String? sender;
  final TransactionArg transactionArg;

  @override
  State<TransactionReceipt> createState() => _TransactionReceiptState();
}

class _TransactionReceiptState extends State<TransactionReceipt> {
  @override
  Widget build(BuildContext context) {
    Transaction transaction = widget.transactionArg.transaction;
    return Container(
      color: AppColors.bgWhite,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        child: Column(
          children: [
            const YBox(80),
            svgHelper(AppSvgs.bothLogo, height: 24, width: 110),
            const YBox(10),
            Text(
              "Transaction Receipt",
              style: AppTypography.text14.copyWith(
                color: AppColors.gray600,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(30),
            Text(
              "${transaction.currency?.code ?? ""} ${AppUtils.formatAmountDoubleString(transaction.amount ?? "")}",
              style: AppTypography.text24.copyWith(
                color: AppColors.primaryBlue,
                fontWeight: FontWeight.w700,
              ),
            ),
            const YBox(50),
            SizedBox(
              width: Sizer.screenWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomListViews.currencyListText(
                    margin: EdgeInsets.zero,
                    leftText: 'Description',
                    rightText: transaction.description ?? '',
                    rightFontWeight: FontWeight.w500,
                    rightTextColor: AppColors.textGray,
                  ),
                  const YBox(30),
                  CustomListViews.currencyListText(
                    margin: EdgeInsets.zero,
                    leftText: 'Status',
                    rightText: widget.transactionArg.transaction.status ?? "",
                    rightTextColor: AppColors.iconGreen,
                    rightFontWeight: FontWeight.w600,
                  ),
                  const YBox(30),
                  CustomListViews.currencyListText(
                    margin: EdgeInsets.zero,
                    leftText: 'Transaction Type',
                    rightText: widget.transactionArg.transaction.category ?? "",
                    rightFontWeight: FontWeight.w700,
                  ),
                  const YBox(30),
                  CustomListViews.currencyListText(
                    margin: EdgeInsets.zero,
                    leftText: 'Fees',
                    rightText:
                        "${transaction.fees ?? ""} ${transaction.currency?.code ?? ""}",
                    rightFontWeight: FontWeight.w500,
                    rightTextColor: AppColors.textGray,
                  ),
                  if (transaction.rateFormat != null) const YBox(30),
                  if (transaction.rateFormat != null)
                    CustomListViews.currencyListText(
                      margin: EdgeInsets.zero,
                      leftText: 'Exchange Rate',
                      rightText: transaction.rate ?? '',
                      rightFontWeight: FontWeight.w500,
                      rightTextColor: AppColors.textGray,
                    ),
                ],
              ),
            ),
            const YBox(30),
            Column(
              children: [
                CustomListViews.currencyListText(
                  margin: EdgeInsets.zero,
                  leftText: 'Date/Time',
                  rightText: AppUtils.formatDateTime(
                      widget.transactionArg.transaction.createdAt.toString()),
                  rightFontWeight: FontWeight.w500,
                  rightTextColor: AppColors.textGray,
                ),
                if (transaction.category == "transfer")
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const YBox(30),
                      CustomListViews.currencyListText(
                        margin: EdgeInsets.zero,
                        leftText: 'Recipient',
                        rightText:
                            widget.transactionArg.transaction.destination ?? "",
                        rightFontWeight: FontWeight.w500,
                        rightTextColor: AppColors.textGray,
                      ),
                    ],
                  ),
                if (transaction.source != null &&
                    transaction.category != "transfer")
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const YBox(30),
                      CustomListViews.currencyListText(
                        margin: EdgeInsets.zero,
                        leftText: 'Source',
                        rightText:
                            widget.transactionArg.transaction.source ?? "",
                        rightFontWeight: FontWeight.w500,
                        rightTextColor: AppColors.textGray,
                      ),
                    ],
                  ),
                if (transaction.category?.toLowerCase() == "transfer")
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const YBox(30),
                      CustomListViews.currencyListText(
                        margin: EdgeInsets.zero,
                        leftText: 'Sender',
                        rightText: widget.sender ?? '',
                        rightFontWeight: FontWeight.w500,
                        rightTextColor: AppColors.textGray,
                      ),
                    ],
                  ),
                if (transaction.destination != null &&
                    transaction.category != "transfer")
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const YBox(30),
                      CustomListViews.currencyListText(
                        margin: EdgeInsets.zero,
                        leftText: 'Destination',
                        rightText:
                            widget.transactionArg.transaction.destination ?? "",
                        rightFontWeight: FontWeight.w500,
                        rightTextColor: AppColors.textGray,
                      ),
                    ],
                  ),
                const YBox(30),
                CustomListViews.currencyListText(
                  margin: EdgeInsets.zero,
                  leftText: 'Reference',
                  rightText: widget.transactionArg.transaction.reference ?? "",
                  rightFontWeight: FontWeight.w500,
                  rightTextColor: AppColors.textGray,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
