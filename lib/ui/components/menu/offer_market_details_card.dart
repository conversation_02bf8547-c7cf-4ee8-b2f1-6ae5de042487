import 'dart:async';

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class OfferMarketDetailsCard extends StatefulWidget {
  const OfferMarketDetailsCard({
    super.key,
    this.onTap,
    required this.title1,
    required this.title2,
    required this.rate,
    this.expiresAt,
    this.createAt,
    this.minimumAmount,
    this.showMinimumAmount = false,
    this.isCompleted = false,
    this.offerType = OfferType.sell,
  });

  final VoidCallback? onTap;
  final String title1;
  final String title2;
  final String rate;
  final DateTime? expiresAt;
  final DateTime? createAt;
  final bool showMinimumAmount;
  final bool isCompleted;
  final String? minimumAmount;
  final OfferType offerType;

  @override
  State<OfferMarketDetailsCard> createState() => _OfferMarketDetailsCardState();
}

class _OfferMarketDetailsCardState extends State<OfferMarketDetailsCard> {
  Timer? _timer;
  int _secondsRemaining = 0;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  _startTimer() {
    // DateTime createdDateTime = widget.createAt ?? DateTime.now();
    DateTime expiresDateTime = widget.expiresAt ?? DateTime.now();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _secondsRemaining = expiresDateTime.difference(DateTime.now()).inSeconds;

      setState(() {
        // printty('Difference in seconds: $_secondsRemaining');
        if (_secondsRemaining <= 0) {
          printty('Timer is done');
          _timer?.cancel();
          // _showBottomSheet();
          return;
        }
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    printty('OfferMarketDetailsCard disposed');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: ContainerWithBluewishBg(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(13),
        ).copyWith(
          left: Sizer.width(16),
          right: Sizer.width(8),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: widget.title1,
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w700,
                              fontFamily: "Inter",
                              color: AppColors.blue600,
                            ),
                          ),
                          TextSpan(
                            text: widget.offerType == OfferType.sell
                                ? " avaliable for "
                                : " needed for ",
                            style: AppTypography.text14.copyWith(
                              fontFamily: "Inter",
                              color: AppColors.blue600,
                            ),
                          ),
                          TextSpan(
                            text: widget.title2,
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w700,
                              fontFamily: "Inter",
                              color: AppColors.blue600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const YBox(6),
                    Text(
                      "Rate: ${widget.rate}",
                      style: AppTypography.text12.copyWith(
                        color: AppColors.gray500,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  ],
                ),
                const Spacer(),
                Icon(
                  Iconsax.arrow_right_3,
                  color: AppColors.iconBlack800,
                  size: Sizer.radius(24),
                ),
              ],
            ),
            const YBox(12),
            Row(
              children: [
                widget.isCompleted
                    ? Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: AppColors.iconGreen,
                            size: Sizer.radius(16),
                          ),
                          const XBox(4),
                          Text(
                            "Completed",
                            style: AppTypography.text10
                                .copyWith(color: AppColors.gray700),
                          ),
                        ],
                      )
                    : Row(
                        children: [
                          Icon(
                            Iconsax.clock5,
                            color: _secondsRemaining > 0
                                ? Colors.yellow.shade900
                                : AppColors.iconRed,
                            size: Sizer.radius(16),
                          ),
                          const XBox(4),
                          Text(
                            AppUtils.expireAtFormatDuration(
                                Duration(seconds: _secondsRemaining)),
                            style: AppTypography.text10
                                .copyWith(color: AppColors.gray700),
                          ),
                        ],
                      ),
                const Spacer(),
                if (widget.showMinimumAmount)
                  Container(
                    padding: EdgeInsets.only(right: Sizer.width(6)),
                    child: Text(
                      widget.minimumAmount ?? "",
                      style: AppTypography.text12.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  )
              ],
            )
          ],
        ),
      ),
    );
  }
}
