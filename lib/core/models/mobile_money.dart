import 'dart:convert';

List<MobileMoney> mobileMoneyFromJson(String str) => List<MobileMoney>.from(
    json.decode(str).map((x) => MobileMoney.fromJson(x)));

String mobileMoneyToJson(List<MobileMoney> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class MobileMoney {
  final String? name;
  final String? uuid;
  final String? mobileNumber; // Using for Args not coming from BE
  final String? recipientName; // Using for Args not coming from BE
  final bool? saveBeneficiary; // Using for Args not coming from BE

  MobileMoney({
    this.name,
    this.uuid,
    this.mobileNumber,
    this.recipientName,
    this.saveBeneficiary,
  });

  factory MobileMoney.fromJson(Map<String, dynamic> json) => MobileMoney(
        name: json["name"],
        uuid: json["uuid"],
        mobileNumber: json["mobileNumber"],
        recipientName: json["recipientName"],
        saveBeneficiary: json["save_beneficiary"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "uuid": uuid,
        "mobileNumber": mobileNumber,
        "recipientName": recipientName,
        "save_beneficiary": saveBeneficiary,
      };
}
