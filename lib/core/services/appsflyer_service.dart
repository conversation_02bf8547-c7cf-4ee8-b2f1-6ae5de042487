import 'dart:async';
import 'dart:io';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/core.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppsFlyerService {
  static AppsFlyerService? _instance;
  static AppsFlyerService get instance => _instance ??= AppsFlyerService._();

  AppsFlyerService._();

  late AppsflyerSdk _appsflyerSdk;
  bool _isInitialized = false;

  final String _appsFlyerDevKey = dotenv.env["AF_DEV_KEY"] ?? '';
  final String _appleAppId = dotenv.env["AF_APP_ID"] ?? ''; // iOS only

  // For Android, we use the package name instead of the iOS App Store ID
  String get _appId {
    if (Platform.isIOS) {
      return _appleAppId;
    } else {
      // For Android, return empty string - A<PERSON><PERSON>lyer will use package name automatically
      return '';
    }
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Get package info for debugging
      final packageInfo = await PackageInfo.fromPlatform();

      // Debug configuration
      printty('  Platform: ${Platform.isIOS ? 'iOS' : 'Android'}');
      printty('  Package Name: ${packageInfo.packageName}');
      printty(
          '  Dev Key: ${_appsFlyerDevKey.isNotEmpty ? 'Found' : 'Missing'}');
      printty(
          ' App ID: ${_appId.isNotEmpty ? _appId : 'Using package name (Android)'}');

      // Important: Check if package name is registered in AppsFlyer
      if (Platform.isAndroid) {
        printty(
            '⚠️  IMPORTANT: Ensure "${packageInfo.packageName}" is registered in your AppsFlyer dashboard');
        printty(
            '   If you get 403 errors, this package name might not be configured in AppsFlyer');
      }
      // // Initialize stream controllers
      // _conversionDataController =
      //     StreamController<Map<String, dynamic>>.broadcast();
      // _deepLinkController = StreamController<Map<String, dynamic>>.broadcast();

      // Configure AppsFlyer options
      AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
        afDevKey: _appsFlyerDevKey,
        appId: _appId,
        showDebug: !EnvironmentConfig.isProduction,
        timeToWaitForATTUserAuthorization: 60,
        // disableAdvertisingIdentifier: false,
        // disableCollectASA: false,
      );

      // Initialize SDK
      _appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

      // Set up callbacks
      _setupCallbacks();

      // Initialize the SDK
      await _appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );

      _isInitialized = true;
      printty('✅ AppsFlyer SDK initialized successfully');
    } catch (e) {
      printty('❌ AppsFlyer initialization error: $e');
      rethrow;
    }
  }

  void _setupCallbacks() {
    // Conversion data callback
    _appsflyerSdk.onInstallConversionData((data) {
      printty('📊 Conversion Data: $data');

      // Process attribution data
      _processAttributionData(data);
    });

    // App open attribution callback
    _appsflyerSdk.onAppOpenAttribution((data) {
      printty('📱 App Open Attribution: $data');

      // Process attribution data for app open events
      _processAttributionData(data);
    });

    // Deep link callback
    _appsflyerSdk.onDeepLinking((data) {
      printty('🔗 Deep Link Data: $data');
      printty('🔗 Deep Link Status: ${data.status}');

      if (data.deepLink?.clickEvent != null) {
        // _deepLinkController?.add(data.deepLink!.clickEvent);
        // Process deep link through the dedicated handler
        _handleDeepLink(data.deepLink!.clickEvent);
      }
    });
  }

  /// Process attribution data and send to Attribution Service
  void _processAttributionData(Map<String, dynamic> data) {
    try {
      // Send attribution data to Attribution Service for processing
      AttributionService.instance.processAppsFlyerData(data);
    } catch (e) {
      printty('❌ Error processing attribution data: $e');
    }
  }

  /// Handle deep link data and route appropriately
  void _handleDeepLink(Map<String, dynamic> clickEvent) {
    try {
      // Use the dedicated DeepLinkHandler for processing
      final deepLinkData = DeepLinkHandler.instance.processDeepLink(clickEvent);

      // Log the deep link routing
      logEvent('deep_link_processed', {
        'type': deepLinkData.type.toString(),
        'has_referral': deepLinkData.referralCode != null,
        'target_route': deepLinkData.targetRoute,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      printty('❌ Error handling deep link: $e');
    }
  }

  // Start tracking
  Future<void> startTracking() async {
    if (!_isInitialized) {
      throw Exception('AppsFlyer SDK not initialized');
    }

    try {
      _appsflyerSdk.startSDK();
      printty('▶️ AppsFlyer tracking started');
    } catch (e) {
      printty('❌ Error starting AppsFlyer tracking: $e');
      rethrow;
    }
  }

  // Stop tracking
  Future<void> stopTracking() async {
    try {
      _appsflyerSdk.stop(true);
      printty('⏹️ AppsFlyer tracking stopped');
    } catch (e) {
      printty('❌ Error stopping AppsFlyer tracking: $e');
    }
  }

  // Get AppsFlyer ID
  Future<String?> getAppsFlyerId() async {
    try {
      return await _appsflyerSdk.getAppsFlyerUID();
    } catch (e) {
      printty('❌ Error getting AppsFlyer ID: $e');
      return null;
    }
  }

  // Basic event tracking
  Future<void> logEvent(
      String eventName, Map<String, dynamic>? eventValues) async {
    if (!_isInitialized) {
      printty('⚠️ AppsFlyer SDK not initialized');
      return;
    }

    try {
      printty('📤 Attempting to log event: $eventName');

      final res = await _appsflyerSdk.logEvent(eventName, eventValues);
      printty('✅ Event logged successfully: $eventName');
      printty('📝 Response: $res');
    } catch (e) {
      printty('❌ Error logging event "$eventName": $e');
      printty(
          '🔍 Check if AppsFlyer dev key is valid and app is properly configured');
    }
  }

  // Set user ID
  Future<void> setUserId(String userId) async {
    try {
      _appsflyerSdk.setCustomerUserId(userId);
      printty('👤 User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting user ID: $e');
    }
  }

  // Set additional user properties
  Future<void> setUserProperties(Map<String, dynamic> properties) async {
    try {
      _appsflyerSdk.setAdditionalData(properties);
      printty('👥 User properties set: $properties');
    } catch (e) {
      printty('❌ Error setting user properties: $e');
    }
  }

  // Revenue tracking
  Future<void> logRevenue(
    double revenue,
    String currency, {
    String? orderId,
    String? productId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    Map<String, dynamic> eventValues = {
      'af_revenue': revenue,
      'af_currency': currency,
    };

    if (orderId != null) eventValues['af_order_id'] = orderId;
    if (productId != null) eventValues['af_content_id'] = productId;
    if (additionalParameters != null) eventValues.addAll(additionalParameters);

    await logEvent('af_purchase', eventValues);
  }

  // Enhanced event tracking methods for key funnel events

  /// Track Create Account event
  Future<void> logCreateAccount({
    required String userId,
    String? method,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      if (method != null) 'registration_method': method,
      'event_category': 'user_acquisition',
      ...?additionalParameters,
    };

    await logEvent('create_account', eventValues);
  }

  /// Track Complete KYC event
  Future<void> logCompleteKyc({
    required String userId,
    String? kycLevel,
    String? verificationMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      if (kycLevel != null) 'kyc_level': kycLevel,
      if (verificationMethod != null) 'verification_method': verificationMethod,
      'event_category': 'user_verification',
      ...?additionalParameters,
    };

    await logEvent('complete_kyc', eventValues);
  }

  /// Track Deposit Money event
  Future<void> logDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      'af_revenue': amount,
      'af_currency': currency,
      if (paymentMethod != null) 'payment_method': paymentMethod,
      if (corridor != null) 'corridor': corridor,
      'event_category': 'financial_action',
      'event_type': 'deposit',
      ...?additionalParameters,
    };

    await logEvent('deposit_money', eventValues);
  }

  /// Track Send Money event
  Future<void> logSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final eventValues = <String, dynamic>{
      'af_customer_user_id': userId,
      'af_revenue': amount,
      'af_currency': fromCurrency,
      'from_currency': fromCurrency,
      'to_currency': toCurrency,
      'corridor': corridor,
      if (transactionId != null) 'transaction_id': transactionId,
      'event_category': 'financial_action',
      'event_type': 'money_transfer',
      ...?additionalParameters,
    };

    await logEvent('send_money', eventValues);
  }

  /// Check if AppsFlyer is initialized
  bool get isInitialized => _isInitialized;
}
