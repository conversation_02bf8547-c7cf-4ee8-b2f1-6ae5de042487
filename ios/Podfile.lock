PODS:
  - appsflyer_sdk (6.15.3):
    - Apps<PERSON>lyerFramework (= 6.15.3)
    - Flutter
  - AppsFlyerFramework (6.15.3):
    - AppsFlyerFramework/Main (= 6.15.3)
  - AppsFlyerFramework/Main (6.15.3)
  - customer_io (1.5.2):
    - CustomerIO/MessagingInApp (~> 2)
    - CustomerIO/Tracking (~> 2)
    - Flutter
  - CustomerIO/MessagingInApp (2.14.2):
    - CustomerIOMessagingInApp (= 2.14.2)
  - CustomerIO/Tracking (2.14.2):
    - CustomerIOTracking (= 2.14.2)
  - CustomerIOCommon (2.14.2)
  - CustomerIOMessagingInApp (2.14.2):
    - CustomerIOTracking (= 2.14.2)
  - CustomerIOTracking (2.14.2):
    - CustomerIOCommon (= 2.14.2)
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 18.0)
    - Flutter
  - FBAEMKit (18.0.1):
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (18.0.1):
    - FBAEMKit (= 18.0.1)
    - FBSDKCoreKit_Basics (= 18.0.1)
  - FBSDKCoreKit_Basics (18.0.1)
  - file_saver (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_contacts (0.0.1):
    - Flutter
  - freshchat_sdk (0.10.25):
    - Flutter
    - FreshchatSDK (= 6.3.7)
  - FreshchatSDK (6.3.7)
  - Mixpanel-swift (4.4.0):
    - Mixpanel-swift/Complete (= 4.4.0)
  - Mixpanel-swift/Complete (4.4.0)
  - mixpanel_flutter (2.4.0):
    - Flutter
    - Mixpanel-swift (= 4.4.0)
  - Onfido (32.6.1)
  - onfido_sdk (0.0.1):
    - Flutter
    - Onfido (~> 32.6.0)
  - open_file_ios (0.0.1):
    - Flutter
  - permission_handler_apple (9.3.0):
    - Flutter
  - store_redirect (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - appsflyer_sdk (from `.symlinks/plugins/appsflyer_sdk/ios`)
  - customer_io (from `.symlinks/plugins/customer_io/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - Flutter (from `Flutter`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - freshchat_sdk (from `.symlinks/plugins/freshchat_sdk/ios`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - onfido_sdk (from `.symlinks/plugins/onfido_sdk/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - AppsFlyerFramework
    - CustomerIO
    - CustomerIOCommon
    - CustomerIOMessagingInApp
    - CustomerIOTracking
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FreshchatSDK
    - Mixpanel-swift
    - Onfido

EXTERNAL SOURCES:
  appsflyer_sdk:
    :path: ".symlinks/plugins/appsflyer_sdk/ios"
  customer_io:
    :path: ".symlinks/plugins/customer_io/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  Flutter:
    :path: Flutter
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  freshchat_sdk:
    :path: ".symlinks/plugins/freshchat_sdk/ios"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  onfido_sdk:
    :path: ".symlinks/plugins/onfido_sdk/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  appsflyer_sdk: 0132c7cd33db42ce3daf878f02809a9f88756f9f
  AppsFlyerFramework: ad7ff0d22aa36c7f8cc4f71a5424e19b89ccb8ae
  customer_io: 6b1ff804eb2037461088d9d521d380ef850f619e
  CustomerIO: 51eaaea8fd5a6f769ac93dd0ba0991effd1b78e1
  CustomerIOCommon: f21661da1b67b7c2ee420d424cffacad3bbb28f6
  CustomerIOMessagingInApp: dc75d9c9e732a62739fd52a51e2c71eb278c006d
  CustomerIOTracking: 1be3d27b63026b2a5903b50174e06c4b30c940a0
  facebook_app_events: b8348b321ea43a541f2855e1e8e9994a4b7fd3cf
  FBAEMKit: b2ed182002dbcb65d5a60059c9693d322186cd00
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: 7f96852f2539bdb88ba8d47e5ab4ae70a6f8d691
  FBSDKCoreKit_Basics: 22d4c1a509ded4e45116f3bb167a14907550f62e
  file_saver: 503e386464dbe118f630e17b4c2e1190fa0cf808
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  flutter_contacts: edb1c5ce76aa433e20e6cb14c615f4c0b66e0983
  freshchat_sdk: 4078d62c19b5f0163c2398b6e27600d6654ba7b9
  FreshchatSDK: 52a5fbda55f5892cc96bc0f2cd80ffe188fa106c
  Mixpanel-swift: 478ff46d19de4a251244a9c9a582070d4bb94cf9
  mixpanel_flutter: c2e55a95a2ae23cf27c065115ecee6226e0e0718
  Onfido: 0b13df41f9af28b700c4f8375d022a6038cee671
  onfido_sdk: b4b4695efa586e46526e4e1b1fbe22083afccc47
  open_file_ios: 461db5853723763573e140de3193656f91990d9e
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e
  wakelock_plus: 373cfe59b235a6dd5837d0fb88791d2f13a90d56

PODFILE CHECKSUM: 9c46fd01abff66081b39f5fa5767b3f1d0b11d76

COCOAPODS: 1.16.2
