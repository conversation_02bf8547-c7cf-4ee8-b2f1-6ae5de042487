import 'package:korrency/core/core.dart';

class LanguageSelector extends StatelessWidget {
  const LanguageSelector({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.grayC3,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
        ),
        child: Row(
          children: [
            Text("EN",
                style: AppTypography.text14.copyWith(
                  color: AppColors.mainBlack,
                )),
            const XBox(8),
            Icon(
              Iconsax.arrow_down_1,
              color: AppColors.gray500,
              size: Sizer.height(20),
            )
          ],
        ),
      ),
    );
  }
}
