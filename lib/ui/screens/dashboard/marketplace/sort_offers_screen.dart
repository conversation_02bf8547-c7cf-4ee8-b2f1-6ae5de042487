import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SortOffersScreen extends StatelessWidget {
  const SortOffersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const ArrowBack(),
                  Text(
                    "Sort Offers by",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.gray000,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.gray000,
                    ),
                  ),
                ],
              ),
              const YBox(30),
              Text(
                "Sort Offers by",
                style: AppTypography.text20.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const YBox(40),
              InkWell(
                onTap: () {
                  BsWrapper.bottomSheet(
                      context: context, widget: const FilterOrderSheet());
                },
                child: ContainerWithBluewishBg(
                  padding: EdgeInsets.symmetric(
                    vertical: Sizer.height(20),
                    horizontal: Sizer.height(16),
                  ),
                  child: Text(
                    "Time (Most Recent)",
                    style: AppTypography.text16,
                  ),
                ),
              ),
              const YBox(20),
              ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.height(16),
                ),
                child: Text(
                  "Rate",
                  style: AppTypography.text16,
                ),
              ),
              const YBox(20),
              ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.height(16),
                ),
                child: Text(
                  "Amount",
                  style: AppTypography.text16,
                ),
              ),
              const YBox(20),
              ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.height(16),
                ),
                child: Text(
                  "Minimum Exchange",
                  style: AppTypography.text16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
