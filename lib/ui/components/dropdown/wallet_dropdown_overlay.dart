import 'package:korrency/core/core.dart';

class WalletDropdownOverlay extends StatelessWidget {
  const WalletDropdownOverlay({
    super.key,
    required this.onWalletSelected,
    required this.selectedWallet,
    this.maxHeight = 220,
  });

  final Function(Wallet) onWalletSelected;
  final Wallet? selectedWallet;
  final double maxHeight;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, walletVm, _) {
      return Container(
        constraints: BoxConstraints(maxHeight: Sizer.height(maxHeight)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.mainBlack.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: walletVm.walletList.isEmpty
            ? Container(
                height: 60,
                alignment: Alignment.center,
                child: Text(
                  'No wallets available',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.gray500,
                  ),
                ),
              )
            : ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(8),
                ),
                itemCount: walletVm.walletList.length,
                separatorBuilder: (_, __) => YBox(10),
                // separatorBuilder: (_, __) => const Divider(
                //   height: 1,
                //   color: AppColors.litGrey100,
                // ),
                itemBuilder: (ctx, i) {
                  final wallet = walletVm.walletList[i];
                  final isSelected = selectedWallet?.id == wallet.id;

                  return WalletDropdownItem(
                    wallet: wallet,
                    isSelected: isSelected,
                    onTap: () => onWalletSelected(wallet),
                  );
                },
              ),
      );
    });
  }
}

class WalletDropdownItem extends StatelessWidget {
  const WalletDropdownItem({
    super.key,
    required this.wallet,
    required this.isSelected,
    required this.onTap,
  });

  final Wallet wallet;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(12),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : Colors.transparent,
        ),
        child: Row(
          children: [
            // Currency flag
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: SizedBox(
                width: Sizer.width(24),
                height: Sizer.height(24),
                child: SvgPicture.network(
                  wallet.currency?.flag ?? '',
                  fit: BoxFit.cover,
                  placeholderBuilder: (context) => Container(
                    color: AppColors.litGrey100,
                    child: Icon(
                      Icons.account_balance_wallet,
                      size: Sizer.width(12),
                      color: AppColors.gray500,
                    ),
                  ),
                ),
              ),
            ),
            XBox(12),
            // Currency name and code
            Expanded(
              child: Text(
                wallet.currency?.name ?? 'Unknown',
                style: AppTypography.text14.copyWith(
                  color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
                ),
              ),
            ),
            XBox(8),
            // Balance
            Text(
              '${wallet.currency?.symbol ?? ''}${AppUtils.formatAmountDoubleString(wallet.balance ?? "0")}',
              style: AppTypography.text14.medium.copyWith(
                color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
