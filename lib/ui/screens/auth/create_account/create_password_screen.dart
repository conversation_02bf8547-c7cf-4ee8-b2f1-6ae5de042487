import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CreatePasswordScreen extends StatefulWidget {
  const CreatePasswordScreen({super.key});

  @override
  State<CreatePasswordScreen> createState() => _CreatePasswordScreenState();
}

class _CreatePasswordScreenState extends State<CreatePasswordScreen> {
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _confirmPasswordFocusNode = FocusNode();

  @override
  void dispose() {
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Create Password',
            onBackBtnTap: () {
              vm.clearEmailPasswordCredentials();
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Please choose a password for your account",
                          style: AppTypography.text15
                              .copyWith(color: AppColors.gray93),
                        ),
                        const YBox(40),
                        CustomTextField(
                          labelText: "Password",
                          showLabelHeader: true,
                          focusNode: _passwordFocusNode,
                          controller: vm.passwordC,
                          isPassword: true,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: SvgPicture.asset(
                              AppSvgs.passwordCheck,
                            ),
                          ),
                          onChanged: (val) {
                            vm.validatePassword(vm.passwordC.text);
                          },
                          onSubmitted: (p0) {
                            _passwordFocusNode.unfocus();
                          },
                        ),
                        const YBox(40),
                        CustomTextField(
                          labelText: "Confirm Password",
                          showLabelHeader: true,
                          focusNode: _confirmPasswordFocusNode,
                          controller: vm.confirmPassword,
                          isPassword: true,
                          borderRadius: Sizer.height(12),
                          errorText: _confirmPasswordFocusNode.hasFocus &&
                                  vm.confirmPassword.text != vm.passwordC.text
                              ? "Passwords do not match"
                              : null,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: SvgPicture.asset(
                              AppSvgs.passwordCheck,
                            ),
                          ),
                          onChanged: (val) {
                            vm.passwordMatch(
                                vm.passwordC.text, vm.confirmPassword.text);
                          },
                          onSubmitted: (p0) {
                            _passwordFocusNode.unfocus();
                          },
                        ),
                        const YBox(20),
                        Column(children: [
                          ValidationItemWidget(
                            label: "Must be up to 8 characters",
                            isValid: vm.validatorStatus.hasAtleast8Character,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                              label: "Must contain at least 1 number",
                              isValid: vm.validatorStatus.containsANumber),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "Must contain at least 1 uppercase letter",
                            isValid: vm.validatorStatus.containsUpperCase,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "Must contain at least 1 lowercase letter",
                            isValid: vm.validatorStatus.containsLowerCase,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "Must contain at least 1 special character",
                            isValid:
                                vm.validatorStatus.containsSpecialCharacter,
                          ),
                        ]),
                        const YBox(60),
                      ],
                    ),
                  ),
                ),
                CustomBtn.withChild(
                  onTap: () {
                    Navigator.pushReplacementNamed(
                        context, RoutePath.referralCodeScreen);
                  },
                  online: vm.isMatchingPassword,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(isOnline: vm.isMatchingPassword),
                ),
                const YBox(30),
              ],
            ),
          ),
        ),
      );
    });
  }
}
