<svg width="207" height="146" viewBox="0 0 207 146" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_16821_41532)">
<path d="M199.236 138.489C203.655 138.489 207.296 134.889 206.658 130.517C206 126.01 204.785 121.592 203.035 117.365C200.261 110.668 196.195 104.583 191.069 99.4568C185.943 94.331 179.858 90.265 173.161 87.4909C166.463 84.7169 159.285 83.2891 152.036 83.2891C144.787 83.2891 137.609 84.7169 130.912 87.4909C124.215 90.265 118.13 94.331 113.004 99.4568C107.878 104.583 103.812 110.668 101.038 117.365C99.2875 121.592 98.0729 126.01 97.4151 130.517C96.777 134.889 100.418 138.489 104.836 138.489L152.036 138.489H199.236Z" fill="#487CEA"/>
</g>
<g filter="url(#filter1_dii_16821_41532)">
<path d="M136.509 141.001C140.927 141.001 144.556 137.408 144.054 133.018C143.312 126.533 141.669 120.17 139.161 114.115C135.631 105.592 130.456 97.8469 123.932 91.3232C117.408 84.7994 109.663 79.6245 101.14 76.0939C92.6161 72.5633 83.4805 70.7461 74.2545 70.7461C65.0286 70.7461 55.893 72.5633 47.3693 76.0939C38.8456 79.6245 31.1008 84.7994 24.5771 91.3232C18.0533 97.8469 12.8784 105.592 9.34781 114.115C6.84 120.17 5.19667 126.533 4.455 133.018C3.95296 137.408 7.58172 141.001 12 141.001L74.2545 141.001H136.509Z" fill="#F3FAFF"/>
</g>
<g filter="url(#filter2_dii_16821_41532)">
<circle cx="74.2546" cy="33.1091" r="30.1091" fill="#F3FAFF"/>
</g>
<g filter="url(#filter3_ii_16821_41532)">
<circle cx="154.546" cy="48.1651" r="25.0909" fill="#487CEA"/>
</g>
<defs>
<filter id="filter0_ii_16821_41532" x="91.3423" y="77.2891" width="117.314" height="63.1251" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-6" dy="-6"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.407843 0 0 0 0 0.72549 0 0 0 0.82 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_16821_41532"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92592" dy="1.92592"/>
<feGaussianBlur stdDeviation="1.44444"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.644444 0 0 0 0 0.957202 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_16821_41532" result="effect2_innerShadow_16821_41532"/>
</filter>
<filter id="filter1_dii_16821_41532" x="0.954274" y="67.758" width="146.601" height="77.8479" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.15147"/>
<feGaussianBlur stdDeviation="1.72721"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.710026 0 0 0 0 0.71778 0 0 0 0 0.758485 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_16821_41532"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_16821_41532" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.98812" dy="-2.98812"/>
<feGaussianBlur stdDeviation="1.49406"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.359191 0 0 0 0 0.655925 0 0 0 0 0.870801 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_16821_41532"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.49406" dy="1.49406"/>
<feGaussianBlur stdDeviation="1.12054"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.979145 0 0 0 0 0.99749 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_16821_41532" result="effect3_innerShadow_16821_41532"/>
</filter>
<filter id="filter2_dii_16821_41532" x="40.6911" y="0.0118818" width="67.1271" height="67.8128" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.15147"/>
<feGaussianBlur stdDeviation="1.72721"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.710026 0 0 0 0 0.71778 0 0 0 0 0.758485 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_16821_41532"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_16821_41532" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.98812" dy="-2.98812"/>
<feGaussianBlur stdDeviation="1.49406"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.359191 0 0 0 0 0.655925 0 0 0 0 0.870801 0 0 0 0.17 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_16821_41532"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.49406" dy="1.49406"/>
<feGaussianBlur stdDeviation="1.12054"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.979145 0 0 0 0 0.99749 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_16821_41532" result="effect3_innerShadow_16821_41532"/>
</filter>
<filter id="filter3_ii_16821_41532" x="125.455" y="19.0742" width="56.1076" height="56.1095" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="-4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.196078 0 0 0 0 0.407843 0 0 0 0 0.72549 0 0 0 0.82 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_16821_41532"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.92592" dy="1.92592"/>
<feGaussianBlur stdDeviation="1.44444"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.644444 0 0 0 0 0.957202 0 0 0 0 1 0 0 0 0.65 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_16821_41532" result="effect2_innerShadow_16821_41532"/>
</filter>
</defs>
</svg>
