import 'package:korrency/core/core.dart';
import 'package:korrency/core/i18n/translation_helper.dart';
import 'package:korrency/ui/components/components.dart';

class PreferredLanguageScreen extends StatefulWidget {
  const PreferredLanguageScreen({super.key});

  @override
  State<PreferredLanguageScreen> createState() =>
      _PreferredLanguageScreenState();
}

class _PreferredLanguageScreenState extends State<PreferredLanguageScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageVM>().ensureInitialized(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthUserVM, LanguageVM>(
        builder: (context, authVm, langVm, _) {
      return BusyOverlay(
        show: authVm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: TranslationHelper.tr(
              'menu.preferences.language.title',
              fallback: 'Preferred Language',
            ),
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  TranslationHelper.tr(
                    'menu.preferences.language.subtitle',
                    fallback: "Please select your preferred language below",
                  ),
                  style: AppTypography.text16.copyWith(color: AppColors.gray93),
                ),
                const YBox(24),
                Expanded(
                  child: Container(
                    width: Sizer.screenWidth,
                    color: AppColors.white,
                    child: ListView(
                      padding: EdgeInsets.only(
                        top: Sizer.height(24),
                        bottom: Sizer.height(24),
                      ),
                      children: [
                        LangListTile(
                          langCode: 'EN',
                          langName: 'English',
                          isSelected: langVm.isSelected('EN'),
                          onTap: () => langVm.setLanguage(context, 'en'),
                        ),
                        YBox(16),
                        LangListTile(
                          langCode: 'FR',
                          langName: 'Français',
                          isSelected: langVm.isSelected('FR'),
                          onTap: () => langVm.setLanguage(context, 'fr'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class LangListTile extends StatelessWidget {
  const LangListTile({
    super.key,
    required this.langCode,
    required this.langName,
    this.isSelected = false,
    required this.onTap,
  });

  final String langCode;
  final String langName;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(16),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : AppColors.transparent,
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Text(
                    langCode,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray51,
                    ),
                  ),
                  const XBox(16),
                  Text(
                    langName,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray51,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected) Icon(Icons.check)
          ],
        ),
      ),
    );
  }
}
