import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateFeedbackModal extends StatefulWidget {
  const RateFeedbackModal({
    super.key,
    required this.rating,
  });

  final int rating;

  @override
  State<RateFeedbackModal> createState() => _RateFeedbackModalState();
}

class _RateFeedbackModalState extends State<RateFeedbackModal> {
  final _feedbackC = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _feedbackC.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.only(
          left: Sizer.width(24),
          right: Sizer.width(24),
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(24),
            Text(
              'Your feedback helps us improve Korrency for everyone',
              textAlign: TextAlign.center,
              style: FontTypography.text20.semiBold,
            ),
            YBox(4),
            Text(
              'Take a moment to tell us how we can improve',
              textAlign: TextAlign.center,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray93,
              ),
            ),
            const YBox(24),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Your Feedback',
                textAlign: TextAlign.center,
                style: FontTypography.text14.withCustomColor(
                  AppColors.gray51,
                ),
              ),
            ),
            const YBox(6),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grayAB),
                borderRadius: BorderRadius.circular(Sizer.height(12)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: Sizer.width(12),
                      top: Sizer.height(10),
                    ),
                    child: Icon(
                      Iconsax.message_edit,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  Expanded(
                    child: CustomTextField(
                      controller: _feedbackC,
                      focusNode: _focusNode,
                      hideBorder: true,
                      hintText: "Start typing here...",
                      borderRadius: Sizer.height(12),
                      maxLines: 3,
                      contentPadding: EdgeInsets.only(
                        left: Sizer.width(8),
                        top: Sizer.height(20),
                      ),
                      onChanged: (val) => setState(() {}),
                      onSubmitted: (p0) {
                        _focusNode.unfocus();
                      },
                    ),
                  ),
                ],
              ),
            ),
            const YBox(28),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(20),
              online: _feedbackC.text.trim().isNotEmpty,
              isLoading: context.watch<RatingVm>().isBusy,
              onTap: () async {
                _focusNode.unfocus();
                final res = await context.read<RatingVm>().submitReview(
                      userRatingPromptId: context
                              .read<RatingVm>()
                              .eligibilityModel
                              ?.triggerId ??
                          0,
                      rating: widget.rating,
                      feedback: _feedbackC.text.trim(),
                    );
                handleApiResponse(
                    response: res,
                    showErrorToast: false,
                    onError: () {
                      BsWrapper.bottomSheet(
                        context: context,
                        widget: RateErrorModal(
                          message: res.message,
                        ),
                      );
                    },
                    onSuccess: () {
                      if (context.mounted) {
                        Navigator.pop(context);
                        Navigator.pop(context);
                        BsWrapper.bottomSheet(
                          context: context,
                          widget: const RateSuccessModal(),
                        );
                      }
                    });
              },
              text: "Submit",
            ),
            const YBox(36),
          ],
        ),
      ),
    );
  }
}
