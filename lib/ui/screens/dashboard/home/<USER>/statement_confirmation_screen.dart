import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class StatementConfirmScreenArgs {
  const StatementConfirmScreenArgs({
    required this.message,
    required this.title,
  });
  final String message;
  final String title;
}

class StatementConfirmationScreen extends StatelessWidget {
  const StatementConfirmationScreen({
    Key? key,
    required this.args,
  }) : super(key: key);

  final StatementConfirmScreenArgs args;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                CustomHeader(
                  onBackBtnTap: () {
                    Navigator.pushNamed(context, RoutePath.dashboardNav);
                  },
                ),
                const Y<PERSON><PERSON>(80),
                imageHelper(
                  AppImages.envelope,
                  height: Sizer.height(280),
                ),
                const YBox(40),
                Text(
                  args.title,
                  style: AppTypography.text20b,
                ),
                const YBox(4),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Text(
                    args.message,
                    textAlign: TextAlign.center,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.textBlack800,
                    ),
                  ),
                ),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () {
                    Navigator.pushNamedAndRemoveUntil(
                        context, RoutePath.dashboardNav, (_) => false);
                  },
                  text: "Home",
                ),
                const YBox(60),
              ],
            ),
          ),
        ),
      );
    });
  }
}
