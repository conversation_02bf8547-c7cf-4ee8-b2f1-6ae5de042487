import 'package:korrency/core/services/storage_service.dart';
import 'package:korrency/core/utils/storage_keys.dart';

class WalletMaskingService {
  /// Generate a wallet-specific storage key
  static String _getWalletKey(String walletType) {
    return '${StorageKey.walletVisibilityMasked}_$walletType';
  }

  /// Get the current wallet masking state for a specific wallet
  /// Returns false (unmasked) by default
  static Future<bool> getWalletMaskingState(String walletType) async {
    final key = _getWalletKey(walletType);
    return await StorageService.getBoolItem(key) ?? false;
  }

  /// Set the wallet masking state for a specific wallet
  static Future<void> setWalletMaskingState(String walletType, bool isMasked) async {
    final key = _getWalletKey(walletType);
    await StorageService.storeBoolItem(key, isMasked);
  }

  /// Toggle the wallet masking state for a specific wallet and return the new state
  static Future<bool> toggleWalletMaskingState(String walletType) async {
    final currentState = await getWalletMaskingState(walletType);
    final newState = !currentState;
    await setWalletMaskingState(walletType, newState);
    return newState;
  }

  /// Clear the wallet masking state for a specific wallet (reset to default unmasked)
  static Future<void> clearWalletMaskingState(String walletType) async {
    final key = _getWalletKey(walletType);
    await StorageService.removeBoolItem(key);
  }

  /// Clear all wallet masking states
  static Future<void> clearAllWalletMaskingStates() async {
    const walletTypes = ['CAD', 'GBP', 'NGN', 'OTHER'];
    for (final walletType in walletTypes) {
      await clearWalletMaskingState(walletType);
    }
  }
}