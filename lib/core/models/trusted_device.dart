import 'dart:convert';

TrustedDevice trustedDeviceFromJson(String str) =>
    TrustedDevice.fromJson(json.decode(str));

String trustedDeviceToJson(TrustedDevice data) => json.encode(data.toJson());

class TrustedDevice {
  Device? primaryDevice;
  List<Device>? otherDevices;

  TrustedDevice({
    this.primaryDevice,
    this.otherDevices,
  });

  factory TrustedDevice.fromJson(Map<String, dynamic> json) => TrustedDevice(
        primaryDevice: json["primary_device"] == null
            ? null
            : Device.fromJson(json["primary_device"]),
        otherDevices: json["other_devices"] == null
            ? []
            : List<Device>.from(
                json["other_devices"]!.map((x) => Device.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "primary_device": primaryDevice?.toJson(),
        "other_devices": otherDevices == null
            ? []
            : List<dynamic>.from(otherDevices!.map((x) => x.toJson())),
      };
}

class Device {
  int? id;
  String? deviceType;
  String? deviceName;
  String? appVersion;
  bool? isActive;
  DateTime? lastActive;

  Device({
    this.id,
    this.deviceType,
    this.deviceName,
    this.appVersion,
    this.isActive,
    this.lastActive,
  });

  factory Device.fromJson(Map<String, dynamic> json) => Device(
        id: json["id"],
        deviceType: json["device_type"],
        deviceName: json["device_name"],
        appVersion: json["app_version"],
        isActive: json["is_active"],
        lastActive: json["last_active"] == null
            ? null
            : DateTime.parse(json["last_active"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "device_type": deviceType,
        "device_name": deviceName,
        "app_version": appVersion,
        "is_active": isActive,
        "last_active": lastActive?.toIso8601String(),
      };
}
