// ignore_for_file: constant_identifier_names

import 'dart:convert';

List<OccupationData> occupationDataFromJson(String str) =>
    List<OccupationData>.from(
        json.decode(str).map((x) => OccupationData.fromJson(x)));

String occupationDataToJson(List<OccupationData> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OccupationData {
  int? id;
  String? name;
  Level? level;
  int? points;
  DateTime? createdAt;
  DateTime? updatedAt;

  OccupationData({
    this.id,
    this.name,
    this.level,
    this.points,
    this.createdAt,
    this.updatedAt,
  });

  factory OccupationData.fromJson(Map<String, dynamic> json) => OccupationData(
        id: json["id"],
        name: json["name"],
        level: levelValues.map[json["level"]]!,
        points: json["points"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "level": levelValues.reverse[level],
        "points": points,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is OccupationData &&
        other.id == id &&
        other.name == name &&
        other.level == level &&
        other.points == points &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        level.hashCode ^
        points.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}

enum Level { HIGH, LOW, MEDIUM }

final levelValues =
    EnumValues({"High": Level.HIGH, "Low": Level.LOW, "Medium": Level.MEDIUM});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
