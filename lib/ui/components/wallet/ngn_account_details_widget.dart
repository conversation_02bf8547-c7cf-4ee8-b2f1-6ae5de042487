import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class NgnAccountDetailsWidget extends StatelessWidget {
  const NgnAccountDetailsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          color: AppColors.blueBFF,
          borderRadius: BorderRadius.circular(Sizer.radius(10)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: NgnColText(
                    title: "Account number",
                    value:
                        vm.nairaWallet?.virtualAccounts?[0].accountNumber ?? "",
                  ),
                ),
                CopyBtn(
                  text: vm.nairaWallet?.virtualAccounts?[0].accountNumber ?? "",
                ),
              ],
            ),
            YBox(20),
            NgnColText(
              title: "Account name",
              value: vm.nairaWallet?.virtualAccounts?[0].accountName ?? "",
            ),
            YBox(20),
            NgnColText(
              title: "Bank name",
              value: vm.nairaWallet?.virtualAccounts?[0].bankName ?? "",
            ),
            YBox(28),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(Sizer.radius(14)),
              height: Sizer.height(40),
              isOutline: true,
              onlineColor: AppColors.white,
              textColor: AppColors.primaryBlue,
              onTap: () {
                Share.share(
                  "Account Number: ${vm.nairaWallet?.virtualAccounts?[0].accountNumber} \nAccount Name: ${vm.nairaWallet?.virtualAccounts?[0].accountName} \nBank Name: ${vm.nairaWallet?.virtualAccounts?[0].bankName}",
                  subject: "Korrency",
                );
              },
              text: "Share details",
            )
          ],
        ),
      );
    });
  }
}
