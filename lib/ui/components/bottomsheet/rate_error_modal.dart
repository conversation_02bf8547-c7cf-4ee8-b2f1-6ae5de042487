import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateErrorModal extends StatefulWidget {
  const RateErrorModal({
    super.key,
    this.message,
  });

  final String? message;

  @override
  State<RateErrorModal> createState() => _RateErrorModalState();
}

class _RateErrorModalState extends State<RateErrorModal>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _starAnimationController;

  // Animations
  late Animation<double> _starRotationAnimation;
  late Animation<double> _starScaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Star animation controller
    _starAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Star rotation animation
    _starRotationAnimation = Tween<double>(
      begin: -0.2,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));

    // Star scale animation
    _starScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _startInitialAnimations();
  }

  void _startInitialAnimations() {
    // Start star animation with slight delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _starAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _starAnimationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(80),
                Text(
                  widget.message ??
                      'Couldn\'t send feedback. \nPlease try again',
                  textAlign: TextAlign.center,
                  style: FontTypography.text20.semiBold,
                ),
                const YBox(28),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: "Try Again",
                ),
                const YBox(14),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pop(context);
                    Navigator.pop(context);
                  },
                  child: Text(
                    'Maybe Later',
                    textAlign: TextAlign.center,
                    style: FontTypography.text14.medium.withCustomColor(
                      AppColors.primaryBlue,
                    ),
                  ),
                ),
                const YBox(36),
              ],
            ),
          ),
          Positioned(
            top: -Sizer.height(62),
            right: 0,
            left: 0,
            child: AnimatedBuilder(
                animation: Listenable.merge([
                  _starAnimationController,
                  _starRotationAnimation,
                  _starScaleAnimation,
                ]),
                builder: (context, child) {
                  return Transform.scale(
                      scale: _starScaleAnimation.value,
                      child: Transform.rotate(
                        angle: _starRotationAnimation.value,
                        child: imageHelper(
                          AppImages.ratingCancel,
                          height: Sizer.height(124),
                          width: Sizer.width(124),
                        ),
                      ));
                }),
          )
        ],
      ),
    );
  }
}
