import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class SendMoneyPinSheet extends StatefulWidget {
  const SendMoneyPinSheet({
    super.key,
    required this.onTransfer,
  });

  final Function(String pin) onTransfer;

  @override
  State<SendMoneyPinSheet> createState() => _SendMoneyPinSheetState();
}

class _SendMoneyPinSheetState extends State<SendMoneyPinSheet> {
  final FocusNode pinFocusNode = FocusNode();
  String _pin = '';

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
  }

  @override
  dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.63,
        child: Consumer<SendMoneyVM>(builder: (context, vm, _) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(20),
              const AuthTextSubTitle(
                title: "Confirm PIN",
                subtitle: "To authorize this transaction, enter \nyour PIN",
              ),
              const YBox(40),
              Container(
                alignment: Alignment.center,
                child: Pinput(
                  defaultPinTheme: PinInputTheme.defaultPinTheme(),
                  // focusedPinTheme: PinInputTheme.focusFillPinTheme(),
                  followingPinTheme: PinInputTheme.followPinTheme(),
                  length: 4,
                  focusNode: pinFocusNode,
                  showCursor: true,
                  obscureText: true,
                  obscuringWidget: Container(
                    padding: EdgeInsets.only(top: Sizer.height(8)),
                    child: Text('*', style: AppTypography.text36),
                  ),
                  onChanged: (pin) => setState(() {
                    _pin = pin;
                  }),
                  onCompleted: (pin) {
                    pinFocusNode.unfocus();
                    Navigator.pop(context);
                    widget.onTransfer(pin);
                  },
                ),
              ),
              const Spacer(),
              CustomBtn.solid(
                onTap: () {
                  pinFocusNode.unfocus();
                  Navigator.pop(context);
                  widget.onTransfer(_pin);
                },
                online: _pin.length == 4,
                isLoading: vm.isBusy,
                text: "Continue",
              ),
              const YBox(60),
            ],
          );
        }),
      ),
    );
  }
}
