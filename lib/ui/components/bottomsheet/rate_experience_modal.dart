import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateExperienceModal extends StatefulWidget {
  const RateExperienceModal({super.key});

  @override
  State<RateExperienceModal> createState() => _RateExperienceModalState();
}

class _RateExperienceModalState extends State<RateExperienceModal>
    with TickerProviderStateMixin {
  final _emojis = [
    AppImages.emoji1,
    AppImages.emoji2,
    AppImages.emoji3,
    AppImages.emoji4,
    AppImages.emoji5,
  ];

  int _selectedEmoji = -1;
  int? _hoveredEmoji;
  bool _isProcessing = false;

  // Animation controllers
  late AnimationController _modalAnimationController;
  late AnimationController _starAnimationController;
  late AnimationController _emojiScaleController;
  late AnimationController _pulseController;

  // Animations
  late Animation<double> _modalSlideAnimation;
  late Animation<double> _starRotationAnimation;
  late Animation<double> _starScaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startInitialAnimations();
  }

  void _initializeAnimations() {
    // Modal animation controller
    _modalAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Star animation controller
    _starAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Emoji scale animation controller
    _emojiScaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Pulse animation controller
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Modal slide up animation
    _modalSlideAnimation = Tween<double>(
      begin: 100.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _modalAnimationController,
      curve: Curves.elasticOut,
    ));

    // Star rotation animation
    _starRotationAnimation = Tween<double>(
      begin: -0.2,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));

    // Star scale animation
    _starScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _startInitialAnimations() {
    // Start modal animation
    _modalAnimationController.forward();

    // Start star animation with slight delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _starAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _modalAnimationController.dispose();
    _starAnimationController.dispose();
    _emojiScaleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleEmojiTap(int index) async {
    final navCtx = NavigatorKeys.appNavigatorKey.currentContext!;
    if (_isProcessing) return;

    setState(() {
      _selectedEmoji = index;
      _isProcessing = true;
    });

    // Trigger emoji animation
    await _emojiScaleController.forward();

    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Brief pause for visual feedback
    await Future.delayed(const Duration(milliseconds: 400));

    await _emojiScaleController.reverse();

    if (mounted) {
      if (index < 3) {
        // Negative rating - show feedback modal
        BsWrapper.bottomSheet(
          context: context,
          widget: RateFeedbackModal(rating: index + 1),
        );
      } else {
        // Positive rating - request store review
        await RatingService.requestReview();

        // Uncomment when ready to integrate with backend

        await navCtx.read<RatingVm>().submitReview(
              userRatingPromptId:
                  navCtx.read<RatingVm>().eligibilityModel?.triggerId ?? 0,
              rating: index + 1,
              feedback: "",
            );
        if (context.mounted) {
          Navigator.pop(navCtx);
          Navigator.pop(navCtx);
          BsWrapper.bottomSheet(
            context: navCtx,
            widget: const RateSuccessModal(),
          );
        }
      }
    }

    setState(() {
      _isProcessing = false;
    });
  }

  void _handleEmojiHover(int? index) {
    if (_isProcessing) return;

    setState(() {
      _hoveredEmoji = index;
    });

    if (index != null && index != _selectedEmoji) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  // Color _getEmojiColor(int index) {
  //   final colors = [
  //     Colors.red.shade400,
  //     Colors.orange.shade400,
  //     Colors.yellow.shade600,
  //     Colors.lightGreen.shade400,
  //     Colors.green.shade400,
  //   ];
  //   return colors[index];
  // }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _modalAnimationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _modalSlideAnimation.value),
          child: Padding(
            padding: EdgeInsets.all(Sizer.radius(12)),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  decoration: BoxDecoration(
                    color: AppColors.bgWhite,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const YBox(80),

                      // Animated title
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 800),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 20 * (1 - value)),
                              child: Text(
                                'How would you rate your \nexperience',
                                textAlign: TextAlign.center,
                                style: FontTypography.text22.semiBold,
                              ),
                            ),
                          );
                        },
                      ),

                      YBox(4),

                      // Animated subtitle
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1000),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Transform.translate(
                              offset: Offset(0, 15 * (1 - value)),
                              child: Text(
                                'We would love to hear from you',
                                textAlign: TextAlign.center,
                                style: FontTypography.text16.withCustomColor(
                                  AppColors.gray93,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      YBox(20),

                      // Animated emoji row
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1200),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: List.generate(
                                _emojis.length,
                                (i) => _buildAnimatedEmoji(i, value),
                              ),
                            ),
                          );
                        },
                      ),

                      YBox(_isProcessing ? 16 : 50),

                      // Processing indicator
                      if (_isProcessing)
                        TweenAnimationBuilder<double>(
                          duration: const Duration(milliseconds: 300),
                          tween: Tween(begin: 0.0, end: 1.0),
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Padding(
                                padding: EdgeInsets.only(
                                  bottom: Sizer.height(16),
                                ),
                                child: const Column(
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                    YBox(10),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),

                // Animated star
                Positioned(
                  top: -Sizer.height(62),
                  right: 0,
                  left: 0,
                  child: AnimatedBuilder(
                    animation: Listenable.merge([
                      _starAnimationController,
                      _starRotationAnimation,
                      _starScaleAnimation,
                    ]),
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _starScaleAnimation.value,
                        child: Transform.rotate(
                          angle: _starRotationAnimation.value,
                          child: imageHelper(
                            AppImages.star,
                            height: Sizer.height(124),
                            width: Sizer.width(124),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedEmoji(int index, double animationValue) {
    final isSelected = _selectedEmoji == index;
    final isHovered = _hoveredEmoji == index;
    final isActive = isSelected || isHovered;

    return MouseRegion(
      onEnter: (_) => _handleEmojiHover(index),
      onExit: (_) => _handleEmojiHover(null),
      child: GestureDetector(
        onTap: () => _handleEmojiTap(index),
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _emojiScaleController,
            _pulseController,
          ]),
          builder: (context, child) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: EdgeInsets.all(Sizer.radius(isActive ? 8 : 6)),
              decoration: BoxDecoration(
                color: _getEmojiBackgroundColor(index, isActive),
                borderRadius: BorderRadius.circular(100),
              ),
              child: imageHelper(
                _emojis[index],
                height: Sizer.height(isActive ? 42 : 38),
                width: Sizer.width(isActive ? 42 : 38),
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getEmojiBackgroundColor(int index, bool isActive) {
    if (isActive) {
      return AppColors.blueE5;
    }
    return _selectedEmoji == index ? AppColors.blueE5 : AppColors.blueFD;
  }
}
