import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    focusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: 'Forgot Password',
            ),
            body: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: Column(
                children: [
                  Text(
                    "Enter your email address to reset your password, please ensure it’s the same one you used to create your Korrency Account",
                    style:
                        AppTypography.text16.copyWith(color: AppColors.gray93),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const YBox(36),
                        CustomTextField(
                          labelText: "Email",
                          focusNode: focusNode,
                          showLabelHeader: true,
                          controller: vm.emailC,
                          keyboardType: KeyboardType.email,
                          borderRadius: Sizer.height(12),
                          labelSize: 14,
                          prefixIcon: Icon(
                            Iconsax.sms,
                            color: AppColors.gray500,
                            size: Sizer.height(20),
                          ),
                          errorText:
                              vm.emailC.text.isNotEmpty && !vm.isValidEmail
                                  ? "Invalid Email"
                                  : null,
                          onChanged: (_) => vm.emailIsValid(),
                          onSubmitted: (p0) {
                            focusNode.unfocus();
                          },
                        ),
                      ],
                    ),
                  ),
                  CustomBtn.solid(
                    onTap: () async {
                      focusNode.unfocus();
                      // _requestForgotPasswordOtp();
                      final res = await BsWrapper.bottomSheet(
                          context: context,
                          widget: ConfirmEmailModal(email: vm.emailC.text));

                      if (res == true) {
                        _requestForgotPasswordOtp();
                      }
                    },
                    online: vm.isValidEmail,
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    text: "Send OTP",
                  ),
                  const YBox(20),
                  InkWell(
                    onTap: () {
                      vm.clearData();
                      Navigator.pushNamed(context, RoutePath.createAcctScreen);
                    },
                    child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: "Don’t have an account yet? ",
                          style: AppTypography.text16.copyWith(
                            color: AppColors.gray500,
                            fontFamily: AppFont.outfit.family,
                            height: 2,
                          ),
                          children: [
                            TextSpan(
                              text: "Create Account",
                              style: AppTypography.text16.copyWith(
                                color: AppColors.primaryBlue90,
                                fontFamily: AppFont.outfit.family,
                                height: 1.2,
                              ),
                            ),
                          ],
                        )),
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  _requestForgotPasswordOtp() async {
    focusNode.unfocus();
    final res = await context.read<PasskeyResetVM>().forgotPasswordOtpRequest();

    handleApiResponse(
      response: res,
      onSuccess: () {
        Navigator.pushReplacementNamed(
          context,
          RoutePath.passwordResetScreen,
        );
      },
    );
  }
}
