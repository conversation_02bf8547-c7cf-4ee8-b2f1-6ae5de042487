import 'package:korrency/core/core.dart';

class AccountNameWidget extends StatelessWidget {
  const AccountNameWidget({
    super.key,
    required this.accountName,
  });

  final String accountName;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: Sizer.height(20),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(12),
              vertical: Sizer.height(8),
            ),
            decoration: BoxDecoration(
              color: AppColors.greenEF,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  size: Sizer.radius(18),
                  color: AppColors.baseGreen,
                ),
                const XBox(4),
                Text(
                  accountName.toUpperCase(),
                  style: AppTypography.text16.copyWith(
                    color: AppColors.baseGreen,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
