import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:korrency/core/core.dart';

class WalletVM extends BaseVM {
  List<Wallet> _walletList = [];
  List<Wallet> get walletList => _walletList;

  // get primary wallet currency id for
  // frequent beneficiary
  int get primaryWalletCurrecyId =>
      _walletList.isNotEmpty == true ? _walletList.first.currency?.id ?? 0 : 0;

  Wallet? get nairaWallet =>
      _walletList.firstWhere((element) => element.currency?.code == "NGN");
  Wallet? get cadWallet =>
      _walletList.firstWhere((element) => element.currency?.code == "CAD");

  // Return the first inactive wallet _walletList
  Wallet? get inactiveWallet => _walletList.firstWhere(
        (element) => element.id != _activeWallet?.id,
        orElse: () {
          return _walletList.first;
        },
      );

  // return available currency in recipientCurrencies thats not in the active wallet
  Currency? get toCurrency {
    return _activeWallet?.currency?.recipientCurrencies?.firstWhere(
      (element) => element.code != _activeWallet?.currency?.code,
    );
  }

  // get wallet from code
  Wallet? getWalletFromCode(String currencyCode) {
    return walletList.firstWhereOrNull(
      (element) =>
          element.currency?.code?.toLowerCase() == currencyCode.toLowerCase(),
    );
  }

  Wallet? getWalletType(String offerType) {
    return offerType == OfferConst.sellOffer ? nairaWallet : cadWallet;
  }

  // Balance deplayed on the home screen
  String homeBalance(Wallet? wallet, [bool symbol = false]) {
    var balance = wallet?.balance?.split(".")[0] ?? "0";
    return "${symbol ? wallet?.currency?.symbol ?? "" : ""}${AppUtils.formatAmountString(balance)}";
  }

  // Decimal Part
  String decimalBalance(Wallet? wallet) {
    var balance = wallet?.balance?.split(".")[1] ?? "0";
    return balance;
  }

  Wallet? _activeWallet;
  Wallet? get activeWallet => _activeWallet;
  setActiveWallet(Wallet wallet) {
    _activeWallet = wallet;
    reBuildUI();
  }

  bool _showWalletVisibility = true;
  bool get toggleWalletVisibility => _showWalletVisibility;

  // Initialize wallet visibility state from storage
  Future<void> initializeWalletVisibility() async {
    final storedValue =
        await StorageService.getBoolItem(StorageKey.walletVisibilityMasked);
    _showWalletVisibility = storedValue ?? true; // Default to true (unmasked)
    reBuildUI();
  }

  setWalletVisibility() async {
    _showWalletVisibility = !_showWalletVisibility;
    // Persist the state to storage
    await StorageService.storeBoolItem(
        StorageKey.walletVisibilityMasked, _showWalletVisibility);
    reBuildUI();
  }

  Future<ApiResponse> getWallets({String? busyState}) async {
    return await performApiCall(
      url: "/wallets",
      method: apiService.getWithAuth,
      busyObjectName: busyState,
      onSuccess: (data) {
        var wallets = walletFromJson(json.encode(data["data"]));

        // If the _activeWallet is null, set it to the first wallet in the list
        if (_activeWallet == null) {
          _activeWallet = wallets.first;
        } else {
          // Otherwise, search for the wallet with the same currency code as the current active wallet
          var activeWalletCurrencyCode = _activeWallet?.currency?.code;
          var activeWallet = wallets.firstWhere(
            (element) => element.currency?.code == activeWalletCurrencyCode,
            orElse: () => wallets.first,
          );
          _activeWallet = activeWallet;
        }

        _walletList = wallets;

        // Initialize wallet visibility from storage when wallets are loaded
        initializeWalletVisibility();

        printty("_activeWallet: ${_activeWallet?.currency?.code ?? "null"}");
        return ApiResponse<List<Wallet>>(success: true, data: wallets);
      },
    );
  }

  Future<ApiResponse> createVirtualAccount({
    required int currencyId,
    String? bvn,
  }) async {
    return await performApiCall(
      url: "/virtual-accounts/create",
      method: apiService.postWithAuth,
      busyObjectName: createState,
      body: {"currency_id": currencyId},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  void resetData() {
    printty("Resetting Wallet VM data ");

    _walletList = [];
    _activeWallet = null;
    // Initialize wallet visibility from storage instead of hardcoding to false
    initializeWalletVisibility();
  }
}
