import 'package:korrency/core/core.dart';

class BuildColText extends StatelessWidget {
  const BuildColText({
    super.key,
    required this.title,
    required this.subTitle,
  });

  final String title;
  final String subTitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray79,
          ),
        ),
        Text(
          subTitle,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: AppTypography.text16.copyWith(
            color: AppColors.primaryBlue,
          ),
        )
      ],
    );
  }
}
