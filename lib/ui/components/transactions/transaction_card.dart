import 'package:korrency/core/core.dart';

class TransactionTab extends StatelessWidget {
  const TransactionTab({
    super.key,
    this.onTabChange,
    required this.text,
    this.isActive = false,
  });

  final String text;
  final bool isActive;
  final VoidCallback? onTabChange;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTabChange,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(8),
        ),
        decoration: BoxDecoration(
          color: isActive ? AppColors.primaryBlue : AppColors.grayF5,
          borderRadius: BorderRadius.circular(
            Sizer.radius(20),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                fontSize: Sizer.text(12),
                color: isActive ? AppColors.white : AppColors.gray79,
              ),
            ),
            const XBox(4),
            Icon(
              Iconsax.arrow_down_1,
              size: Sizer.radius(16),
              color: isActive ? AppColors.white : AppColors.gray79,
            )
          ],
        ),
      ),
    );
  }
}
