import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class SetupYourPinScreen extends StatefulWidget {
  const SetupYourPinScreen({super.key});

  @override
  State<SetupYourPinScreen> createState() => _SetupYourPinScreenState();
}

class _SetupYourPinScreenState extends State<SetupYourPinScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'New Transaction Pin',
          onBackBtnTap: () {
            Navigator.pop(context);
            vm.pinC.clear();
          },
        ),
        body: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text:
                          "Enter a new 4-digit PIN which would be required for all future transactions",
                      style: FontTypography.text16
                          .withCustomColor(AppColors.gray93)
                          .withCustomHeight(1.6),
                    ),
                  ],
                ),
              ),
              const YBox(70),
              Center(
                child: Pinput(
                  defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                  followingPinTheme: PinInputTheme.changePinTheme(),
                  focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                  submittedPinTheme: PinInputTheme.changePinTheme(),
                  length: 4,
                  controller: vm.pinC,
                  showCursor: true,
                  obscureText: true,
                  obscuringWidget: Container(
                    padding: EdgeInsets.only(top: Sizer.height(8)),
                    child: Text('*', style: AppTypography.text36),
                  ),
                  onChanged: (value) {
                    vm.reBuildUI();
                    if (value.length == 4) {
                      FocusScope.of(context).unfocus();
                    }
                  },
                  onCompleted: (pin) {
                    FocusScope.of(context).unfocus();
                  },
                ),
              ),
              const YBox(10),
              const Spacer(),
              CustomBtn.withChild(
                online: vm.pinC.text.length == 4,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                onTap: () {
                  FocusScope.of(context).unfocus();
                  Navigator.pushNamed(context, RoutePath.confirmYourPinScreen);
                },
                child: ContinueText(isOnline: vm.pinC.text.length == 4),
              ),
              const YBox(60),
            ],
          ),
        ),
      );
    });
  }
}
