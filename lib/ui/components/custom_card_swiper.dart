import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:swipe_deck/swipe_deck.dart';

class AngledCardStack extends StatefulWidget {
  final List<Widget> cards;
  final double cardWidth;
  final double cardHeight;
  final double angleInDegrees;
  final double stackOffset;
  final double swipeThreshold;
  final Function(int)? onCardChanged;
  final EdgeInsetsGeometry padding;
  final int visibleCardsCount;

  const AngledCardStack({
    super.key,
    required this.cards,
    this.cardWidth = 300,
    this.cardHeight = 190,
    this.angleInDegrees = 7.0,
    this.stackOffset = 20.0,
    this.swipeThreshold = 0.3,
    this.onCardChanged,
    this.padding = const EdgeInsets.all(20),
    this.visibleCardsCount = 3,
  });

  @override
  State<AngledCardStack> createState() => _AngledCardStackState();
}

class _AngledCardStackState extends State<AngledCardStack>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentIndex = 0;
  double _dragStartPosition = 0;
  double _dragDistance = 0;
  bool _isDragging = false;
  bool _isAnimating = false;

  // Track velocity for swipe momentum
  double _dragVelocity = 0;
  DateTime? _lastDragTime;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animation = Tween<double>(begin: 0, end: 0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _animationController.addListener(() {
      setState(() {});
    });

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _isAnimating = false;
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onDragStart(DragStartDetails details) {
    if (_isAnimating) return;

    setState(() {
      _dragStartPosition = details.localPosition.dx;
      _isDragging = true;
      _dragDistance = 0;
      _lastDragTime = DateTime.now();
    });
  }

  void _onDragUpdate(DragUpdateDetails details) {
    if (_isAnimating) return;

    final now = DateTime.now();
    final timeDelta = now.difference(_lastDragTime!).inMilliseconds;

    if (timeDelta > 0) {
      // Calculate velocity (pixels per millisecond)
      final instantVelocity =
          (details.localPosition.dx - _dragStartPosition - _dragDistance) /
              timeDelta;
      // Simple velocity smoothing
      _dragVelocity = _dragVelocity * 0.8 + instantVelocity * 0.2;
    }

    setState(() {
      _dragDistance = details.localPosition.dx - _dragStartPosition;
      _lastDragTime = now;
    });
  }

  void _onDragEnd(DragEndDetails details) {
    if (_isAnimating) return;

    final screenWidth = MediaQuery.of(context).size.width;
    final swipeThreshold = screenWidth * widget.swipeThreshold;

    // Consider both drag distance and velocity for swiping
    final effectiveSwipe = _dragDistance + (_dragVelocity * 100);

    if (effectiveSwipe.abs() > swipeThreshold) {
      if (effectiveSwipe > 0) {
        // Swipe right (previous card)
        if (_currentIndex > 0) {
          _goToPreviousCard();
        } else {
          _resetPosition();
        }
      } else {
        // Swipe left (next card)
        if (_currentIndex < widget.cards.length - 1) {
          _goToNextCard();
        } else {
          _resetPosition();
        }
      }
    } else {
      _resetPosition();
    }

    setState(() {
      _isDragging = false;
      _dragVelocity = 0;
    });
  }

  void _goToNextCard() {
    _isAnimating = true;
    final screenWidth = MediaQuery.of(context).size.width;

    _animation = Tween<double>(
      begin: _dragDistance,
      end: -screenWidth,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward(from: 0).then((_) {
      setState(() {
        _currentIndex = math.min(_currentIndex + 1, widget.cards.length - 1);
        _dragDistance = 0;
      });

      if (widget.onCardChanged != null) {
        widget.onCardChanged!(_currentIndex);
      }

      _animationController.reset();
    });
  }

  void _goToPreviousCard() {
    _isAnimating = true;
    final screenWidth = MediaQuery.of(context).size.width;

    _animation = Tween<double>(
      begin: _dragDistance,
      end: screenWidth,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward(from: 0).then((_) {
      setState(() {
        _currentIndex = math.max(_currentIndex - 1, 0);
        _dragDistance = 0;
      });

      if (widget.onCardChanged != null) {
        widget.onCardChanged!(_currentIndex);
      }

      _animationController.reset();
    });
  }

  void _resetPosition() {
    _isAnimating = true;

    _animation = Tween<double>(
      begin: _dragDistance,
      end: 0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward(from: 0).then((_) {
      setState(() {
        _dragDistance = 0;
      });
      _animationController.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.cards.isEmpty) {
      return const SizedBox();
    }

    return Padding(
      padding: widget.padding,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final effectiveWidth =
              math.min(widget.cardWidth, constraints.maxWidth);
          final effectiveHeight =
              math.min(widget.cardHeight, constraints.maxHeight);

          return Center(
            child: SizedBox(
              width: effectiveWidth,
              height: effectiveHeight +
                  (widget.stackOffset * (widget.visibleCardsCount - 1)),
              child: Stack(
                alignment: Alignment.topCenter,
                children: _buildStackedCards(effectiveWidth, effectiveHeight),
              ),
            ),
          );
        },
      ),
    );
  }

  List<Widget> _buildStackedCards(double width, double height) {
    final List<Widget> stackedCards = [];

    // Calculate how many cards to show (behind the current one)
    final maxVisibleCards =
        math.min(widget.visibleCardsCount, widget.cards.length);

    // Start from the bottom of the stack
    for (int i = _currentIndex + maxVisibleCards - 1; i >= _currentIndex; i--) {
      if (i >= widget.cards.length) continue;

      final indexFromTop = i - _currentIndex;
      final isCurrentCard = indexFromTop == 0;

      // Calculate the position of this card in the stack
      final offset = widget.stackOffset * indexFromTop;

      // Calculate the rotation angle (only for the current card when dragging)
      final angle = isCurrentCard
          ? (_isDragging || _animationController.isAnimating
              ? (_dragDistance +
                      (_animationController.isAnimating
                          ? _animation.value
                          : 0)) /
                  width *
                  15
              : 0)
          : 0.0;

      // Calculate the offset of the current card when dragging
      final dragOffset = isCurrentCard
          ? (_isDragging || _animationController.isAnimating
              ? _dragDistance +
                  (_animationController.isAnimating ? _animation.value : 0)
              : 0)
          : 0.0;

      stackedCards.add(
        Positioned(
          top: offset,
          child: Transform.translate(
            offset: Offset(dragOffset.toDouble(), 0),
            child: Transform.rotate(
              angle: (angle + (indexFromTop * widget.angleInDegrees)) *
                  (math.pi / 180),
              alignment: Alignment.topCenter,
              child: Opacity(
                opacity: 1.0 - (indexFromTop * 0.1),
                child: GestureDetector(
                  onHorizontalDragStart: isCurrentCard ? _onDragStart : null,
                  onHorizontalDragUpdate: isCurrentCard ? _onDragUpdate : null,
                  onHorizontalDragEnd: isCurrentCard ? _onDragEnd : null,
                  child: SizedBox(
                    width: width,
                    height: height,
                    child: widget.cards[i],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }

    return stackedCards;
  }
}

// Card indicator dots
class CardIndicators extends StatelessWidget {
  final int total;
  final int current;
  final Color activeColor;
  final Color inactiveColor;
  final double size;
  final double spacing;

  const CardIndicators({
    super.key,
    required this.total,
    required this.current,
    this.activeColor = Colors.blue,
    this.inactiveColor = Colors.grey,
    this.size = 8.0,
    this.spacing = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        total,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: spacing / 2),
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: current == index ? activeColor : inactiveColor,
          ),
        ),
      ),
    );
  }
}

const IMAGES = [
  "",
];

class CardStackDemo extends StatefulWidget {
  const CardStackDemo({super.key});

  @override
  State<CardStackDemo> createState() => _CardStackDemoState();
}

class _CardStackDemoState extends State<CardStackDemo> {
  final int _currentCardIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F2F5),
      appBar: AppBar(
        title: const Text('Angled Card Stack'),
        elevation: 0,
      ),
      body: Center(
        child: SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: Center(
            child: SwipeDeck(
              aspectRatio: 9 / 4,
              startIndex: 0,
              emptyIndicator: const Center(
                child: Text("Nothing Here"),
              ),
              cardSpreadInDegrees: 5, // Change the Spread of Background Cards
              onSwipeLeft: () {
                print("USER SWIPED LEFT -> GOING TO NEXT WIDGET");
              },
              onSwipeRight: () {
                print("USER SWIPED RIGHT -> GOING TO PREVIOUS WIDGET");
              },
              onChange: (index) {
                print(IMAGES[index]);
              },
              widgets: IMAGES
                  .map((e) => GestureDetector(
                        onTap: () {
                          print(e);
                        },
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image.asset(
                              "assets/images/$e.jpg",
                              fit: BoxFit.cover,
                            )),
                      ))
                  .toList(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrencyCard({
    required Gradient gradient,
    required String currencyCode,
    required String flagEmoji,
    required String balance,
    required String accountNumber,
  }) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                child: Row(
                  children: [
                    Text(
                      flagEmoji,
                      style: const TextStyle(fontSize: 18),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      currencyCode,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Balance',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.visibility,
                color: Colors.white.withOpacity(0.8),
                size: 20,
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            balance,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 32,
            ),
          ),
          const Spacer(),
          const Text(
            'Account Number',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '**** **** **** $accountNumber',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalletCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1A2342), Color(0xFF0F1429)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Digital Wallet',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),
          const Spacer(),
          const Text(
            'Your secure digital payment solution',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildFeatureChip('Send'),
              const SizedBox(width: 8),
              _buildFeatureChip('Receive'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(30),
      ),
      child: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
