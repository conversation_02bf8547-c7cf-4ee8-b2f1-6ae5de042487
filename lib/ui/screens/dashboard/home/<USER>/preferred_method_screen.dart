// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class PreferredMethodScreen extends StatelessWidget {
//   const PreferredMethodScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Consumer<WalletVM>(builder: (context, vm, _) {
//       return Scaffold(
//         backgroundColor: AppColors.bgWhite,
//         body: SafeArea(
//           bottom: false,
//           child: Container(
//             padding: EdgeInsets.symmetric(
//               horizontal: Sizer.width(24),
//             ).copyWith(
//               top: Sizer.height(20),
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Container(
//                   alignment: Alignment.centerLeft,
//                   child: const ArrowBack(),
//                 ),
//                 const YBox(30),
//                 const AuthTextSubTitle(
//                   title: "Choose preferred method",
//                   subtitle: "Select a method to add funds to your wallet.",
//                 ),
//                 const YBox(24),
//                 if (vm.activeWallet != null &&
//                     vm.activeWallet?.currency?.code ==
//                         CurrencyConst.nairaCurrency)
//                   CustomListViews.view(
//                     icon: Iconsax.bank,
//                     title: "Bank Transfer",
//                     onTap: () {
//                       Navigator.pushNamed(
//                           context, RoutePath.accountDetailsScreen);
//                     },
//                   ),
//                 if (vm.activeWallet != null &&
//                     vm.activeWallet?.currency?.code ==
//                         CurrencyConst.cadCurrency)
//                   CustomListViews.view(
//                     icon: AppImages.interac,
//                     title: "Interac e-Transfer",
//                     onTap: () {
//                       Navigator.pushNamed(
//                           context, RoutePath.interacTransferScreen);
//                     },
//                   ),
//                 const YBox(24),
//                 CustomListViews.view(
//                   icon: Iconsax.arrow_swap_horizontal,
//                   title: "Fund with other wallets",
//                   onTap: () {
//                     if (vm.walletList.length < 2) {
//                       BsWrapper.bottomSheet(
//                         context: context,
//                         widget: ConfirmationSheet(
//                           // title: 'Warning',
//                           message:
//                               "You need at least two wallets to convert currency",
//                           firstBtnText: "Create Wallet",
//                           firstBtnTap: () {
//                             Navigator.pop(context);
//                             BsWrapper.bottomSheet(
//                               context: context,
//                               widget: const AllWalletsSheet(),
//                             );
//                           },
//                         ),
//                       );

//                       return;
//                     }

//                     Navigator.pushNamed(
//                         context, RoutePath.convertCurrencyScreen);
//                   },
//                 ),
//                 const YBox(24),
//                 CustomListViews.view(
//                   icon: Iconsax.bank,
//                   isSvg: true,
//                   title: "Share username (Korrency)",
//                   onTap: () {
//                     Navigator.pushNamed(
//                         context, RoutePath.shareByUsernameScreen);
//                   },
//                 )
//               ],
//             ),
//           ),
//         ),
//       );
//     });
//   }
// }
