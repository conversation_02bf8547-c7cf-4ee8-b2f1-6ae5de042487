import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';

class CopyBtn extends StatefulWidget {
  const CopyBtn({
    super.key,
    required this.text,
  });

  final String text;

  @override
  State<CopyBtn> createState() => _CopyBtnState();
}

class _CopyBtnState extends State<CopyBtn> with TickerProviderStateMixin {
  bool hasCopied = false;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: InkWell(
            onTap: () async {
              // Scale down animation
              await _scaleController.forward();

              await Clipboard.setData(
                  ClipboardData(text: widget.text ?? "Hello"));
              hasCopied = true;
              setState(() {});

              // Scale back up animation
              await _scaleController.reverse();

              Future.delayed(const Duration(seconds: 2), () {
                if (mounted) {
                  hasCopied = false;
                  setState(() {});
                }
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(12),
                vertical: Sizer.height(4),
              ),
              decoration: BoxDecoration(
                color: hasCopied ? AppColors.primaryBlue : AppColors.bgWhite,
                border: Border.all(
                  color: AppColors.primaryBlue,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(Sizer.radius(14)),
              ),
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 250),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.3),
                        end: Offset.zero,
                      ).animate(animation),
                      child: child,
                    ),
                  );
                },
                child: Text(
                  hasCopied ? "Copied!" : "Copy",
                  key: ValueKey(hasCopied),
                  style: AppTypography.text12.medium.copyWith(
                    color: hasCopied ? AppColors.white : AppColors.primaryBlue,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
