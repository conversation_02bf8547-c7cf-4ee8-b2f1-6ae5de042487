import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class BeneficiaryScreen extends StatefulWidget {
  const BeneficiaryScreen({
    super.key,
    this.arg,
  });

  final BeneficiaryArg? arg;

  @override
  State<BeneficiaryScreen> createState() => _BeneficiaryScreenState();
}

class _BeneficiaryScreenState extends State<BeneficiaryScreen> {
  @override
  void initState() {
    super.initState();
    printty("BeneficiaryArg ${widget.arg?.currencyId}");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.arg?.currencyId != null) {
        if (context.read<BeneficiaryVM>().beneficiariesByCurrencyId.isEmpty) {
          _getBeneficiaryByCurrencyId();
        }
        return;
      } else {
        _getBeneficiary();
      }
    });
  }

  _getBeneficiary() {
    context.read<BeneficiaryVM>().getBeneficiaries().then((value) {
      if (value.success && value.data["data"].isNotEmpty) {
        return;
      } else {
        Navigator.pushReplacementNamed(
          context,
          RoutePath.infoBeneficiaryScreen,
        );
      }
    });
  }

  _getBeneficiaryByCurrencyId() {
    context.read<BeneficiaryVM>().getBeneficiaries(
          currencyId: widget.arg!.currencyId,
        );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BeneficiaryVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Beneficiary',
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                CustomTextField(
                  borderRadius: Sizer.height(12),
                  prefixIcon: Icon(
                    Iconsax.search_normal_1,
                    color: AppColors.black600,
                    size: Sizer.radius(20),
                  ),
                  hintText: 'Search...',
                  // controller: vm.emailController,
                  onChanged: (val) {
                    vm.searchBeneficiaries(val);
                  },
                ),
                if (vm.beneficiaries.isEmpty &&
                    vm.beneficiariesByCurrencyId.isEmpty &&
                    !vm.isBusy)
                  SizedBox(
                    height: Sizer.height(500),
                    child: Center(
                      child: Text(
                        "No Beneficiary added yet",
                        style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.gray500),
                      ),
                    ),
                  ),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      if (widget.arg?.currencyId != null) {
                        _getBeneficiaryByCurrencyId();
                      } else {
                        _getBeneficiary();
                      }
                    },
                    child: ListView.separated(
                      padding: EdgeInsets.only(
                          top: Sizer.height(30), bottom: Sizer.height(200)),
                      itemBuilder: (context, i) {
                        var beneficiary = widget.arg?.currencyId != null
                            ? vm.beneficiariesByCurrencyId[i]
                            : vm.beneficiaries[i];

                        return UserListTile(
                          name: beneficiary.accountName?.capitalize() ?? '',
                          avater: beneficiary.iconUrl,
                          useImageNetwork: true,
                          showSubTitle: true,
                          subTitle:
                              "...${(beneficiary.accountIdentifier ?? "").substring((beneficiary.accountIdentifier?.length ?? 0) - 4)}",
                          onTap: () {
                            /// Auto fill account number and name
                            /// In send money screen
                            if (widget.arg?.currencyId != null) {
                              var sendMoneyVM = context.read<SendMoneyVM>();
                              if (widget.arg?.transferMethodType ==
                                  TransferMethodType.bankTransfer) {
                                sendMoneyVM.autoFillAcctNumberName(
                                  beneficiary,
                                  saveBeneficiary: true,
                                );
                              } else {
                                sendMoneyVM.autoFillInteracArgs(beneficiary);
                              }

                              Navigator.pop(context, beneficiary);
                            }
                          },
                          trailingWidget: widget.arg?.currencyId != null
                              ? null
                              : InkWell(
                                  onTap: () {
                                    BsWrapper.bottomSheet(
                                      context: context,
                                      widget: ConfirmationSheet(
                                        centerWidget: Container(
                                          margin: EdgeInsets.only(
                                            top: Sizer.height(16),
                                          ),
                                          padding: EdgeInsets.symmetric(
                                            horizontal: Sizer.width(12),
                                            vertical: Sizer.height(8),
                                          ),
                                          color: AppColors.blu000,
                                          child: UserListTile(
                                            name: beneficiary.accountName
                                                    ?.capitalize() ??
                                                '',
                                            showSubTitle: true,
                                            avater: beneficiary.iconUrl,
                                            useImageNetwork: true,
                                            subTitle:
                                                "...${(beneficiary.accountIdentifier ?? "").substring((beneficiary.accountIdentifier?.length ?? 0) - 4)}",
                                          ),
                                        ),
                                        title: 'Delete Beneficiary',
                                        message:
                                            'Are you sure you want to delete this beneficiary?',
                                        // loading: vm.isBusy,
                                        secondBtnText: 'Delete',
                                        secendBtnTap: () {
                                          Navigator.pop(context);
                                          vm
                                              .deleteBeneficiary(
                                                  beneficiary.id!)
                                              .then((value) {
                                            if (value.success) {
                                              _showComfirmationScreen(
                                                  msg: value.message);
                                            } else {
                                              _showComfirmationScreen(
                                                  isFailed: true,
                                                  msg: value.message);
                                            }
                                          });
                                        },
                                      ),
                                    );
                                  },
                                  child: svgHelper(AppSvgs.trash),
                                ),
                          showTrailing: true,
                        );
                      },
                      separatorBuilder: (ctx, _) => const YBox(20),
                      itemCount: widget.arg?.currencyId != null
                          ? vm.beneficiariesByCurrencyId.length
                          : vm.beneficiaries.length,
                    ),
                  ),
                ),

                // const YBox(50),
              ],
            ),
          ),
          bottomSheet: Container(
            color: AppColors.bgWhite,
            height: Sizer.height(80),
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.solid(
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              onTap: () {
                Navigator.pushNamed(context, RoutePath.selectCountryScreen);
              },
              online: true,
              text: "Add New Beneficiary",
            ),
          ),
        ),
      );
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Something went wrong"
                : "Beneficiary deleted successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }
}
