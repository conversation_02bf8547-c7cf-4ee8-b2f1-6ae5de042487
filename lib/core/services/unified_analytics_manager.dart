import 'package:korrency/core/core.dart';

/// Unified Analytics Manager that coordinates all analytics services
/// Implements proper attribution logic to ensure each platform only tracks
/// users from their respective ad campaigns
class UnifiedAnalyticsManager {
  static UnifiedAnalyticsManager? _instance;
  static UnifiedAnalyticsManager get instance =>
      _instance ??= UnifiedAnalyticsManager._();

  UnifiedAnalyticsManager._();

  bool _isInitialized = false;

  /// Initialize all analytics services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      printty('🚀 Initializing Unified Analytics Manager...');

      // Initialize Attribution Service first
      await AttributionService.instance.initialize();

      // Initialize Firebase Analytics
      await FirebaseAnalyticsService.instance.initialize();

      // Initialize Facebook Analytics
      await FacebookAnalyticsService.instance.initialize();

      // AppsFlyer is already initialized in AppInitService
      // Just ensure it's ready
      if (!AppsFlyerService.instance.isInitialized) {
        await AppsFlyerService.instance.initialize();
      }

      _isInitialized = true;
      printty('✅ Unified Analytics Manager initialized successfully');
    } catch (e) {
      printty('❌ Error initializing Unified Analytics Manager: $e');
      rethrow;
    }
  }

  /// Set user ID across all analytics platforms
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      // Set user ID in AppsFlyer (tracks all users)
      await AppsFlyerService.instance.setUserId(userId);

      // Set user ID in Firebase Analytics (only for Google Ads users)
      await FirebaseAnalyticsService.instance.setUserId(userId);

      // Set user ID in Facebook Analytics (only for Facebook Ads users)
      await FacebookAnalyticsService.instance.setUserId(userId);

      // Identify user in Customer.io
      await CustomerIOService.instance.identify(userId: userId);

      printty('👤 User ID set across all analytics platforms: $userId');
    } catch (e) {
      printty('❌ Error setting user ID: $e');
    }
  }

  /// Set user properties for enhanced targeting
  Future<void> setUserProperties({
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? city,
    String? state,
    String? country,
    String? zipCode,
    Map<String, String>? customProperties,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      // Set Firebase Analytics user properties (only for Google Ads users)
      if (AttributionService.instance.isFromGoogleAds()) {
        if (email != null) {
          await FirebaseAnalyticsService.instance
              .setUserProperty('email', email);
        }
        if (country != null) {
          await FirebaseAnalyticsService.instance
              .setUserProperty('country', country);
        }
        if (customProperties != null) {
          for (final entry in customProperties.entries) {
            await FirebaseAnalyticsService.instance
                .setUserProperty(entry.key, entry.value);
          }
        }
      }

      // Set Facebook Analytics user data (only for Facebook Ads users)
      if (AttributionService.instance.isFromFacebookAds()) {
        await FacebookAnalyticsService.instance.setUserData(
          email: email,
          firstName: firstName,
          lastName: lastName,
          phone: phone,
          city: city,
          state: state,
          country: country,
          zipCode: zipCode,
        );
      }

      printty('📝 User properties set across applicable analytics platforms');

      // Update attributes in Customer.io (if identified)
      final attributes = <String, dynamic>{};
      if (email != null) attributes['email'] = email;
      if (firstName != null) attributes['first_name'] = firstName;
      if (lastName != null) attributes['last_name'] = lastName;
      if (phone != null) attributes['phone'] = phone;
      if (city != null) attributes['city'] = city;
      if (state != null) attributes['state'] = state;
      if (country != null) attributes['country'] = country;
      if (zipCode != null) attributes['zip'] = zipCode;
      if (customProperties != null) {
        attributes.addAll(customProperties);
      }
      await CustomerIOService.instance.setUserProperties(attributes);
    } catch (e) {
      printty('❌ Error setting user properties: $e');
    }
  }

  /// Track Create Account event across all applicable platforms
  Future<void> trackCreateAccount({
    required String userId,
    String? method,
    Map<String, dynamic>? additionalParameters,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      printty('📊 Tracking Create Account event for user: $userId');

      // Track in AppsFlyer (all users)
      await AppsFlyerService.instance.logCreateAccount(
        userId: userId,
        method: method,
        additionalParameters: additionalParameters,
      );

      // Track in Firebase Analytics (only Google Ads users)
      await FirebaseAnalyticsService.instance.logCreateAccount(
        userId: userId,
        method: method,
        additionalParameters: additionalParameters,
      );

      // Track in Facebook Analytics (only Facebook Ads users)
      await FacebookAnalyticsService.instance.logCreateAccount(
        userId: userId,
        method: method,
        additionalParameters: additionalParameters,
      );

      // Track in Customer.io (ensure identification)
      await CustomerIOService.instance.identify(userId: userId);
      await CustomerIOService.instance.trackCreateAccount(
        userId: userId,
        method: method ?? 'unknown',
        timestamp: DateTime.now(),
        hasReferral: (additionalParameters?['has_referral_code'] ?? false) == true,
        referralData: additionalParameters,
      );

      printty('✅ Create Account event tracked across applicable platforms');
    } catch (e) {
      printty('❌ Error tracking Create Account event: $e');
    }
  }

  /// Track Complete KYC event across all applicable platforms
  Future<void> trackCompleteKyc({
    required String userId,
    String? kycLevel,
    String? verificationMethod,
    Map<String, dynamic>? additionalParameters,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      printty('📊 Tracking Complete KYC event for user: $userId');

      // Track in AppsFlyer (all users)
      await AppsFlyerService.instance.logCompleteKyc(
        userId: userId,
        kycLevel: kycLevel,
        verificationMethod: verificationMethod,
        additionalParameters: additionalParameters,
      );

      // Track in Firebase Analytics (only Google Ads users)
      await FirebaseAnalyticsService.instance.logCompleteKyc(
        userId: userId,
        kycLevel: kycLevel,
        verificationMethod: verificationMethod,
        additionalParameters: additionalParameters,
      );

      // Track in Facebook Analytics (only Facebook Ads users)
      await FacebookAnalyticsService.instance.logCompleteKyc(
        userId: userId,
        kycLevel: kycLevel,
        verificationMethod: verificationMethod,
        additionalParameters: additionalParameters,
      );

      // Track in Customer.io
      await CustomerIOService.instance.trackCompleteKyc(
        userId: userId,
        verificationLevel: kycLevel ?? 'unknown',
        completionTime: DateTime.now(),
        extra: additionalParameters,
      );

      printty('✅ Complete KYC event tracked across applicable platforms');
    } catch (e) {
      printty('❌ Error tracking Complete KYC event: $e');
    }
  }

  /// Track Deposit Money event across all applicable platforms
  Future<void> trackDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      printty(
          '📊 Tracking Deposit Money event for user: $userId, amount: $amount $currency');

      // Track in AppsFlyer (all users)
      await AppsFlyerService.instance.logDepositMoney(
        userId: userId,
        amount: amount,
        currency: currency,
        paymentMethod: paymentMethod,
        corridor: corridor,
        additionalParameters: additionalParameters,
      );

      // Track in Firebase Analytics (only Google Ads users)
      await FirebaseAnalyticsService.instance.logDepositMoney(
        userId: userId,
        amount: amount,
        currency: currency,
        paymentMethod: paymentMethod,
        corridor: corridor,
        additionalParameters: additionalParameters,
      );

      // Track in Facebook Analytics (only Facebook Ads users)
      await FacebookAnalyticsService.instance.logDepositMoney(
        userId: userId,
        amount: amount,
        currency: currency,
        paymentMethod: paymentMethod,
        corridor: corridor,
        additionalParameters: additionalParameters,
      );

      // Track in Customer.io
      await CustomerIOService.instance.trackDepositMoney(
        userId: userId,
        amount: amount,
        currency: currency,
        paymentMethod: paymentMethod,
        corridor: corridor,
        timestamp: DateTime.now(),
        extra: additionalParameters,
      );

      printty('✅ Deposit Money event tracked across applicable platforms');
    } catch (e) {
      printty('❌ Error tracking Deposit Money event: $e');
    }
  }

  /// Track Send Money event across all applicable platforms
  Future<void> trackSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    Map<String, dynamic>? additionalParameters,
  }) async {
    if (!_isInitialized) {
      printty('⚠️ Unified Analytics Manager not initialized');
      return;
    }

    try {
      printty(
          '📊 Tracking Send Money event for user: $userId, amount: $amount $fromCurrency -> $toCurrency');

      // Track in AppsFlyer (all users)
      await AppsFlyerService.instance.logSendMoney(
        userId: userId,
        amount: amount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        corridor: corridor,
        transactionId: transactionId,
        additionalParameters: additionalParameters,
      );

      // Track in Firebase Analytics (only Google Ads users)
      await FirebaseAnalyticsService.instance.logSendMoney(
        userId: userId,
        amount: amount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        corridor: corridor,
        transactionId: transactionId,
        additionalParameters: additionalParameters,
      );

      // Track in Facebook Analytics (only Facebook Ads users)
      await FacebookAnalyticsService.instance.logSendMoney(
        userId: userId,
        amount: amount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        corridor: corridor,
        transactionId: transactionId,
        additionalParameters: additionalParameters,
      );

      // Track in Customer.io
      await CustomerIOService.instance.trackSendMoney(
        userId: userId,
        amount: amount,
        fromCurrency: fromCurrency,
        toCurrency: toCurrency,
        corridor: corridor,
        transactionId: transactionId,
        timestamp: DateTime.now(),
        extra: additionalParameters,
      );

      printty('✅ Send Money event tracked across applicable platforms');
    } catch (e) {
      printty('❌ Error tracking Send Money event: $e');
    }
  }

  /// Get current attribution information
  AttributionData? get currentAttribution =>
      AttributionService.instance.currentAttribution;

  /// Check if analytics manager is initialized
  bool get isInitialized => _isInitialized;
}
