import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ConfirmPinScreen extends StatefulWidget {
  const ConfirmPinScreen({super.key});

  @override
  State<ConfirmPinScreen> createState() => _ConfirmPinScreenState();
}

class _ConfirmPinScreenState extends State<ConfirmPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, child) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Confirm Transaction Pin',
            onBackBtnTap: () {
              vm.pinConfirmC.clear();
              Navigator.pop(context);
            },
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: Column(
              children: [
                Text(
                  "Enter a new 4-digit PIN which would be required for all future transactions",
                  style: AppTypography.text16.copyWith(color: AppColors.gray93),
                ),
                const YBox(70),
                Center(
                  child: Pinput(
                    defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                    followingPinTheme: PinInputTheme.changePinTheme(),
                    focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                    submittedPinTheme: PinInputTheme.changePinTheme(),
                    length: 4,
                    controller: vm.pinConfirmC,
                    focusNode: pinFocusNode,
                    showCursor: true,
                    obscureText: true,
                    obscuringWidget: Container(
                      padding: EdgeInsets.only(top: Sizer.height(8)),
                      child: Text('*', style: AppTypography.text36),
                    ),
                    onChanged: (value) {
                      if (!vm.isPinMatched &&
                          vm.pinConfirmC.text.trim().length == 4) {
                        // vm.pinConfirmC.clear();
                      }
                      vm.reBuildUI();
                    },
                    onCompleted: (pin) {},
                  ),
                ),
                const YBox(10),
                Visibility(
                  visible: !vm.isPinMatched &&
                      vm.pinConfirmC.text.trim().length == 4,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 1000),
                    opacity: (!vm.isPinMatched &&
                            vm.pinConfirmC.text.trim().length == 4)
                        ? 1
                        : 0,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 1000),
                      curve: Curves.easeInOutCubic,
                      alignment: Alignment.center,
                      child: Text(
                        "Code doesn’t match",
                        style: AppTypography.text10.copyWith(
                          color: AppColors.alertRed,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ),
                Spacer(),
                CustomBtn.withChild(
                  onTap: () {
                    _setTransactionPin();
                  },
                  online: vm.isPinMatched && vm.pinConfirmC.text.length == 4,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(
                    isOnline:
                        vm.isPinMatched && vm.pinConfirmC.text.length == 4,
                  ),
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  _setTransactionPin() async {
    var vm = context.read<TransactionPinVM>();
    final pinRes = await vm.createTransactionPin();

    handleApiResponse(
      response: pinRes,
      onSuccess: () {
        vm.clearData();
        Navigator.pop(context);
        Navigator.pop(context);
      },
      onError: () {
        vm.clearData();
        Navigator.pop(context);
      },
    );

    // .then((value) {
    //   if (value.success) {
    //     vm.clearData();
    //     Navigator.pop(context);
    //     Navigator.pop(context);
    //     _openSuccessSheet();
    //   } else {
    //     FlushBarToast.fLSnackBar(
    //       message: value.message.toString(),
    //     );
    //   }
    // });
  }
}
