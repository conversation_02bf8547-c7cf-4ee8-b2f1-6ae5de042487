import 'package:korrency/core/core.dart';

class NgnColText extends StatelessWidget {
  const NgnColText({
    super.key,
    required this.title,
    required this.value,
  });

  final String title;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text12.copyWith(
            color: AppColors.gray93,
          ),
        ),
        Text(
          value,
          style: AppTypography.text16.copyWith(
            color: AppColors.gray51,
          ),
        ),
      ],
    );
  }
}
