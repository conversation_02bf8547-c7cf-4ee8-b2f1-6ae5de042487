import 'package:flutter/material.dart';
import 'package:korrency/core/i18n/translation_helper.dart';

/// Examples of how to use the enhanced TranslationHelper
/// This file serves as documentation and can be removed in production
class TranslationUsageExamples {
  
  /// Basic translation with fallback
  static String basicExample() {
    return TranslationHelper.tr(
      'common.buttons.continue',
      fallback: 'Continue',
    );
  }
  
  /// Translation with arguments
  static String withArgumentsExample() {
    return TranslationHelper.tr(
      'errors.kyc.incomplete',
      namedArgs: {'action': 'send money'},
      fallback: 'Complete your KYC to send money',
    );
  }
  
  /// Safe translation that never throws
  static String safeTranslationExample() {
    return TranslationHelper.safeTr(
      'some.potentially.missing.key',
      fallback: 'Default text',
    );
  }
  
  /// Using extension methods
  static String extensionExample() {
    return 'common.buttons.done'.trSafe;
  }
  
  /// Check if translation exists
  static bool checkTranslationExample() {
    return TranslationHelper.hasTranslation('common.buttons.save');
  }
  
  /// Pluralization example
  static String pluralizationExample(int count) {
    return TranslationHelper.plural(
      'common.time.minutes_ago',
      count,
      fallback: '$count minutes ago',
    );
  }
  
  /// Widget usage examples
  static Widget buildExampleWidgets() {
    return Column(
      children: [
        // Basic usage in Text widget
        Text(TranslationHelper.tr(
          'common.buttons.continue',
          fallback: 'Continue',
        )),
        
        // Usage in AppBar
        AppBar(
          title: Text(TranslationHelper.tr(
            'menu.preferences.title',
            fallback: 'Preferences',
          )),
        ),
        
        // Usage in TextField
        TextField(
          decoration: InputDecoration(
            labelText: TranslationHelper.tr(
              'common.labels.email',
              fallback: 'Email',
            ),
            hintText: TranslationHelper.tr(
              'common.placeholders.enter_email',
              fallback: 'Enter your email',
            ),
          ),
        ),
        
        // Usage in Button
        ElevatedButton(
          onPressed: () {},
          child: Text(TranslationHelper.tr(
            'common.buttons.submit',
            fallback: 'Submit',
          )),
        ),
        
        // Error message usage
        Text(
          TranslationHelper.tr(
            'errors.network',
            fallback: 'Network error occurred',
          ),
          style: const TextStyle(color: Colors.red),
        ),
        
        // Conditional translation
        Builder(
          builder: (context) {
            final hasTranslation = TranslationHelper.hasTranslation('custom.key');
            return Text(
              hasTranslation 
                ? 'custom.key'.trSafe
                : 'Default fallback text',
            );
          },
        ),
      ],
    );
  }
  
  /// Best practices examples
  static void bestPracticesExamples() {
    // ✅ DO: Always provide fallbacks
    final goodExample = TranslationHelper.tr(
      'common.buttons.save',
      fallback: 'Save',
    );
    
    // ✅ DO: Use meaningful key names
    final meaningfulKey = TranslationHelper.tr(
      'auth.login.invalid_credentials',
      fallback: 'Invalid email or password',
    );
    
    // ✅ DO: Use named arguments for dynamic content
    final withNamedArgs = TranslationHelper.tr(
      'transaction.amount_with_currency',
      namedArgs: {'amount': '100', 'currency': 'USD'},
      fallback: '100 USD',
    );
    
    // ❌ DON'T: Use hardcoded strings without translation
    // final badExample = 'Hardcoded text'; // Don't do this
    
    // ❌ DON'T: Use translation keys without fallbacks in production
    // final noFallback = TranslationHelper.tr('some.key'); // Risky
  }
  
  /// Debugging helpers
  static void debuggingExamples() {
    // Get all missing translation keys
    final missingKeys = TranslationHelper.missingKeys;
    print('Missing translations: $missingKeys');
    
    // Clear missing keys cache
    TranslationHelper.clearMissingKeys();
    
    // Check if specific translation exists
    if (!TranslationHelper.hasTranslation('some.key')) {
      print('Translation missing for: some.key');
    }
  }
}

/// Example of creating a reusable translation widget
class TranslatedText extends StatelessWidget {
  final String translationKey;
  final String fallback;
  final TextStyle? style;
  final Map<String, String>? namedArgs;
  final List<String>? args;
  
  const TranslatedText(
    this.translationKey, {
    super.key,
    required this.fallback,
    this.style,
    this.namedArgs,
    this.args,
  });
  
  @override
  Widget build(BuildContext context) {
    return Text(
      TranslationHelper.tr(
        translationKey,
        namedArgs: namedArgs,
        args: args,
        fallback: fallback,
      ),
      style: style,
    );
  }
}
