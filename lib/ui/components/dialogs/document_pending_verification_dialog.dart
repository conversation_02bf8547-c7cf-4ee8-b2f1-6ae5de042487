// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DocumentPendingVerificationDialog extends StatefulWidget {
  const DocumentPendingVerificationDialog({super.key});

  @override
  State<DocumentPendingVerificationDialog> createState() =>
      _DocumentPendingVerificationDialogState();
}

class _DocumentPendingVerificationDialogState
    extends State<DocumentPendingVerificationDialog> {
  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(Sizer.width(10)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppColors.white.withValues(alpha: 0.15),
                      border: Border.all(
                        color: AppColors.white.withValues(alpha: 0.70),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.close,
                      size: Sizer.width(16),
                      color: AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
            YBox(12),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const YBox(28),
                  SvgPicture.asset(
                    AppSvgs.pendingReview,
                    height: Sizer.height(200),
                  ),
                  YBox(20),
                  Text(
                    "Pending Review",
                    textAlign: TextAlign.center,
                    style: FontTypography.text22.semiBold,
                  ),
                  YBox(4),
                  Text(
                    "Your ID document is currently \nundergoing review",
                    textAlign: TextAlign.center,
                    style: FontTypography.text16.withCustomColor(
                      AppColors.gray93,
                    ),
                  ),
                  const YBox(28),
                  CustomBtn.solid(
                    width: Sizer.width(280),
                    onTap: () {
                      Navigator.pop(context);
                    },
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    text: "Done",
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
