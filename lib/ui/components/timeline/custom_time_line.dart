// import 'package:flutter/material.dart';
// import 'package:korrency/core/lib/helpers/sizer.dart';
// import 'package:korrency/core/lib/themes/themes.dart';
// import 'package:timeline_tile/timeline_tile.dart';

// class CustomTimeLine extends StatelessWidget {
//   const CustomTimeLine({
//     Key? key,
//     required this.isFirst,
//     required this.isLast,
//   }) : super(key: key);

//   final bool isFirst;
//   final bool isLast;

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: AppColors.white,
//       height: Sizer.height(200),
//       child: TimelineTile(
//         alignment: TimelineAlign.start,
//         isFirst: isFirst,
//         isLast: isLast,
//         indicatorStyle: const IndicatorStyle(
//           width: 25,
//           color: AppColors.primaryBlue,
//           padding: EdgeInsets.all(8),
//         ),
//         // endChild: const _RightChild(),
//         beforeLineStyle: const LineStyle(
//           color: AppColors.primaryBlue,
//           thickness: 2,
//         ),
//       ),
//     );
//   }
// }
