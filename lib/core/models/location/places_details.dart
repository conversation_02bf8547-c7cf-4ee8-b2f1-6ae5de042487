import 'dart:convert';

List<AddressComponent> addressComponentFromJson(String str) =>
    List<AddressComponent>.from(
        json.decode(str).map((x) => AddressComponent.fromJson(x)));

PlacesDetails placesDetailsFromJson(String str) =>
    PlacesDetails.fromJson(json.decode(str));

String placesDetailsToJson(PlacesDetails data) => json.encode(data.toJson());

class PlacesDetails {
  final List<dynamic>? htmlAttributions;
  final PlacesResult? result;
  final String? status;

  PlacesDetails({
    this.htmlAttributions,
    this.result,
    this.status,
  });

  factory PlacesDetails.fromJson(Map<String, dynamic> json) => PlacesDetails(
        htmlAttributions: json["html_attributions"] == null
            ? []
            : List<dynamic>.from(json["html_attributions"]!.map((x) => x)),
        result: json["result"] == null
            ? null
            : PlacesResult.fromJson(json["result"]),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "html_attributions": htmlAttributions == null
            ? []
            : List<dynamic>.from(htmlAttributions!.map((x) => x)),
        "result": result?.toJson(),
        "status": status,
      };
}

class PlacesResult {
  final List<AddressComponent>? addressComponents;
  final String? adrAddress;
  final String? businessStatus;
  final bool? curbsidePickup;
  final CurrentOpeningHours? currentOpeningHours;
  final bool? delivery;
  final bool? dineIn;
  final String? formattedAddress;
  final String? formattedPhoneNumber;
  final Geometry? geometry;
  final String? icon;
  final String? iconBackgroundColor;
  final String? iconMaskBaseUri;
  final String? internationalPhoneNumber;
  final String? name;
  final OpeningHours? openingHours;
  final List<Photo>? photos;
  final String? placeId;
  final PlusCode? plusCode;
  final double? rating;
  final String? reference;
  final bool? reservable;
  final List<Review>? reviews;
  final List<CurrentOpeningHours>? secondaryOpeningHours;
  final bool? servesBeer;
  final bool? servesDinner;
  final bool? servesLunch;
  final bool? servesVegetarianFood;
  final bool? servesWine;
  final bool? takeout;
  final List<String>? types;
  final String? url;
  final int? userRatingsTotal;
  final int? utcOffset;
  final String? vicinity;
  final String? website;
  final bool? wheelchairAccessibleEntrance;

  PlacesResult({
    this.addressComponents,
    this.adrAddress,
    this.businessStatus,
    this.curbsidePickup,
    this.currentOpeningHours,
    this.delivery,
    this.dineIn,
    this.formattedAddress,
    this.formattedPhoneNumber,
    this.geometry,
    this.icon,
    this.iconBackgroundColor,
    this.iconMaskBaseUri,
    this.internationalPhoneNumber,
    this.name,
    this.openingHours,
    this.photos,
    this.placeId,
    this.plusCode,
    this.rating,
    this.reference,
    this.reservable,
    this.reviews,
    this.secondaryOpeningHours,
    this.servesBeer,
    this.servesDinner,
    this.servesLunch,
    this.servesVegetarianFood,
    this.servesWine,
    this.takeout,
    this.types,
    this.url,
    this.userRatingsTotal,
    this.utcOffset,
    this.vicinity,
    this.website,
    this.wheelchairAccessibleEntrance,
  });

  factory PlacesResult.fromJson(Map<String, dynamic> json) => PlacesResult(
        addressComponents: json["address_components"] == null
            ? []
            : List<AddressComponent>.from(json["address_components"]!
                .map((x) => AddressComponent.fromJson(x))),
        adrAddress: json["adr_address"],
        businessStatus: json["business_status"],
        curbsidePickup: json["curbside_pickup"],
        currentOpeningHours: json["current_opening_hours"] == null
            ? null
            : CurrentOpeningHours.fromJson(json["current_opening_hours"]),
        delivery: json["delivery"],
        dineIn: json["dine_in"],
        formattedAddress: json["formatted_address"],
        formattedPhoneNumber: json["formatted_phone_number"],
        geometry: json["geometry"] == null
            ? null
            : Geometry.fromJson(json["geometry"]),
        icon: json["icon"],
        iconBackgroundColor: json["icon_background_color"],
        iconMaskBaseUri: json["icon_mask_base_uri"],
        internationalPhoneNumber: json["international_phone_number"],
        name: json["name"],
        openingHours: json["opening_hours"] == null
            ? null
            : OpeningHours.fromJson(json["opening_hours"]),
        photos: json["photos"] == null
            ? []
            : List<Photo>.from(json["photos"]!.map((x) => Photo.fromJson(x))),
        placeId: json["place_id"],
        plusCode: json["plus_code"] == null
            ? null
            : PlusCode.fromJson(json["plus_code"]),
        rating: json["rating"]?.toDouble(),
        reference: json["reference"],
        reservable: json["reservable"],
        reviews: json["reviews"] == null
            ? []
            : List<Review>.from(
                json["reviews"]!.map((x) => Review.fromJson(x))),
        secondaryOpeningHours: json["secondary_opening_hours"] == null
            ? []
            : List<CurrentOpeningHours>.from(json["secondary_opening_hours"]!
                .map((x) => CurrentOpeningHours.fromJson(x))),
        servesBeer: json["serves_beer"],
        servesDinner: json["serves_dinner"],
        servesLunch: json["serves_lunch"],
        servesVegetarianFood: json["serves_vegetarian_food"],
        servesWine: json["serves_wine"],
        takeout: json["takeout"],
        types: json["types"] == null
            ? []
            : List<String>.from(json["types"]!.map((x) => x)),
        url: json["url"],
        userRatingsTotal: json["user_ratings_total"],
        utcOffset: json["utc_offset"],
        vicinity: json["vicinity"],
        website: json["website"],
        wheelchairAccessibleEntrance: json["wheelchair_accessible_entrance"],
      );

  Map<String, dynamic> toJson() => {
        "address_components": addressComponents == null
            ? []
            : List<dynamic>.from(addressComponents!.map((x) => x.toJson())),
        "adr_address": adrAddress,
        "business_status": businessStatus,
        "curbside_pickup": curbsidePickup,
        "current_opening_hours": currentOpeningHours?.toJson(),
        "delivery": delivery,
        "dine_in": dineIn,
        "formatted_address": formattedAddress,
        "formatted_phone_number": formattedPhoneNumber,
        "geometry": geometry?.toJson(),
        "icon": icon,
        "icon_background_color": iconBackgroundColor,
        "icon_mask_base_uri": iconMaskBaseUri,
        "international_phone_number": internationalPhoneNumber,
        "name": name,
        "opening_hours": openingHours?.toJson(),
        "photos": photos == null
            ? []
            : List<dynamic>.from(photos!.map((x) => x.toJson())),
        "place_id": placeId,
        "plus_code": plusCode?.toJson(),
        "rating": rating,
        "reference": reference,
        "reservable": reservable,
        "reviews": reviews == null
            ? []
            : List<dynamic>.from(reviews!.map((x) => x.toJson())),
        "secondary_opening_hours": secondaryOpeningHours == null
            ? []
            : List<dynamic>.from(secondaryOpeningHours!.map((x) => x.toJson())),
        "serves_beer": servesBeer,
        "serves_dinner": servesDinner,
        "serves_lunch": servesLunch,
        "serves_vegetarian_food": servesVegetarianFood,
        "serves_wine": servesWine,
        "takeout": takeout,
        "types": types == null ? [] : List<dynamic>.from(types!.map((x) => x)),
        "url": url,
        "user_ratings_total": userRatingsTotal,
        "utc_offset": utcOffset,
        "vicinity": vicinity,
        "website": website,
        "wheelchair_accessible_entrance": wheelchairAccessibleEntrance,
      };
}

class AddressComponent {
  final String? longName;
  final String? shortName;
  final List<String>? types;

  AddressComponent({
    this.longName,
    this.shortName,
    this.types,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) =>
      AddressComponent(
        longName: json["long_name"],
        shortName: json["short_name"],
        types: json["types"] == null
            ? []
            : List<String>.from(json["types"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "long_name": longName,
        "short_name": shortName,
        "types": types == null ? [] : List<dynamic>.from(types!.map((x) => x)),
      };
}

class CurrentOpeningHours {
  final bool? openNow;
  final List<CurrentOpeningHoursPeriod>? periods;
  final List<String>? weekdayText;
  final String? type;

  CurrentOpeningHours({
    this.openNow,
    this.periods,
    this.weekdayText,
    this.type,
  });

  factory CurrentOpeningHours.fromJson(Map<String, dynamic> json) =>
      CurrentOpeningHours(
        openNow: json["open_now"],
        periods: json["periods"] == null
            ? []
            : List<CurrentOpeningHoursPeriod>.from(json["periods"]!
                .map((x) => CurrentOpeningHoursPeriod.fromJson(x))),
        weekdayText: json["weekday_text"] == null
            ? []
            : List<String>.from(json["weekday_text"]!.map((x) => x)),
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "open_now": openNow,
        "periods": periods == null
            ? []
            : List<dynamic>.from(periods!.map((x) => x.toJson())),
        "weekday_text": weekdayText == null
            ? []
            : List<dynamic>.from(weekdayText!.map((x) => x)),
        "type": type,
      };
}

class CurrentOpeningHoursPeriod {
  final PurpleClose? close;
  final PurpleClose? open;

  CurrentOpeningHoursPeriod({
    this.close,
    this.open,
  });

  factory CurrentOpeningHoursPeriod.fromJson(Map<String, dynamic> json) =>
      CurrentOpeningHoursPeriod(
        close:
            json["close"] == null ? null : PurpleClose.fromJson(json["close"]),
        open: json["open"] == null ? null : PurpleClose.fromJson(json["open"]),
      );

  Map<String, dynamic> toJson() => {
        "close": close?.toJson(),
        "open": open?.toJson(),
      };
}

class PurpleClose {
  final DateTime? date;
  final int? day;
  final String? time;
  final bool? truncated;

  PurpleClose({
    this.date,
    this.day,
    this.time,
    this.truncated,
  });

  factory PurpleClose.fromJson(Map<String, dynamic> json) => PurpleClose(
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        day: json["day"],
        time: json["time"],
        truncated: json["truncated"],
      );

  Map<String, dynamic> toJson() => {
        "date":
            "${date!.year.toString().padLeft(4, '0')}-${date!.month.toString().padLeft(2, '0')}-${date!.day.toString().padLeft(2, '0')}",
        "day": day,
        "time": time,
        "truncated": truncated,
      };
}

class Geometry {
  final Location? location;
  final GeoViewport? viewport;

  Geometry({
    this.location,
    this.viewport,
  });

  factory Geometry.fromJson(Map<String, dynamic> json) => Geometry(
        location: json["location"] == null
            ? null
            : Location.fromJson(json["location"]),
        viewport: json["viewport"] == null
            ? null
            : GeoViewport.fromJson(json["viewport"]),
      );

  Map<String, dynamic> toJson() => {
        "location": location?.toJson(),
        "viewport": viewport?.toJson(),
      };
}

class Location {
  final double? lat;
  final double? lng;

  Location({
    this.lat,
    this.lng,
  });

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        lat: json["lat"]?.toDouble(),
        lng: json["lng"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "lat": lat,
        "lng": lng,
      };
}

class GeoViewport {
  final Location? northeast;
  final Location? southwest;

  GeoViewport({
    this.northeast,
    this.southwest,
  });

  factory GeoViewport.fromJson(Map<String, dynamic> json) => GeoViewport(
        northeast: json["northeast"] == null
            ? null
            : Location.fromJson(json["northeast"]),
        southwest: json["southwest"] == null
            ? null
            : Location.fromJson(json["southwest"]),
      );

  Map<String, dynamic> toJson() => {
        "northeast": northeast?.toJson(),
        "southwest": southwest?.toJson(),
      };
}

class OpeningHours {
  final bool? openNow;
  final List<OpeningHoursPeriod>? periods;
  final List<String>? weekdayText;

  OpeningHours({
    this.openNow,
    this.periods,
    this.weekdayText,
  });

  factory OpeningHours.fromJson(Map<String, dynamic> json) => OpeningHours(
        openNow: json["open_now"],
        periods: json["periods"] == null
            ? []
            : List<OpeningHoursPeriod>.from(
                json["periods"]!.map((x) => OpeningHoursPeriod.fromJson(x))),
        weekdayText: json["weekday_text"] == null
            ? []
            : List<String>.from(json["weekday_text"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "open_now": openNow,
        "periods": periods == null
            ? []
            : List<dynamic>.from(periods!.map((x) => x.toJson())),
        "weekday_text": weekdayText == null
            ? []
            : List<dynamic>.from(weekdayText!.map((x) => x)),
      };
}

class OpeningHoursPeriod {
  final FluffyClose? close;
  final FluffyClose? open;

  OpeningHoursPeriod({
    this.close,
    this.open,
  });

  factory OpeningHoursPeriod.fromJson(Map<String, dynamic> json) =>
      OpeningHoursPeriod(
        close:
            json["close"] == null ? null : FluffyClose.fromJson(json["close"]),
        open: json["open"] == null ? null : FluffyClose.fromJson(json["open"]),
      );

  Map<String, dynamic> toJson() => {
        "close": close?.toJson(),
        "open": open?.toJson(),
      };
}

class FluffyClose {
  final int? day;
  final String? time;

  FluffyClose({
    this.day,
    this.time,
  });

  factory FluffyClose.fromJson(Map<String, dynamic> json) => FluffyClose(
        day: json["day"],
        time: json["time"],
      );

  Map<String, dynamic> toJson() => {
        "day": day,
        "time": time,
      };
}

class Photo {
  final int? height;
  final List<String>? htmlAttributions;
  final String? photoReference;
  final int? width;

  Photo({
    this.height,
    this.htmlAttributions,
    this.photoReference,
    this.width,
  });

  factory Photo.fromJson(Map<String, dynamic> json) => Photo(
        height: json["height"],
        htmlAttributions: json["html_attributions"] == null
            ? []
            : List<String>.from(json["html_attributions"]!.map((x) => x)),
        photoReference: json["photo_reference"],
        width: json["width"],
      );

  Map<String, dynamic> toJson() => {
        "height": height,
        "html_attributions": htmlAttributions == null
            ? []
            : List<dynamic>.from(htmlAttributions!.map((x) => x)),
        "photo_reference": photoReference,
        "width": width,
      };
}

class PlusCode {
  final String? compoundCode;
  final String? globalCode;

  PlusCode({
    this.compoundCode,
    this.globalCode,
  });

  factory PlusCode.fromJson(Map<String, dynamic> json) => PlusCode(
        compoundCode: json["compound_code"],
        globalCode: json["global_code"],
      );

  Map<String, dynamic> toJson() => {
        "compound_code": compoundCode,
        "global_code": globalCode,
      };
}

class Review {
  final String? authorName;
  final String? authorUrl;
  final String? language;
  final String? originalLanguage;
  final String? profilePhotoUrl;
  final int? rating;
  final String? relativeTimeDescription;
  final String? text;
  final int? time;
  final bool? translated;

  Review({
    this.authorName,
    this.authorUrl,
    this.language,
    this.originalLanguage,
    this.profilePhotoUrl,
    this.rating,
    this.relativeTimeDescription,
    this.text,
    this.time,
    this.translated,
  });

  factory Review.fromJson(Map<String, dynamic> json) => Review(
        authorName: json["author_name"],
        authorUrl: json["author_url"],
        language: json["language"],
        originalLanguage: json["original_language"],
        profilePhotoUrl: json["profile_photo_url"],
        rating: json["rating"],
        relativeTimeDescription: json["relative_time_description"],
        text: json["text"],
        time: json["time"],
        translated: json["translated"],
      );

  Map<String, dynamic> toJson() => {
        "author_name": authorName,
        "author_url": authorUrl,
        "language": language,
        "original_language": originalLanguage,
        "profile_photo_url": profilePhotoUrl,
        "rating": rating,
        "relative_time_description": relativeTimeDescription,
        "text": text,
        "time": time,
        "translated": translated,
      };
}
