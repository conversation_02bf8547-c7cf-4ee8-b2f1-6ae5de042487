import 'package:flutter/material.dart';
import 'reusable_swiper_card.dart';

/// Example demonstrating how to use the ReusableSwiperCard widget
/// with different types of content (not just wallet cards)
class SwiperCardExample extends StatefulWidget {
  const SwiperCardExample({super.key});

  @override
  State<SwiperCardExample> createState() => _SwiperCardExampleState();
}

class _SwiperCardExampleState extends State<SwiperCardExample> {
  
  // Example 1: Photo cards
  List<Widget> _buildPhotoCards() {
    final photos = [
      {'url': 'https://picsum.photos/300/200?random=1', 'title': 'Nature'},
      {'url': 'https://picsum.photos/300/200?random=2', 'title': 'City'},
      {'url': 'https://picsum.photos/300/200?random=3', 'title': 'Ocean'},
      {'url': 'https://picsum.photos/300/200?random=4', 'title': 'Mountains'},
    ];

    return photos.map((photo) => Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              photo['url']!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.image, size: 50, color: Colors.grey),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Text(
                  photo['title']!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    )).toList();
  }

  // Example 2: Product cards
  List<Widget> _buildProductCards() {
    final products = [
      {'name': 'iPhone 15', 'price': '\$999', 'color': Colors.blue},
      {'name': 'MacBook Pro', 'price': '\$2499', 'color': Colors.purple},
      {'name': 'AirPods Pro', 'price': '\$249', 'color': Colors.green},
      {'name': 'Apple Watch', 'price': '\$399', 'color': Colors.orange},
    ];

    return products.map((product) => Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            product['color'] as Color,
            (product['color'] as Color).withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: (product['color'] as Color).withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(
              Icons.apple,
              color: Colors.white,
              size: 40,
            ),
            const Spacer(),
            Text(
              product['name'] as String,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              product['price'] as String,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Text(
                'Buy Now',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    )).toList();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: const Text('Swiper Card Examples'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Photo Cards Example
              const Text(
                'Photo Cards',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 250,
                child: ReusableSwiperCard(
                  cards: _buildPhotoCards(),
                  onSwipe: (originalIndex, isRightSwipe, cardWidget) {
                    print('Photo card $originalIndex swiped ${isRightSwipe ? 'right' : 'left'}');
                  },
                  onTap: (originalIndex, cardWidget) {
                    print('Background photo card $originalIndex tapped');
                  },
                  onPrimaryTap: (originalIndex, cardWidget) {
                    print('Primary photo card $originalIndex tapped');
                  },
                  cardHeight: 200,
                  cardOffset: 20.0,
                  animationDuration: const Duration(milliseconds: 250),
                  enableSwipe: true,
                  enableBackgroundTap: true,
                  enablePrimaryTap: true,
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Product Cards Example
              const Text(
                'Product Cards',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ReusableSwiperCard(
                  cards: _buildProductCards(),
                  onSwipe: (originalIndex, isRightSwipe, cardWidget) {
                    print('Product card $originalIndex swiped ${isRightSwipe ? 'right' : 'left'}');
                  },
                  onTap: (originalIndex, cardWidget) {
                    print('Background product card $originalIndex tapped');
                  },
                  onPrimaryTap: (originalIndex, cardWidget) {
                    print('Primary product card $originalIndex tapped');
                  },
                  cardHeight: 280,
                  cardOffset: 30.0,
                  animationDuration: const Duration(milliseconds: 400),
                  enableSwipe: true,
                  enableBackgroundTap: true,
                  enablePrimaryTap: false, // Disable primary tap for products
                  minDragDistance: 80.0,
                  minVelocity: 150.0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}