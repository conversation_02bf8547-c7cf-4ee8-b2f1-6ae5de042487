import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class ShareByUsernameScreen extends StatelessWidget {
  const ShareByUsernameScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Share Username',
        ),
        body: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Share your username with other Korrency users to receive funds easily and securely",
                style: AppTypography.text15.copyWith(color: AppColors.gray93),
              ),
              YBox(48),
              Text(
                'Username',
                style: AppTypography.text16.copyWith(
                  color: AppColors.gray51,
                ),
              ),
              const YBox(8),
              Container(
                height: Sizer.height(56),
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(12),
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.primaryBlue,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Text(
                      vm.user?.userName ?? '',
                      style: AppTypography.text16,
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: () {
                        Clipboard.setData(ClipboardData(
                          text: vm.user?.userName ?? '',
                        ));

                        FlushBarToast.fLSnackBar(
                          message: "${vm.user?.userName ?? 'Username'} Copied",
                          snackBarType: SnackBarType.success,
                        );
                      },
                      child: SvgPicture.asset(
                        AppSvgs.copy,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              CustomBtn.withChild(
                borderRadius: BorderRadius.circular(20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Share",
                      style: AppTypography.text16.medium.copyWith(
                        color: AppColors.white,
                      ),
                    ),
                    XBox(8),
                    SvgPicture.asset(
                      AppSvgs.share,
                      colorFilter:
                          ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                    ),
                  ],
                ),
                onTap: () async {
                  await Share.share(
                    korrencyUserShare(
                      userName: vm.user?.userName,
                      refCode: vm.user?.referralCode,
                    ),
                    // vm.user?.userName ?? '',
                    subject: "Korrency",
                  );
                },
              ),
              const YBox(50),
            ],
          ),
        ),
      );
    });
  }
}
