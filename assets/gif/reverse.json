{"assets": [{"id": "3", "layers": [{"ind": 2, "ty": 4, "ks": {}, "ip": 0, "op": 721, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [55.5, 55.5]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [111, 111]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 721, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.61, -1.15], [1.15, 1.62], [-1.62, 1.15], [-1.15, -1.62]], "o": [[1.15, 1.61], [-1.62, 1.15], [-1.15, -1.62], [1.62, -1.15], [0, 0]], "v": [[66.79, 70.84], [65.95, 75.85], [60.94, 75], [61.78, 69.99], [66.79, 70.84]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.15, 1.62], [1.62, -1.15], [-1.15, -1.62], [-1.62, 1.15]], "o": [[1.62, -1.15], [-1.15, -1.62], [-1.62, 1.15], [1.15, 1.62], [0, 0]], "v": [[21.32, 13.11], [22.17, 8.1], [17.16, 7.25], [16.31, 12.27], [21.32, 13.11]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.15, -1.62], [-1.62, 1.15], [1.15, 1.62], [1.62, -1.15]], "o": [[-1.62, 1.15], [1.15, 1.62], [1.61, -1.15], [-1.15, -1.62], [0, 0]], "v": [[70.84, 16.31], [70, 21.32], [75.01, 22.17], [75.85, 17.16], [70.84, 16.31]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.15, -1.62], [-1.61, 1.15], [1.15, 1.62], [1.62, -1.15]], "o": [[-1.62, 1.15], [1.15, 1.62], [1.62, -1.15], [-1.15, -1.62], [0, 0]], "v": [[8.1, 60.94], [7.25, 65.95], [12.27, 66.8], [13.11, 61.78], [8.1, 60.94]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0.05, 0.07], [0.07, 0.06], [0, 0], [0.75, 0.18], [0.18, -0.76], [0, 0], [0, 0], [0, 0], [0.75, 0.18], [0.17, -0.75], [0, 0], [0, 0], [0, 0], [0, 0], [0.49, 0.6], [0.6, -0.49], [0, 0], [0, 0], [0, 0], [0.49, 0.6], [0.6, -0.49], [0, 0], [0.09, 0.02], [0.09, 0], [0, 0], [0.66, -0.41], [-0.41, -0.66], [0, 0], [0, 0], [0, 0], [0.66, -0.4], [-0.41, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0.77, 0.07], [0.08, -0.77], [0, 0], [0, 0], [0, 0], [0.77, 0.08], [0.08, -0.77], [0, 0], [0.08, -0.05], [0.06, -0.06], [0, 0], [0.18, -0.75], [-0.75, -0.18], [0, 0], [0, 0], [0, 0], [0.18, -0.75], [-0.75, -0.18], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, -0.49], [-0.48, -0.6], [0, 0], [0, 0], [0, 0], [0.6, -0.49], [-0.48, -0.59], [0, 0], [0, -0.04], [0, -0.04], [0, 0], [0.07, -0.39], [-0.18, -0.3], [-0.66, 0.41], [0, 0], [0, 0], [0, 0], [0.06, -0.38], [-0.19, -0.3], [-0.66, 0.41], [0, 0], [0, 0], [0, 0], [0, 0], [0.08, -0.77], [-0.77, -0.08], [0, 0], [0, 0], [0, 0], [0.08, -0.77], [-0.77, -0.08], [0, 0], [-0.02, -0.03], [-0.02, -0.03], [0, 0], [-0.23, -0.32], [-0.34, -0.08], [-0.18, 0.75], [0, 0], [0, 0], [0, 0], [-0.23, -0.32], [-0.34, -0.08], [-0.18, 0.75], [0, 0], [0, 0], [0, 0], [0, 0], [-0.49, -0.6], [-0.6, 0.49], [0, 0], [0, 0], [0, 0], [-0.49, -0.6], [-0.6, 0.49], [0, 0], [-0.04, -0.01], [-0.04, 0], [0, 0], [-0.38, -0.06], [-0.3, 0.18], [0.41, 0.66], [0, 0], [0, 0], [0, 0], [-0.38, -0.07], [-0.3, 0.19], [0.41, 0.65], [0, 0], [0, 0], [0, 0], [0, 0], [-0.77, -0.07], [-0.08, 0.77], [0, 0], [0, 0], [0, 0], [-0.77, -0.08], [-0.07, 0.77], [0, 0], [-0.03, 0.02], [-0.02, 0.03], [0, 0], [-0.32, 0.22], [-0.08, 0.34], [0.75, 0.18], [0, 0], [0, 0], [0, 0], [-0.32, 0.23], [-0.08, 0.34], [0.75, 0.18], [0, 0], [0, 0], [0, 0], [0, 0], [-0.6, 0.49], [0.48, 0.6], [0, 0], [0, 0], [0, 0], [-0.6, 0.48], [0.49, 0.6], [0, 0], [-0.02, 0.09], [0, 0.09], [0, 0], [0.41, 0.66], [0.66, -0.4], [0, 0], [0, 0], [0, 0], [0.41, 0.66], [0.66, -0.41], [0, 0], [0, 0], [0, 0], [0, 0], [-0.08, 0.77], [0.77, 0.07], [0, 0], [0, 0], [0, 0], [-0.08, 0.77], [0.76, 0.08]], "o": [[-0.03, -0.08], [-0.05, -0.07], [0, 0], [0.18, -0.75], [-0.75, -0.18], [0, 0], [0, 0], [0, 0], [0.18, -0.75], [-0.75, -0.18], [0, 0], [0, 0], [0, 0], [0, 0], [0.6, -0.49], [-0.49, -0.6], [0, 0], [0, 0], [0, 0], [0.59, -0.48], [-0.49, -0.6], [0, 0], [-0.09, -0.04], [-0.09, -0.02], [0, 0], [-0.41, -0.65], [-0.66, 0.41], [0, 0], [0, 0], [0, 0], [-0.4, -0.66], [-0.66, 0.41], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, -0.77], [-0.77, -0.08], [0, 0], [0, 0], [0, 0], [0.08, -0.77], [-0.77, -0.08], [0, 0], [-0.08, 0.04], [-0.07, 0.05], [0, 0], [-0.75, -0.18], [-0.18, 0.75], [0, 0], [0, 0], [0, 0], [-0.75, -0.18], [-0.18, 0.75], [0, 0], [0, 0], [0, 0], [0, 0], [-0.49, -0.6], [-0.6, 0.49], [0, 0], [0, 0], [0, 0], [-0.49, -0.6], [-0.6, 0.49], [0, 0], [-0.01, 0.03], [0, 0.04], [0, 0], [-0.36, 0.22], [-0.05, 0.32], [0.4, 0.66], [0, 0], [0, 0], [0, 0], [-0.36, 0.22], [-0.06, 0.32], [0.41, 0.66], [0, 0], [0, 0], [0, 0], [0, 0], [-0.77, -0.08], [-0.08, 0.77], [0, 0], [0, 0], [0, 0], [-0.77, -0.08], [-0.07, 0.77], [0, 0], [0.02, 0.02], [0.02, 0.03], [0, 0], [-0.1, 0.41], [0.19, 0.27], [0.75, 0.18], [0, 0], [0, 0], [0, 0], [-0.1, 0.41], [0.19, 0.27], [0.75, 0.18], [0, 0], [0, 0], [0, 0], [0, 0], [-0.6, 0.49], [0.48, 0.59], [0, 0], [0, 0], [0, 0], [-0.6, 0.49], [0.49, 0.6], [0, 0], [0.03, 0.01], [0.03, 0], [0, 0], [0.21, 0.36], [0.32, 0.05], [0.66, -0.41], [0, 0], [0, 0], [0, 0], [0.22, 0.36], [0.32, 0.05], [0.66, -0.4], [0, 0], [0, 0], [0, 0], [0, 0], [-0.08, 0.77], [0.77, 0.08], [0, 0], [0, 0], [0, 0], [-0.08, 0.77], [0.77, 0.08], [0, 0], [0.03, -0.02], [0.03, -0.02], [0, 0], [0.41, 0.1], [0.27, -0.19], [0.18, -0.75], [0, 0], [0, 0], [0, 0], [0.41, 0.1], [0.27, -0.19], [0.18, -0.75], [0, 0], [0, 0], [0, 0], [0, 0], [0.48, 0.59], [0.59, -0.48], [0, 0], [0, 0], [0, 0], [0.49, 0.6], [0.59, -0.49], [0, 0], [0.03, -0.08], [0.02, -0.09], [0, 0], [0.66, -0.41], [-0.41, -0.66], [0, 0], [0, 0], [0, 0], [0.66, -0.41], [-0.41, -0.66], [0, 0], [0, 0], [0, 0], [0, 0], [0.77, 0.08], [0.07, -0.77], [0, 0], [0, 0], [0, 0], [0.77, 0.08], [0.08, -0.77], [0, 0]], "v": [[65.45, 24.88], [65.32, 24.65], [65.14, 24.45], [67.03, 16.5], [65.99, 14.81], [64.31, 15.85], [62.08, 25.24], [58.7, 27.64], [60.19, 21.36], [59.16, 19.67], [57.47, 20.71], [55.24, 30.1], [43.49, 38.45], [45.89, 24.24], [53.36, 18.14], [53.57, 16.17], [51.59, 15.97], [46.6, 20.05], [47.29, 15.96], [54.77, 9.86], [54.96, 7.9], [52.99, 7.7], [46.66, 12.86], [46.4, 12.79], [46.14, 12.77], [41.85, 5.82], [39.93, 5.36], [39.47, 7.29], [44.53, 15.5], [43.84, 19.59], [40.45, 14.09], [38.53, 13.63], [38.07, 15.56], [43.13, 23.77], [40.73, 37.99], [32.38, 26.25], [33.36, 16.64], [32.1, 15.11], [30.57, 16.37], [29.92, 22.79], [27.52, 19.41], [28.49, 9.8], [27.24, 8.27], [25.7, 9.53], [24.88, 17.65], [24.64, 17.79], [24.45, 17.96], [16.5, 16.07], [14.81, 17.11], [15.85, 18.8], [25.23, 21.03], [27.64, 24.41], [21.36, 22.91], [19.68, 23.95], [20.71, 25.64], [30.1, 27.87], [38.46, 39.61], [24.24, 37.21], [18.14, 29.74], [16.17, 29.54], [15.97, 31.51], [20.05, 36.51], [15.96, 35.82], [9.87, 28.34], [7.9, 28.14], [7.7, 30.11], [13.02, 36.64], [13, 36.73], [12.99, 36.84], [5.82, 41.25], [5.17, 42.21], [5.36, 43.18], [7.29, 43.64], [15.5, 38.58], [19.59, 39.27], [14.09, 42.65], [13.45, 43.61], [13.64, 44.57], [15.56, 45.03], [23.78, 39.97], [37.99, 42.37], [26.24, 50.73], [16.64, 49.75], [15.11, 51], [16.36, 52.54], [22.78, 53.19], [19.41, 55.59], [9.8, 54.62], [8.27, 55.87], [9.52, 57.4], [17.91, 58.25], [17.95, 58.34], [18.02, 58.41], [16.07, 66.61], [16.3, 67.75], [17.11, 68.3], [18.79, 67.25], [21.03, 57.87], [24.41, 55.47], [22.91, 61.75], [23.13, 62.88], [23.95, 63.43], [25.63, 62.39], [27.87, 53.01], [39.61, 44.65], [37.21, 58.86], [29.74, 64.96], [29.54, 66.93], [31.51, 67.13], [36.5, 63.05], [35.82, 67.14], [28.34, 73.24], [28.14, 75.21], [30.11, 75.41], [36.64, 70.08], [36.73, 70.11], [36.84, 70.11], [41.25, 77.29], [42.21, 77.93], [43.18, 77.75], [43.63, 75.82], [38.57, 67.61], [39.27, 63.52], [42.65, 69.01], [43.61, 69.66], [44.57, 69.46], [45.03, 67.55], [39.97, 59.33], [42.37, 45.11], [50.72, 56.86], [49.75, 66.46], [51, 67.99], [52.54, 66.75], [53.18, 60.32], [55.59, 63.7], [54.62, 73.3], [55.87, 74.83], [57.4, 73.58], [58.25, 65.2], [58.34, 65.15], [58.41, 65.08], [66.61, 67.04], [67.74, 66.81], [68.29, 66], [67.26, 64.31], [57.87, 62.08], [55.46, 58.7], [61.74, 60.2], [62.88, 59.97], [63.43, 59.16], [62.39, 57.47], [53, 55.24], [44.64, 43.49], [58.86, 45.89], [64.96, 53.37], [66.93, 53.56], [67.13, 51.6], [63.05, 46.6], [67.14, 47.29], [73.24, 54.76], [75.21, 54.96], [75.4, 53], [70.24, 46.66], [70.31, 46.41], [70.33, 46.14], [77.28, 41.86], [77.74, 39.93], [75.82, 39.47], [67.6, 44.53], [63.52, 43.84], [69.01, 40.46], [69.46, 38.53], [67.54, 38.08], [59.33, 43.13], [45.11, 40.74], [56.86, 32.38], [66.46, 33.35], [67.99, 32.11], [66.74, 30.57], [60.32, 29.92], [63.7, 27.52], [73.3, 28.49], [74.83, 27.24], [73.58, 25.71]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.96, 0.33], [0.33, -1.96], [-1.95, -0.33], [-0.33, 1.96]], "o": [[0.33, -1.96], [-1.96, -0.33], [-0.33, 1.96], [1.96, 0.33], [0, 0]], "v": [[51.5, 4.19], [48.55, 0.05], [44.41, 3], [47.36, 7.14], [51.5, 4.19]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.96, -0.33], [-0.33, 1.96], [1.96, 0.33], [0.33, -1.96]], "o": [[-0.33, 1.96], [1.96, 0.33], [0.33, -1.96], [-1.96, -0.33], [0, 0]], "v": [[31.61, 78.91], [34.55, 83.05], [38.69, 80.11], [35.75, 75.97], [31.61, 78.91]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-1.96, -0.33], [-0.33, 1.96], [1.96, 0.33], [0.33, -1.96]], "o": [[-0.33, 1.96], [1.96, 0.33], [0.33, -1.96], [-1.96, -0.33], [0, 0]], "v": [[75.97, 47.36], [78.91, 51.5], [83.05, 48.55], [80.11, 44.41], [75.97, 47.36]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.96, 0.33], [0.33, -1.96], [-1.96, -0.33], [-0.33, 1.96]], "o": [[0.33, -1.96], [-1.96, -0.33], [-0.33, 1.95], [1.96, 0.33], [0, 0]], "v": [[7.14, 35.75], [4.19, 31.61], [0.05, 34.55], [3, 38.69], [7.14, 35.75]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.81, 0.93, 1, 1]}, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "6", "layers": [{"ind": 5, "ty": 0, "ks": {}, "w": 111, "h": 111, "ip": 0, "op": 721, "st": 0, "refId": "3"}]}], "fr": 60, "h": 111, "ip": 0, "layers": [{"ind": 8, "ty": 0, "parent": 1, "ks": {"a": {"a": 0, "k": [55.5, 55.5]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 120, "s": [100], "h": 1}, {"t": 481.8, "s": [100], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 601.8, "s": [0], "h": 1}, {"t": 720, "s": [0], "h": 1}]}, "p": {"a": 0, "k": [55.5, 55.5]}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "h": 1}, {"t": 1.8, "s": [0], "i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}}, {"t": 601.8, "s": [-360], "h": 1}, {"t": 720, "s": [-360], "h": 1}]}}, "w": 111, "h": 111, "ip": 0, "op": 721, "st": 0, "refId": "6"}, {"ind": 1, "ty": 3, "parent": 0, "ks": {}, "ip": 0, "op": 721, "st": 0}, {"ind": 0, "ty": 3, "ks": {}, "ip": 0, "op": 721, "st": 0}], "meta": {"g": "https://jitter.video"}, "op": 720, "v": "5.7.4", "w": 111}