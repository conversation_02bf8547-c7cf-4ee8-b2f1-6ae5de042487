import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class MarketPlaceScreen extends StatefulWidget {
  const MarketPlaceScreen({Key? key}) : super(key: key);

  @override
  State<MarketPlaceScreen> createState() => _MarketPlaceScreenState();
}

class _MarketPlaceScreenState extends State<MarketPlaceScreen>
    with SingleTickerProviderStateMixin {
  OfferType _allOffersType = OfferType.sell;

  late TabController _tabController;
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getAllOffers();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    printty('MarketPlaceScreen disposed');
    super.dispose();
  }

  _getAllOffers() async {
    await context.read<MarketPlaceOfferVM>().getAllOffers();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MarketPlaceOfferVM>(builder: (context, vm, _) {
      return SizedBox(
        height: Sizer.screenHeight,
        width: Sizer.screenWidth,
        child: BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ).copyWith(
                  top: Sizer.height(20),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Current Offers',
                          style: AppTypography.text24.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.headerBlack,
                          ),
                        ),
                        IconBtn(
                          onTap: () {
                            Navigator.of(context)
                                .pushNamed(RoutePath.setRateAlertScreen);
                          },
                          icon: Iconsax.arrow_right_3,
                          btnText: "Set Alerts",
                        ),
                      ],
                    ),
                    const YBox(20),
                    Row(
                      children: [
                        Expanded(
                          child: CustomTextField(
                            borderRadius: Sizer.height(4),
                            prefixIcon: Icon(
                              Iconsax.search_normal_1,
                              color: AppColors.black600,
                              size: Sizer.radius(20),
                            ),
                            hintText: 'Search...',
                            // controller: vm.emailController,
                            onChanged: (val) {},
                          ),
                        ),
                        const XBox(14),
                        InkWell(
                          onTap: () {
                            Navigator.of(context)
                                .pushNamed(RoutePath.sortOffersScreen);
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(12),
                              vertical: Sizer.width(11),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.bgWhite,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: AppColors.opacityWhite,
                              ),
                            ),
                            child: svgHelper(AppSvgs.filterLines),
                          ),
                        )
                      ],
                    ),
                    const YBox(30),
                    SizedBox(
                      child: TabBar(
                        padding: EdgeInsets.zero,
                        labelPadding: EdgeInsets.only(
                          right: Sizer.width(30),
                        ),
                        controller: _tabController,
                        indicatorColor: AppColors.transparent,
                        dividerColor: AppColors.transparent,
                        tabs: [
                          TextRow(
                            onTap: () {
                              _allOffersType = OfferType.sell;
                              _tabController.animateTo(0);
                              vm.reBuildUI();
                            },
                            text: "Sell Offers",
                            value: "${vm.sellAllOffers.length}",
                            hasValue: true,
                            isSelected: _allOffersType == OfferType.sell,
                          ),
                          TextRow(
                            text: "Buy Offers",
                            value: "${vm.buyAllOffers.length}",
                            hasValue: true,
                            isSelected: _allOffersType == OfferType.buy,
                            onTap: () {
                              _allOffersType = OfferType.buy;
                              _tabController.animateTo(1);
                              vm.reBuildUI();
                            },
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: const [
                          SellOfferListTab(),
                          BuyOfferListTab(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
