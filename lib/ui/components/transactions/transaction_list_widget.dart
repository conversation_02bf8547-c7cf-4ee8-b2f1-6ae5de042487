import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransactionListWidget extends StatelessWidget {
  final List<Transaction> transactionList;
  final String title;

  const TransactionListWidget({
    super.key,
    required this.transactionList,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (transactionList.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        YBox(20),
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray500,
          ),
        ),
        const YBox(8),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            var transaction = transactionList[i];
            return TransactionListTile(
              title: transaction.title ?? "",
              subTitle: transaction.subtitle ?? "",
              amount:
                  "${transaction.type == "credit" ? "+" : "-"} ${AppUtils.formatAmountDoubleString(transaction.amount ?? "0")}  ${transaction.currency?.code ?? ""}",
              status: transaction.status ?? "",
              category: transaction.category?.toLowerCase() ?? "",
              onTap: () {
                Navigator.of(context).pushNamed(
                    RoutePath.transactionDetailsScreen,
                    arguments: TransactionArg(transaction: transaction));
              },
            );
            // return TransactionCard(
            //   category: transaction.category ?? '',
            //   type: transaction.type ?? '',
            //   subTitleWithColor: true,
            //   title: transaction.description ?? '',
            //   subTitle: transaction.status ?? '',
            //   amount:
            //       "${AppUtils.formatAmountDoubleString(transaction.amount ?? "0")}  ${transaction.currency?.code ?? ""}",
            //   onTap: () {
            //     Navigator.of(context).pushNamed(
            //         RoutePath.transactionDetailsScreen,
            //         arguments: TransactionArg(transaction: transaction));
            //   },
            // );
          },
          separatorBuilder: (ctx, _) => const YBox(20),
          itemCount: transactionList.length,
        ),
      ],
    );
  }
}
