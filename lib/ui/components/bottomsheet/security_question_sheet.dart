import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SecurityQuestionSheet extends StatefulWidget {
  const SecurityQuestionSheet({
    Key? key,
    required this.questLevelsType,
  }) : super(key: key);

  final QuestLevelsType questLevelsType;

  @override
  State<SecurityQuestionSheet> createState() => _SecurityQuestionSheetState();
}

class _SecurityQuestionSheetState extends State<SecurityQuestionSheet> {
  int _selectedIndex = -1;
  @override
  Widget build(BuildContext context) {
    return Consumer<SecQuestVM>(builder: (context, vm, _) {
      return ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.66,
        child: Column(
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(10),
            vm.secQuestions.isEmpty
                ? Expanded(
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Iconsax.bank,
                            size: Sizer.radius(40),
                          ),
                          const YBox(10),
                          Text(
                            "No Security Questions Available",
                            style: AppTypography.text16.copyWith(
                              color: AppColors.textBlack600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : Expanded(
                    child: ListView(
                      shrinkWrap: true,
                      padding: EdgeInsets.only(
                        top: Sizer.height(10),
                      ),
                      children: [
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (ctx, i) {
                            var secQuest = vm.getAvailableQuestions[i];
                            return InkWell(
                              onTap: () {
                                _selectedIndex = i;
                                vm.reBuildUI;

                                _setSecurityQuestion(
                                    secQuest.question ?? "", secQuest.id!);
                                Navigator.pop(context);
                              },
                              child: QuestCard(
                                secQuest: secQuest.question ?? "",
                                isSelected: _selectedIndex == i,
                              ),
                            );
                          },
                          separatorBuilder: (ctx, _) => const YBox(16),
                          itemCount: vm.getAvailableQuestions.length,
                        ),
                        const YBox(100),
                      ],
                    ),
                  ),
          ],
        ),
      );
    });
  }

  _setSecurityQuestion(String secQuest, int id) {
    var secQuestVm = context.read<SecQuestVM>();
    switch (widget.questLevelsType) {
      case QuestLevelsType.first:
        secQuestVm
          ..setQuestionId(SecConst.sec1, id)
          ..setQuestion(SecConst.sec1, secQuest);
        break;
      case QuestLevelsType.second:
        secQuestVm
          ..setQuestionId(SecConst.sec2, id)
          ..setQuestion(SecConst.sec2, secQuest);
        break;
      case QuestLevelsType.third:
        secQuestVm
          ..setQuestionId(SecConst.sec3, id)
          ..setQuestion(SecConst.sec3, secQuest);
        break;
      default:
    }
  }
}

class QuestCard extends StatelessWidget {
  const QuestCard({
    super.key,
    required this.secQuest,
    this.isSelected = false,
  });

  final String secQuest;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return ContainerWithBluewishBg(
      bgColor: isSelected ? AppColors.primaryBlue : AppColors.blue100,
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(10),
        horizontal: Sizer.width(16),
      ),
      child: Text(
        secQuest,
        style: AppTypography.text14.copyWith(
          color: isSelected ? AppColors.blue100 : AppColors.textBlack600,
        ),
      ),
    );
  }
}
