import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AccounntLimitScreen extends StatefulWidget {
  const AccounntLimitScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  State<AccounntLimitScreen> createState() => _AccounntLimitScreenState();
}

class _AccounntLimitScreenState extends State<AccounntLimitScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int selectedIndex = 0;

  @override
  initState() {
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context
          .read<CurrencyVM>()
          .getCurrenciesLimits(currencyId: widget.currency.id);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: "Account Limits",
          ),
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: Text(
                  "View your usage and available limits",
                  style: AppTypography.text16.copyWith(color: AppColors.gray93),
                ),
              ),
              const YBox(24),
              Container(
                height: Sizer.height(42),
                width: Sizer.screenWidth,
                margin: EdgeInsets.symmetric(
                  horizontal: Sizer.width(30),
                ),
                decoration: BoxDecoration(
                  color: AppColors.grayFE,
                  borderRadius: BorderRadius.circular(Sizer.radius(60)),
                  border: Border.all(width: 1, color: AppColors.grayF0),
                ),
                child: TabBar(
                  // splashBorderRadius: BorderRadius.circular(Sizer.radius(12)),
                  physics: const NeverScrollableScrollPhysics(),
                  onTap: (int value) {
                    selectedIndex = value;
                    setState(() {});
                  },
                  dividerColor: Colors.transparent,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(Sizer.radius(60)),
                    color: AppColors.primaryBlue,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelColor: AppColors.white,
                  automaticIndicatorColorAdjustment: true,
                  labelStyle: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  unselectedLabelStyle: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                  controller: _tabController,
                  tabs: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          height: Sizer.height(20),
                          selectedIndex == 0
                              ? AppSvgs.sendLimitA
                              : AppSvgs.sendLimit,
                        ),
                        const XBox(4),
                        Text(
                          'Send Limit',
                          style: AppTypography.text12.copyWith(
                            fontWeight: FontWeight.w600,
                            color: selectedIndex == 0
                                ? AppColors.white
                                : AppColors.primaryBlue,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          height: Sizer.height(20),
                          selectedIndex == 1
                              ? AppSvgs.receiveLimitA
                              : AppSvgs.receiveLimit,
                        ),
                        const XBox(4),
                        Text(
                          'Receive Limit',
                          style: AppTypography.text12.copyWith(
                            fontWeight: FontWeight.w600,
                            color: selectedIndex == 1
                                ? AppColors.white
                                : AppColors.primaryBlue,
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              // SizedBox(
              //   height: Sizer.height(40),
              //   child: ListView.separated(
              //     shrinkWrap: true,
              //     scrollDirection: Axis.horizontal,
              //     padding: EdgeInsets.only(
              //       left: Sizer.width(24),
              //     ),
              //     itemBuilder: (ctx, i) {
              //       var currencyLimit = vm.isCreatableCurrenciesLimits[i];
              //       return CodeCard(
              //         onTap: () {
              //           setState(() {
              //             _selectedIndex = i;
              //           });
              //           vm.setSelectedCurrencyLimit(currencyLimit.code);
              //         },
              //         isSelected: _selectedIndex == i,
              //         code: currencyLimit.code ?? "",
              //       );
              //     },
              //     separatorBuilder: (ctx, _) => const XBox(24),
              //     itemCount: vm.isCreatableCurrenciesLimits.length,
              //   ),
              // ),
              const YBox(36),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      child: Column(
                        children: buildAccountLimitCards(
                          showBorder: true,
                          limits:
                              vm.getCurrencyLimit(widget.currency.code ?? ''),
                          currency: widget.currency,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      child: Column(
                        children: [
                          YBox(60),
                          SvgPicture.asset(
                            AppSvgs.noLimitCard,
                            height: Sizer.height(200),
                          ),
                          YBox(20),
                          Text(
                            "No Receiving Limit",
                            style: AppTypography.text18.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          YBox(8),
                          Text(
                            "You currently do not have a limit on how much you can receive",
                            textAlign: TextAlign.center,
                            style: AppTypography.text14.copyWith(
                              color: AppColors.gray808,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}

class CodeCard extends StatelessWidget {
  const CodeCard({
    super.key,
    this.isSelected = false,
    required this.code,
    this.onTap,
  });

  final bool isSelected;
  final String code;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(25),
          vertical: Sizer.height(13),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : AppColors.gray100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          code,
          style: AppTypography.text12.copyWith(
            color: isSelected ? AppColors.white : AppColors.darkBlue,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class LimitListTile extends StatelessWidget {
  const LimitListTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.trailingText,
    required this.percent,
  });

  final String title;
  final String subtitle;
  final String trailingText;

  /// % btw 0 - 100
  final double percent;

  @override
  Widget build(BuildContext context) {
    return ContainerWithBluewishBg(
      padding: EdgeInsets.zero,
      height: Sizer.height(60),
      child: Stack(
        children: [
          Container(
            height: Sizer.height(60),
            width: Sizer.width(convertPercToWidth(percent, context)),
            decoration: const BoxDecoration(
              color: AppColors.limitColor,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(15),
              vertical: Sizer.height(7),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                    const YBox(4),
                    Text(
                      subtitle,
                      style: AppTypography.text12.copyWith(
                        color: AppColors.darkGreen,
                      ),
                    )
                  ],
                ),
                const Spacer(),
                Text(
                  trailingText,
                  style: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  double convertPercToWidth(double percent, BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return (size.width - 80) * (percent / 100);
  }
}
