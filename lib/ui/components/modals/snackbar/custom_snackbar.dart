// import 'package:iconsax/iconsax.dart';
// import 'package:korrency/core/core.dart';

// class CustomSnackbar {
//   static DateTime? _lastSnackTime; // Track the last snack bar display time
//   static void showSnackBar(BuildContext context, String message,
//       {SnackBarType snackBarType = SnackBarType.warning}) {
//     // Check if less than 5 seconds have passed since the last snackbar was displayed
//     if (_lastSnackTime != null &&
//         DateTime.now().difference(_lastSnackTime!) <
//             const Duration(seconds: 5)) {
//       printty("Less than 5 seconds have passed since the last snackbar.");
//       return; // Exit early if the condition is met
//     }

//     // Update the last snackbar display time
//     _lastSnackTime = DateTime.now();
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         padding: EdgeInsets.zero,
//         duration: const Duration(seconds: 5),
//         content: Container(
//           padding: const EdgeInsets.symmetric(
//             horizontal: 16,
//             vertical: 8,
//           ),
//           color: snackBarType.bgColor,
//           child: Row(
//             children: [
//               Icon(
//                 snackBarType.icon,
//                 size: 30,
//                 color: snackBarType.iconColor,
//               ),
//               const XBox(10),
//               Expanded(
//                 child: Text(
//                   message,
//                   overflow: TextOverflow.ellipsis,
//                   maxLines: 2,
//                   style: AppTypography.text14.copyWith(
//                     color: AppColors.blue800,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//         margin: EdgeInsets.only(
//             bottom: MediaQuery.of(context).size.height - 300,
//             right: 20,
//             left: 20),
//         behavior: SnackBarBehavior.floating,
//         backgroundColor: AppColors.red,
//         elevation: 0,
//       ),
//     );
//   }
// }

// class SnackBarType {
//   const SnackBarType({
//     required this.bgColor,
//     required this.iconColor,
//     required this.icon,
//   });
//   final Color bgColor;
//   final Color iconColor;
//   final IconData icon;

//   static const SnackBarType warning = SnackBarType(
//     bgColor: AppColors.opacityRed100,
//     iconColor: AppColors.iconRed,
//     icon: Iconsax.information5,
//   );

//   static const SnackBarType success = SnackBarType(
//     bgColor: AppColors.opacityGreen200,
//     iconColor: AppColors.textGreen,
//     icon: Icons.check_circle,
//   );
// }
