import 'package:korrency/core/core.dart';

class CreateOfferVM extends BaseVM {
  TextEditingController fromC = TextEditingController();
  TextEditingController rateC = TextEditingController();

  Offers? _createdOffer;
  Offers? get createdOffer => _createdOffer;
  bool _allowPartialAmt = false;
  bool get allowPartialAmt => _allowPartialAmt;
  String? _partialAmt;
  String? get partialAmt => _partialAmt;
  TransactionFee? _transactionFee;
  TransactionFee? get transactionFee => _transactionFee;
  // String? _fees;
  // String? get fees => _fees;
  String? _pin;
  String? get pin => _pin;

  String get buyAmountYouWillPay {
    double doublefrom =
        double.tryParse(fromC.text.trim().replaceAll(",", "")) ?? 0;
    double doubleRate =
        double.tryParse(rateC.text.trim().replaceAll(",", "")) ?? 0;

    return (doublefrom * doubleRate).toString();
  }

  String get sellAmountYouWillPay {
    double doublefrom =
        double.tryParse(fromC.text.trim().replaceAll(",", "")) ?? 0;
    double doubleFee = double.tryParse(_transactionFee?.fee ?? "0") ?? 0;
    // double percentFee = doubleFee / 100;
    double total = 0;
    if (_transactionFee?.method == OfferConst.multiply) {
      total = (doubleFee * doublefrom) + doublefrom;
    } else {
      total = doubleFee + doublefrom;
    }

    return total.toString();
  }

  String get amountYouWillReceiveSellOffer {
    double doublefrom =
        double.tryParse(fromC.text.trim().replaceAll(",", "")) ?? 0;
    double doubleRate =
        double.tryParse(rateC.text.trim().replaceAll(",", "")) ?? 0;

    double total = doublefrom * doubleRate;

    return AppUtils.formatAmountDoubleString(total.toString());
  }

  String get ourFee {
    double doublefrom =
        double.tryParse(fromC.text.trim().replaceAll(",", "")) ?? 0;
    double doubleFee = double.tryParse(_transactionFee?.fee ?? "0") ?? 0;
    // double percentFee = doubleFee / 100;
    printty(doubleFee, level: "Our Fee");
    printty(doublefrom, level: "Our from");

    if (_transactionFee?.method == OfferConst.multiply) {
      return AppUtils.formatAmountDoubleString(
          (doublefrom * doubleFee).toString());
    } else {
      return AppUtils.formatAmountDoubleString(_transactionFee?.fee ?? "0");
    }
  }

  setPin(String value) {
    _pin = value;
    reBuildUI();
  }

  setPartialAmt(String value) {
    _partialAmt = value;
    reBuildUI();
  }

  setAllowPartialAmt(bool value) {
    _allowPartialAmt = value;
    value ? _partialAmt = "500" : _partialAmt = null;
    printty(_allowPartialAmt, level: "Allow Partial Amt");
    reBuildUI();
  }

  void convertAmount(double amount) {
    reBuildUI();
  }

  Future<ApiResponse> createOffer({
    required int askingId,
    required int tradingId,
    required String offerType,
  }) async {
    var body = {
      "trading_currency_id": tradingId,
      "asking_currency_id": askingId,
      "rate": rateC.text.trim().replaceAll(",", ""),
      "type": offerType,
      "allows_partial_trade": _allowPartialAmt ? 1 : 0,
      "minimum_partial_trade_amount": _partialAmt,
      "amount": fromC.text.trim().replaceAll(",", ""),
      "pin": _pin,
    };

    printty(body, level: "Create Offer");
    return await performApiCall(
      url: "/marketplace/offers",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        _createdOffer = Offers.fromJson(data["data"]);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> adjustTimeOffer(
      {required int id, required int time}) async {
    printty(id, level: "Adjust Offer");
    return await performApiCall(
      url: "/marketplace/offers/$id/adjust",
      method: apiService.postWithAuth,
      body: {
        "time": time,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getFees(String type) async {
    return await performApiCall(
      url: "/transactions/fees/$type",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _transactionFee = TransactionFee.fromJson(data["data"]);
        printty(_transactionFee, level: "Fees");
        return apiResponse;
      },
    );
  }

  clearData() {
    fromC.clear();
    rateC.clear();
    _partialAmt = null;
    _transactionFee = null;
    _pin = null;
    _allowPartialAmt = false;

    reBuildUI();
  }

  @override
  void dispose() {
    printty("MarketplaceVM Disposed called");
    fromC.dispose();
    rateC.dispose();

    super.dispose();
  }
}
