// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class UpdateAvailableScreen extends StatelessWidget {
//   const UpdateAvailableScreen({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return ContainerWithTopBorderRadius(
//         child: Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         const YBox(30),
//         imageHelper(
//           AppImages.occupation,
//           height: Sizer.height(200),
//           width: Sizer.width(200),
//         ),
//         const YBox(10),
//         Text(
//           'Update your occupation',
//           style: AppTypography.text20.copyWith(
//             fontWeight: FontWeight.w600,
//           ),
//         ),
//         const YBox(4),
//         Text(
//           'To keep your profile accurate, please update \nyour occupation',
//           textAlign: TextAlign.center,
//           style: AppTypography.text16.copyWith(
//             color: AppColors.textBlack800,
//           ),
//         ),
//         const YBox(40),
//         CustomBtn.solid(
//           // online: true,
//           onTap: () {
//             Navigator.pop(context);
//             BsWrapper.bottomSheet(
//               context: context,
//               canDismiss: false,
//               widget: const OccupationSheet(
//                 fromDashboard: true,
//               ),
//             );
//           },
//           text: "Continue",
//         ),
//         const YBox(40),
//       ],
//     ));
//   }
// }
