// import 'dart:convert';

// CoversionRate coversionRateFromJson(String str) =>
//     CoversionRate.fromJson(json.decode(str));

// String coversionRateToJson(CoversionRate data) => json.encode(data.toJson());

// class CoversionRate {
//   int? id;
//   String? name;
//   String? code;
//   String? country;
//   String? category;
//   String? symbol;
//   String? flag;
//   Rate? rate;

//   CoversionRate({
//     this.id,
//     this.name,
//     this.code,
//     this.country,
//     this.category,
//     this.symbol,
//     this.flag,
//     this.rate,
//   });

//   factory CoversionRate.fromJson(Map<String, dynamic> json) => CoversionRate(
//         id: json["id"],
//         name: json["name"],
//         code: json["code"],
//         country: json["country"],
//         category: json["category"],
//         symbol: json["symbol"],
//         flag: json["flag"],
//         rate: json["rate"] == null ? null : Rate.fromJson(json["rate"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "name": name,
//         "code": code,
//         "country": country,
//         "category": category,
//         "symbol": symbol,
//         "flag": flag,
//         "rate": rate?.toJson(),
//       };
// }

// class Rate {
//   int? currencyId;
//   String? flag;
//   String? to;
//   String? rate;
//   String? newCustomerRate;
//   String? rateFormat;
//   String? newCustomerRateFormat;
//   String? multiplyBy;
//   String? newCustomerMultiplyBy;

//   Rate({
//     this.currencyId,
//     this.flag,
//     this.to,
//     this.rate,
//     this.newCustomerRate,
//     this.rateFormat,
//     this.newCustomerRateFormat,
//     this.multiplyBy,
//     this.newCustomerMultiplyBy,
//   });

//   factory Rate.fromJson(Map<String, dynamic> json) => Rate(
//         currencyId: json["currency_id"],
//         flag: json["flag"],
//         to: json["to"],
//         rate: json["rate"],
//         newCustomerRate: json["new_customer_rate"],
//         rateFormat: json["rate_format"],
//         newCustomerRateFormat: json["new_customer_rate_format"],
//         multiplyBy: json["multiply_by"],
//         newCustomerMultiplyBy: json["new_customer_multiply_by"],
//       );

//   Map<String, dynamic> toJson() => {
//         "currency_id": currencyId,
//         "flag": flag,
//         "to": to,
//         "rate": rate,
//         "new_customer_rate": newCustomerRate,
//         "rate_format": rateFormat,
//         "new_customer_rate_format": newCustomerRateFormat,
//         "multiply_by": multiplyBy,
//         "new_customer_multiply_by": newCustomerMultiplyBy,
//       };
// }

// To parse this JSON data, do
//
//     final coversionRate = coversionRateFromJson(jsonString);

import 'dart:convert';

import 'package:korrency/core/core.dart';

CoversionRate coversionRateFromJson(String str) =>
    CoversionRate.fromJson(json.decode(str));

String coversionRateToJson(CoversionRate data) => json.encode(data.toJson());

class CoversionRate {
  final int? id;
  final String? name;
  final String? code;
  final String? country;
  final String? countryCode;
  final String? category;
  final String? symbol;
  final bool? isCreatable;
  final bool? isFundable;
  final List<PaymentMethod>? paymentMethods;
  final List<CoversionRate>? recipientCurrencies;
  final String? minimumTransferAmount;
  final String? flag;
  final Rate? rate;
  final dynamic deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CoversionRate({
    this.id,
    this.name,
    this.code,
    this.country,
    this.countryCode,
    this.category,
    this.symbol,
    this.isCreatable,
    this.isFundable,
    this.paymentMethods,
    this.recipientCurrencies,
    this.minimumTransferAmount,
    this.flag,
    this.rate,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory CoversionRate.fromJson(Map<String, dynamic> json) => CoversionRate(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        country: json["country"],
        countryCode: json["country_code"],
        category: json["category"],
        symbol: json["symbol"],
        isCreatable: json["is_creatable"],
        isFundable: json["is_fundable"],
        paymentMethods: json["payment_methods"] == null
            ? []
            : List<PaymentMethod>.from(
                json["payment_methods"]!.map((x) => PaymentMethod.fromJson(x))),
        recipientCurrencies: json["recipient_currencies"] == null
            ? []
            : List<CoversionRate>.from(json["recipient_currencies"]!
                .map((x) => CoversionRate.fromJson(x))),
        minimumTransferAmount: json["minimum_transfer_amount"],
        flag: json["flag"],
        rate: json["rate"] == null ? null : Rate.fromJson(json["rate"]),
        deletedAt: json["deleted_at"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "country": country,
        "country_code": countryCode,
        "category": category,
        "symbol": symbol,
        "is_creatable": isCreatable,
        "is_fundable": isFundable,
        "payment_methods": paymentMethods == null
            ? []
            : List<dynamic>.from(paymentMethods!.map((x) => x.toJson())),
        "recipient_currencies": recipientCurrencies == null
            ? []
            : List<dynamic>.from(recipientCurrencies!.map((x) => x.toJson())),
        "minimum_transfer_amount": minimumTransferAmount,
        "flag": flag,
        "rate": rate?.toJson(),
        "deleted_at": deletedAt,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Rate {
  final int? currencyId;
  final String? flag;
  final String? to;
  final String? rate;
  final String? newCustomerRate;
  final String? rateFormat;
  final String? newCustomerRateFormat;
  final String? conversionDirection;
  final String? multiplyBy;
  final String? newCustomerMultiplyBy;

  Rate({
    this.currencyId,
    this.flag,
    this.to,
    this.rate,
    this.newCustomerRate,
    this.rateFormat,
    this.newCustomerRateFormat,
    this.conversionDirection,
    this.multiplyBy,
    this.newCustomerMultiplyBy,
  });

  factory Rate.fromJson(Map<String, dynamic> json) => Rate(
        currencyId: json["currency_id"],
        flag: json["flag"],
        to: json["to"],
        rate: json["rate"],
        newCustomerRate: json["new_customer_rate"],
        rateFormat: json["rate_format"],
        newCustomerRateFormat: json["new_customer_rate_format"],
        conversionDirection: json["conversion_direction"],
        multiplyBy: json["multiply_by"],
        newCustomerMultiplyBy: json["new_customer_multiply_by"],
      );

  Map<String, dynamic> toJson() => {
        "currency_id": currencyId,
        "flag": flag,
        "to": to,
        "rate": rate,
        "new_customer_rate": newCustomerRate,
        "rate_format": rateFormat,
        "new_customer_rate_format": newCustomerRateFormat,
        "conversion_direction": conversionDirection,
        "multiply_by": multiplyBy,
        "new_customer_multiply_by": newCustomerMultiplyBy,
      };
}
