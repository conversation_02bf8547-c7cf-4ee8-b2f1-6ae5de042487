import 'package:korrency/core/core.dart';

class SwipeIndicator extends StatelessWidget {
  final bool isActive;
  final Color activeColor;

  const SwipeIndicator({
    super.key,
    required this.isActive,
    this.activeColor = AppColors.primaryBlue,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: Sizer.height(28),
      width: Sizer.width(28),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Sizer.radius(40)),
        border: Border.all(
          color: AppColors.limitColor,
          width: 1,
        ),
        shape: BoxShape.rectangle,
      ),
      child: SizedBox(
        height: Sizer.height(6),
        width: Sizer.width(6),
        child: FittedBox(
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: isActive ? activeColor : AppColors.limitColor,
              borderRadius: BorderRadius.circular(Sizer.radius(10)),
              shape: BoxShape.rectangle,
            ),
          ),
        ),
      ),
    );
  }
}

// Alternative version with more customization options
class CustomSwiperIndicator extends StatelessWidget {
  final int itemCount;
  final int currentIndex;
  final Color primaryColor;
  final double dotSize;
  final double activeDotSize;
  final double spacing;
  final Duration animationDuration;
  final double borderWidth;
  final bool showShadow;

  const CustomSwiperIndicator({
    super.key,
    required this.itemCount,
    required this.currentIndex,
    this.primaryColor = Colors.blue,
    this.dotSize = 8.0,
    this.activeDotSize = 12.0,
    this.spacing = 12.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.borderWidth = 3.0,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(itemCount, (index) {
        final isActive = index == currentIndex;
        final distance = (index - currentIndex).abs();

        // Create different opacity levels based on distance from active
        double opacity = isActive
            ? 1.0
            : distance == 1
                ? 0.5
                : 0.3;

        return AnimatedContainer(
          padding: EdgeInsets.all(Sizer.radius(11)),
          duration: animationDuration,
          curve: Curves.easeInOut,
          margin: EdgeInsets.symmetric(horizontal: spacing / 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(Sizer.radius(40)),
            border: isActive
                ? Border.all(
                    color: AppColors.grayF0,
                  )
                : null,
          ),
          child: AnimatedContainer(
            duration: animationDuration,
            curve: Curves.easeInOut,
            width: isActive ? activeDotSize : dotSize,
            height: isActive ? activeDotSize : dotSize,
            decoration: BoxDecoration(
              color:
                  isActive ? primaryColor : primaryColor.withOpacity(opacity),
              shape: BoxShape.circle,
            ),
          ),
        );
      }),
    );
  }
}
