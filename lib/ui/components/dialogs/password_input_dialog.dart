// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class PasswordInputDialog extends StatefulWidget {
  const PasswordInputDialog({
    super.key,
    this.dontConfirmPassword = false,
  });

  final bool dontConfirmPassword;

  @override
  State<PasswordInputDialog> createState() => _PasswordInputDialogState();
}

class _PasswordInputDialogState extends State<PasswordInputDialog> {
  final passwordC = TextEditingController();
  final focusNode = FocusNode();

  @override
  dispose() {
    passwordC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          decoration: BoxDecoration(
            color: AppColors.bgWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const YBox(28),
              Text(
                'Please Input your Password',
                style: FontTypography.text22.semiBold,
              ),
              YBox(4),
              Text(
                'Please input the password for this account to proceed',
                textAlign: TextAlign.center,
                style: FontTypography.text16.withCustomColor(
                  AppColors.gray93,
                ),
              ),
              const YBox(32),
              CustomTextField(
                controller: passwordC,
                focusNode: focusNode,
                labelText: "Password",
                showLabelHeader: true,
                borderRadius: Sizer.height(12),
                isPassword: true,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(Sizer.radius(12)),
                  child: SvgPicture.asset(
                    AppSvgs.passwordCheck,
                  ),
                ),
                onChanged: (val) {
                  setState(() {});
                },
                onSubmitted: (val) {
                  focusNode.unfocus();
                },
              ),
              const YBox(50),
              CustomBtn.solid(
                onTap: () {
                  focusNode.unfocus();
                  if (widget.dontConfirmPassword) {
                    Navigator.pop(context, passwordC.text.trim());
                    return;
                  }
                  loginVm.confirmPassword(passwordC.text.trim()).then((value) {
                    if (value.success) {
                      Navigator.pop(context, true);
                    } else {
                      FlushBarToast.fLSnackBar(
                        message: value.message.toString(),
                      );
                    }
                  });
                },
                online: passwordC.text.trim().length > 5,
                isLoading: loginVm.isBusy,
                borderRadius: BorderRadius.circular(Sizer.radius(14)),
                text: "Proceed",
              ),
              const YBox(30),
            ],
          ),
        ),
      );
    });
  }
}
