import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class P2pWelcomeScreen extends StatelessWidget {
  const P2pWelcomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            children: [
              const YBox(10),
              Center(
                child: imageHelper(
                  AppImages.p2p,
                  height: Sizer.height(280),
                  width: Sizer.width(280),
                ),
              ),
              const YBox(10),
              Text(
                "Welcome to the P2P \nMarketplace",
                textAlign: TextAlign.center,
                style: AppTypography.text24.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.headerBlack,
                ),
              ),
              const Y<PERSON><PERSON>(16),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text:
                          'The marketplace is easy to navigate. You can simply - Browse Buy Offers if you have CAD and need NGN',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.textBlack800,
                        height: 1.4,
                        fontFamily: "Inter",
                      ),
                    ),
                    TextSpan(
                      text: ' OR ',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.textBlack800,
                        fontWeight: FontWeight.w700,
                        fontFamily: "Inter",
                      ),
                    ),
                    TextSpan(
                      text:
                          'you can browse Sell Offers if you Need CAD and Have NGN',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.textBlack800,
                        height: 1.4,
                        fontFamily: "Inter",
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Consumer<DashboardVM>(
                builder: (context, vm, _) {
                  return ValidationItemWidget(
                    label: "Dont show me this again",
                    isValid: vm.doNotShowP2pWelcomeScreen,
                    onTap: () => vm
                        .toggleP2pWelcomeScreen(!vm.doNotShowP2pWelcomeScreen),
                  );
                },
              ),
              const YBox(20),
              CustomBtn.solid(
                onTap: () {
                  context.read<DashboardVM>().setHasSeenP2pWelcomeScreen(true);
                  // Navigator.of(context)
                  //     .pushReplacementNamed(RoutePath.marketPlaceScreen);
                },
                online: true,
                text: "Get Started",
              ),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}
