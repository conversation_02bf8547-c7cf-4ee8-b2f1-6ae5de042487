# Comprehensive Analytics Implementation Guide

This guide provides complete setup instructions for implementing comprehensive analytics tracking in your Flutter app with proper attribution logic for AppsFlyer, Firebase Analytics, and Facebook Analytics.

## Overview

The implementation includes:

- **AppsFlyer**: Tracks ALL users from ALL marketing channels
- **Firebase Analytics**: Tracks ONLY users from Google Ads campaigns
- **Facebook Analytics**: Tracks ONLY users from Facebook Ads campaigns
- **Attribution Service**: Determines user acquisition source and routes events appropriately

## 1. Environment Configuration

### 1.1 Update Environment Files

Add the following variables to your `.env.dev`, `.env.staging`, and `.env.prod` files:

```bash
# Facebook Analytics Configuration
FACEBOOK_APP_ID=****************
FACEBOOK_CLIENT_TOKEN=********************************

# AppsFlyer Configuration (if not already present)
AF_DEV_KEY=your_appsflyer_dev_key
AF_APP_ID=your_ios_app_store_id
```

## 2. AppsFlyer Dashboard Setup

### 2.1 Configure App in AppsFlyer Dashboard

1. **Login to AppsFlyer Dashboard**: https://hq1.appsflyer.com/
2. **Add Your App** (if not already added):
   - Go to "My Apps" → "Add App"
   - Select platform (iOS/Android)
   - Enter app details and bundle ID/package name
3. **Get Dev Key**:
   - Go to "App Settings" → "App Details"
   - Copy the "Dev Key" and add to your `.env` files
4. **Configure Media Sources**:
   - Go to "Configuration" → "Media Sources"
   - Enable Facebook and Google Ads
   - Configure postback URLs for conversion tracking

### 2.2 Set Up Conversion Events

Configure the following events in AppsFlyer:

- `create_account` (Create Account)
- `complete_kyc` (Complete KYC)
- `deposit_money` (Deposit Money)
- `send_money` (Send Money)

## 3. Firebase Analytics Setup

### 3.1 Firebase Console Configuration

1. **Access Firebase Console**: https://console.firebase.google.com/
2. **Select Your Project**: `korrency-mobile-app-dev`
3. **Enable Analytics**:
   - Go to "Analytics" → "Dashboard"
   - Ensure Analytics is enabled for your project
4. **Configure Google Ads Linking**:
   - Go to "Project Settings" → "Integrations"
   - Link your Google Ads account
   - Enable conversion tracking

### 3.2 Custom Events Configuration

Set up the following custom events in Firebase Analytics:

- `sign_up` (Create Account)
- `complete_kyc` (Complete KYC)
- `purchase` (Deposit Money & Send Money)
- `deposit_money` (Custom event for deposits)
- `send_money` (Custom event for transfers)

### 3.3 Google Ads Conversion Tracking

1. **Create Conversion Actions** in Google Ads:
   - Account Creation
   - KYC Completion
   - First Deposit
   - Money Transfer
2. **Link Firebase Events** to Google Ads conversions
3. **Set up Enhanced Conversions** for better attribution

## 4. Facebook Business Manager Setup

### 4.1 Facebook App Configuration

1. **Access Facebook Developers**: https://developers.facebook.com/
2. **Select Your App**: App ID `****************`
3. **Configure App Events**:
   - Go to "App Dashboard" → "App Events"
   - Verify the app is properly configured
4. **Set Up Conversions API** (recommended):
   - Go to "Marketing API" → "Conversions API"
   - Configure server-side event tracking

### 4.2 Facebook Ads Manager Setup

1. **Access Ads Manager**: https://business.facebook.com/
2. **Configure Custom Conversions**:
   - Go to "Events Manager" → "Custom Conversions"
   - Create conversions for:
     - CompleteRegistration (Account Creation)
     - CompleteKYC (KYC Completion)
     - Purchase (Deposits and Transfers)
3. **Set Up Attribution Windows**:
   - Configure 1-day view, 7-day click attribution
   - Enable advanced matching for better attribution

## 5. Implementation Usage

### 5.1 Basic Event Tracking

```dart
// Track user registration
await UnifiedAnalyticsManager.instance.trackCreateAccount(
  userId: 'user123',
  method: 'email',
  additionalParameters: {
    'registration_source': 'mobile_app',
  },
);

// Track KYC completion
await UnifiedAnalyticsManager.instance.trackCompleteKyc(
  userId: 'user123',
  kycLevel: 'level_1',
  verificationMethod: 'document_upload',
);

// Track deposit
await UnifiedAnalyticsManager.instance.trackDepositMoney(
  userId: 'user123',
  amount: 100.0,
  currency: 'CAD',
  paymentMethod: 'bank_transfer',
  corridor: 'CA-NG',
);

// Track money transfer
await UnifiedAnalyticsManager.instance.trackSendMoney(
  userId: 'user123',
  amount: 50.0,
  fromCurrency: 'CAD',
  toCurrency: 'NGN',
  corridor: 'CA-NG',
  transactionId: 'txn_456',
);
```

### 5.2 User Properties

```dart
// Set user properties for enhanced targeting
await UnifiedAnalyticsManager.instance.setUserProperties(
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  country: 'CA',
  customProperties: {
    'user_tier': 'premium',
    'registration_date': '2024-01-15',
  },
);
```

## 6. Attribution Logic

The system automatically determines user acquisition source and routes events:

- **Facebook Ads Users**: Events sent to Facebook Analytics only
- **Google Ads Users**: Events sent to Firebase Analytics only
- **All Users**: Events always sent to AppsFlyer for comprehensive tracking
- **Organic Users**: Only tracked in AppsFlyer

## 7. Testing and Validation

### 7.1 Test Attribution

1. **Install via Facebook Ad**: Verify events appear in Facebook Events Manager
2. **Install via Google Ad**: Verify events appear in Firebase Analytics
3. **Organic Install**: Verify events only appear in AppsFlyer

### 7.2 Debug Mode

Enable debug logging by checking console output for:

- `✅ Event logged successfully`
- `🚫 Skipping event - user not from [platform]`
- Attribution source detection logs

### 7.3 Validation Tools

- **AppsFlyer**: Use AppsFlyer's Real-Time Dashboard
- **Firebase**: Use Firebase Analytics DebugView
- **Facebook**: Use Facebook Analytics Events Testing Tool

## 8. Production Deployment

### 8.1 Pre-Launch Checklist

- [ ] All environment variables configured
- [ ] AppsFlyer dev key valid for production
- [ ] Firebase Analytics enabled
- [ ] Facebook App Events configured
- [ ] Attribution logic tested
- [ ] Conversion tracking verified

### 8.2 Monitoring

Monitor the following metrics:

- Event delivery rates across platforms
- Attribution accuracy
- Conversion tracking performance
- User acquisition costs by channel

## 9. Troubleshooting

### Common Issues

1. **Events not appearing in Facebook**: Check attribution logic and Facebook App ID
2. **Firebase events missing**: Verify Google Ads attribution and Firebase configuration
3. **AppsFlyer 403 errors**: Validate dev key and app registration
4. **Attribution not working**: Check AppsFlyer conversion data callbacks

### Debug Commands

```bash
# Check dependencies
flutter pub deps

# Verify Firebase configuration
flutter packages pub run build_runner build

# Test on device
flutter run --debug
```

## 10. Support and Resources

- **AppsFlyer Documentation**: https://support.appsflyer.com/
- **Firebase Analytics Guide**: https://firebase.google.com/docs/analytics
- **Facebook App Events**: https://developers.facebook.com/docs/app-events
- **Flutter Integration Guides**: Available in respective platform documentation

For technical support, refer to the implementation code comments and service documentation within the codebase.

## 11. Integration Examples

### 11.1 User Registration Flow

```dart
// In your registration completion handler
class RegistrationService {
  Future<void> completeRegistration(String userId, String email) async {
    // Set user ID across all analytics platforms
    await UnifiedAnalyticsManager.instance.setUserId(userId);

    // Set user properties
    await UnifiedAnalyticsManager.instance.setUserProperties(
      email: email,
      customProperties: {
        'registration_timestamp': DateTime.now().toIso8601String(),
      },
    );

    // Track registration event
    await UnifiedAnalyticsManager.instance.trackCreateAccount(
      userId: userId,
      method: 'email',
      additionalParameters: {
        'platform': Platform.isIOS ? 'ios' : 'android',
      },
    );
  }
}
```

### 11.2 KYC Completion Flow

```dart
// In your KYC completion handler
class KYCService {
  Future<void> completeKYC(String userId, String kycLevel) async {
    await UnifiedAnalyticsManager.instance.trackCompleteKyc(
      userId: userId,
      kycLevel: kycLevel,
      verificationMethod: 'document_upload',
      additionalParameters: {
        'kyc_completion_time': DateTime.now().toIso8601String(),
        'documents_submitted': ['passport', 'address_proof'],
      },
    );
  }
}
```

### 11.3 Transaction Flow

```dart
// In your transaction completion handler
class TransactionService {
  Future<void> completeDeposit(String userId, double amount, String currency) async {
    await UnifiedAnalyticsManager.instance.trackDepositMoney(
      userId: userId,
      amount: amount,
      currency: currency,
      paymentMethod: 'bank_transfer',
      corridor: 'CA-NG',
      additionalParameters: {
        'transaction_timestamp': DateTime.now().toIso8601String(),
        'payment_processor': 'stripe',
      },
    );
  }

  Future<void> completeTransfer(String userId, double amount, String fromCurrency, String toCurrency, String transactionId) async {
    await UnifiedAnalyticsManager.instance.trackSendMoney(
      userId: userId,
      amount: amount,
      fromCurrency: fromCurrency,
      toCurrency: toCurrency,
      corridor: '$fromCurrency-$toCurrency',
      transactionId: transactionId,
      additionalParameters: {
        'transfer_type': 'international',
        'recipient_country': 'NG',
      },
    );
  }
}
```
