import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AnotherInteracEmailScreen extends StatefulWidget {
  const AnotherInteracEmailScreen({super.key});

  @override
  State<AnotherInteracEmailScreen> createState() =>
      _AnotherInteracEmailScreenState();
}

class _AnotherInteracEmailScreenState extends State<AnotherInteracEmailScreen> {
  TextEditingController interacEmailC = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void dispose() {
    interacEmailC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Add Interac email',
            onBackBtnTap: () {
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Please add your interac email for ease of transfers",
                  style: AppTypography.text15.copyWith(color: AppColors.gray93),
                ),
                const YBox(50),
                CustomTextField(
                  controller: interacEmailC,
                  focusNode: focusNode,
                  labelText: "Email",
                  showLabelHeader: true,
                  hintText: 'Enter Interac email',
                  keyboardType: KeyboardType.email,
                  errorText: !_emailIsValid(interacEmailC.text.trim()) &&
                          interacEmailC.text.trim().isNotEmpty
                      ? "Invalid Email"
                      : null,
                  borderRadius: Sizer.height(12),
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(Sizer.radius(12)),
                    child: Icon(
                      Iconsax.sms,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  onChanged: (val) {
                    vm.reBuildUI();
                  },
                ),
                const Spacer(),
                CustomBtn.withChild(
                  onTap: () {
                    _requestInteracEmail();
                  },
                  online: _emailIsValid(interacEmailC.text.trim()),
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(
                    isOnline: _emailIsValid(interacEmailC.text.trim()),
                  ),
                ),
                const YBox(40),
              ],
            ),
          ),
        ),
      );
    });
  }

  bool _emailIsValid(String email) {
    return email.trim().isNotEmpty &&
        email.contains('@') &&
        email.contains('.');
  }

  _requestInteracEmail() async {
    final res = await context.read<InteracVM>().requestInteracEmail(
          interacEmailC.text.trim(),
        );

    handleApiResponse(
      response: res,
      onSuccess: () {
        Navigator.pushNamed(
          context,
          RoutePath.verifyInteracEmailScreen,
          arguments: interacEmailC.text.trim(),
        );
      },
    );
  }
}
