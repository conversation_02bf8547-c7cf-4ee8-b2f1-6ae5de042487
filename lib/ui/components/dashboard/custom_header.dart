import 'package:korrency/core/core.dart';

class CustomHeader extends StatelessWidget {
  const CustomHeader({
    super.key,
    this.showBackBtn = false,
    this.showHeader = false,
    this.isHeaderText = true,
    this.color,
    this.headerText,
    this.headerWidget,
    this.rightWidget,
    this.onBackBtnTap,
  });

  final bool showBackBtn;
  final bool showHeader;
  final bool isHeaderText;
  final Color? color;
  final String? headerText;
  final Widget? headerWidget;
  final Widget? rightWidget;
  final VoidCallback? onBackBtnTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          showHeader ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        ArrowBack(
          color: color,
          onTap: onBackBtnTap,
        ),
        if (showHeader)
          Expanded(
            child: Container(
              padding:
                  EdgeInsets.only(right: Sizer.width(showBackBtn ? 60 : 0)),
              child: Center(
                child: isHeaderText
                    ? Text(
                        headerText ?? '',
                        style: AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w600,
                          color: color ?? AppColors.black900,
                        ),
                      )
                    : headerWidget,
              ),
            ),
          ),
        if (rightWidget != null) rightWidget!,
      ],
    );
  }
}

class ArrowBack extends StatelessWidget {
  const ArrowBack({
    super.key,
    this.color,
    this.onTap,
  });

  final Color? color;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ??
          () {
            Navigator.pop(context);
          },
      child: Icon(
        Iconsax.arrow_left_2,
        color: color ?? AppColors.black900,
      ),
    );
  }
}

class CustomAppBarHeader extends StatelessWidget
    implements PreferredSizeWidget {
  const CustomAppBarHeader({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(50)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          child,
        ],
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}
