import 'package:korrency/core/core.dart';

class RowText extends StatelessWidget {
  const RowText({
    super.key,
    required this.leftText,
    required this.rightText,
    this.isBold = false,
  });

  final String leftText;
  final String rightText;
  final bool isBold;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          leftText,
          style: AppTypography.text12.copyWith(
            color: AppColors.textGray,
            fontWeight: FontWeight.w400,
          ),
        ),
        const Spacer(),
        Text(
          rightText,
          style: AppTypography.text12.copyWith(
            color: AppColors.blue800,
            fontWeight: isBold ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
