import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/preference/preferred_language_screen.dart';

class PreferredLanguageModal extends StatefulWidget {
  const PreferredLanguageModal({super.key});

  @override
  State<PreferredLanguageModal> createState() => _PreferredLanguageModalState();
}

class _PreferredLanguageModalState extends State<PreferredLanguageModal> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    final langVm = context.watch<LanguageVM>();
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ModalCloseBtn(),
            ],
          ),
          const YBox(12),
          Container(
            height: Sizer.screenHeight * 0.5,
            width: Sizer.screenWidth,
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                YBox(20),
                Text(
                  "Preferred Language",
                  style: AppTypography.text18.copyWith(
                    color: AppColors.mainBlack,
                  ),
                ),
                YBox(20),
                Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(
                      top: Sizer.height(10),
                      bottom: Sizer.height(20),
                    ),
                    itemCount: LangModel.languages.length,
                    separatorBuilder: (_, __) => YBox(8),
                    itemBuilder: (ctx, i) {
                      final lang = LangModel.languages[i];
                      return LangListTile(
                        langCode: lang.code.toUpperCase(),
                        langName: lang.name,
                        isSelected: langVm.isSelected(lang.code),
                        onTap: () => langVm.setLanguage(context, lang.code),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
