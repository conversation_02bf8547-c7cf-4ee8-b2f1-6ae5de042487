// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({super.key});

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen> {
  // final bool _isActive = false;
  bool _useFaceId = false;
  int _timeoutMinutes = 5;
  final List<int> _timeoutOptions = [1, 3, 5, 10, 15, 30];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled).then((value) {
        setState(() {
          _useFaceId = value ?? false;
        });
      });
      StorageService.getStringItem(StorageKey.sessionTimeoutMinutes)
          .then((value) {
        final parsed = int.tryParse(value ?? "");
        setState(() {
          _timeoutMinutes = parsed != null && parsed > 0 ? parsed : 5;
        });
      });
    });
  }

  void _showTimeoutPicker() {
    BsWrapper.bottomSheet(
      context: context,
      widget: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
          vertical: Sizer.height(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Session Timeout',
              style: AppTypography.text18.semiBold,
            ),
            const YBox(8),
            Text(
              'Choose how long the app stays active without interaction.',
              style: AppTypography.text14.copyWith(color: AppColors.gray93),
            ),
            const YBox(20),
            ..._timeoutOptions.map((m) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 6),
                  child: BuildListTileSelector(
                    text: '$m minutes',
                    isSelected: _timeoutMinutes == m,
                    onTap: () async {
                      await StorageService.storeStringItem(
                          StorageKey.sessionTimeoutMinutes, m.toString());
                      setState(() {
                        _timeoutMinutes = m;
                      });
                      SessionTimeoutService.instance
                          .configure(inactivity: Duration(minutes: m));
                      Navigator.pop(context);
                    },
                  ),
                )),
            const YBox(10),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final pinVm = context.watch<TransactionPinVM>();
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Privacy and Security',
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ).copyWith(
          top: Sizer.height(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Manage all your privacy and security settings below.",
              style: FontTypography.text16.withCustomColor(AppColors.gray93),
            ),
            Expanded(
              child: Container(
                width: Sizer.screenWidth,
                color: AppColors.white,
                child: ListView(
                  padding: EdgeInsets.only(
                    top: Sizer.height(40),
                    bottom: Sizer.height(24),
                  ),
                  children: [
                    CustomMenuItem(
                      title: 'Change Transaction PIN',
                      icon: Iconsax.user_edit,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: pinVm.isBusy,
                      trailingWidget:
                          pinVm.isBusy ? CupertinoActivityIndicator() : null,
                      onPressed: () async {
                        // Navigator.pushNamed(
                        //   context,
                        //   RoutePath.answerQuestionScreen,
                        // );
                        // final pinVm = context.read<TransactionPinVM>();
                        // final res = await BsWrapper.showCustomDialog(
                        //   context,
                        //   child: const PasswordInputDialog(),
                        // );

                        // if (res is bool && res) {
                        //   final result = await pinVm.requestOtpTransactionPin();
                        //   handleApiResponse(
                        //     response: result,
                        //     onSuccess: () {
                        //       Navigator.pushNamed(
                        //         context,
                        //         RoutePath.changeTransactionPinScreen,
                        //       );
                        //     },
                        //   );
                        // }

                        Navigator.pushNamed(
                          context,
                          RoutePath.changeTransactionPinScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Change Email',
                      icon: Iconsax.sms_edit,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () async {
                        final res = await BsWrapper.showCustomDialog(
                          context,
                          child: const PasswordInputDialog(),
                        );

                        if (res is bool && res) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.newEmailScreen,
                            arguments: ChangeInfoArg(
                              recipient: "",
                              isPhone: false,
                            ),
                          );
                        }
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Change Phone Number',
                      icon: Iconsax.call,
                      showTrailing: false,
                      iconColor: AppColors.secondaryBlue,
                      onPressed: () async {
                        final res = await BsWrapper.showCustomDialog(
                          context,
                          child: const PasswordInputDialog(),
                        );

                        if (res is bool && res) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.newPhoneNumberScreen,
                            arguments: ChangeInfoArg(
                              recipient: "",
                              isPhone: true,
                            ),
                          );
                        }
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Unlock with Biometrics',
                      icon: Iconsax.finger_cricle,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: true,
                      trailingWidget: CustomSwitch(
                        value: _useFaceId,
                        onChanged: updateFingerPrint,
                      ),
                    ),
                    const YBox(30),
                    // CustomMenuItem(
                    //   title: 'Session Timeout',
                    //   icon: Iconsax.clock,
                    //   iconColor: AppColors.secondaryBlue,
                    //   showTrailing: true,
                    //   trailingWidget: Text(
                    //     '$_timeoutMinutes min',
                    //     style: AppTypography.text14.copyWith(
                    //       color: AppColors.gray93,
                    //     ),
                    //   ),
                    //   onPressed: _showTimeoutPicker,
                    // ),
                    // const YBox(30),
                    CustomMenuItem(
                      title: 'Change Password',
                      icon: Iconsax.lock_1,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.newPasswordScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: 'Trusted Device',
                      icon: AppSvgs.phone1,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.myDeviceScreen,
                        );
                      },
                    ),
                    const YBox(30),
                    CustomMenuItem(
                      title: '2FA',
                      icon: AppSvgs.ffa,
                      iconColor: AppColors.secondaryBlue,
                      showTrailing: false,
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.twoFactorAuthScreen,
                        );
                      },
                    ),
                    // const YBox(20),
                    // CustomMenuItem(
                    //   title: 'Security Question',
                    //   useImageIcon: true,
                    //   isSvg: true,
                    //   imageIcon: AppSvgs.ffa,
                    //   onPressed: () {
                    //     Navigator.pushNamed(
                    //       context,
                    //       RoutePath.securityQuestionScreen,
                    //     );
                    //   },
                    // ),
                    // const YBox(20),
                    // CustomMenuItem(
                    //   title: 'Create Transaction PIN',
                    //   useImageIcon: true,
                    //   isSvg: true,
                    //   imageIcon: AppSvgs.ffa,
                    //   onPressed: () {
                    //     BsWrapper.bottomSheet(
                    //         context: context, widget: const SetupPinScreen());
                    //   },
                    // ),
                    // const YBox(20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  updateFingerPrint(bool val) async {
    if (val) {
      // Check if biometric authentication is available before attempting
      final isAvailable = await BiometricService.isBiometricAvailable();
      if (!isAvailable) {
        final status = await BiometricService.getBiometricStatus();
        FlushBarToast.fLSnackBar(message: status);
        return;
      }

      bool isAuthenticated = await BiometricService.authenticate();
      if (isAuthenticated) {
        StorageService.storeBoolItem(StorageKey.fingerPrintIsEnabled, val);
        setState(() {
          _useFaceId = val;
        });
      } else {
        // Don't show error for user cancellation
        final status = await BiometricService.getBiometricStatus();
        if (!status.contains('canceled') && !status.contains('UserCancel')) {
          FlushBarToast.fLSnackBar(
            message: "Biometric Authentication Failed",
          );
        }
      }
    } else {
      await StorageService.removeBoolItem(StorageKey.fingerPrintIsEnabled);
      setState(() {
        _useFaceId = val;
      });
    }
  }
}
