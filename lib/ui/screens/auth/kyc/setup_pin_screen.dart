import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class SetupPinScreen extends StatefulWidget {
  const SetupPinScreen({super.key});

  @override
  State<SetupPinScreen> createState() => _SetupPinScreenState();
}

class _SetupPinScreenState extends State<SetupPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, child) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Setup Transaction Pin',
          onBackBtnTap: () {
            vm.clearData();
            Navigator.pop(context);
          },
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Enter a new 4-digit PIN which would be required for all future transactions",
                style: AppTypography.text16.copyWith(color: AppColors.gray93),
              ),
              const YBox(20),
              const YBox(40),
              Center(
                child: Pinput(
                  defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                  followingPinTheme: PinInputTheme.changePinTheme(),
                  focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                  submittedPinTheme: PinInputTheme.changePinTheme(),
                  length: 4,
                  controller: vm.pinC,
                  focusNode: pinFocusNode,
                  showCursor: true,
                  obscureText: true,
                  obscuringWidget: Container(
                    padding: EdgeInsets.only(top: Sizer.height(8)),
                    child: Text('*', style: AppTypography.text36),
                  ),
                  onChanged: (value) {
                    vm.reBuildUI();
                    if (value.length == 4) {
                      pinFocusNode.unfocus();
                    }
                  },
                  onCompleted: (pin) {},
                ),
              ),
              Spacer(),
              CustomBtn.withChild(
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.confirmPinScreen,
                  );
                },
                online: vm.pinC.text.length == 4,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                child: ContinueText(isOnline: vm.pinC.text.length == 4),
              ),
              const YBox(50),
            ],
          ),
        ),
      );
    });
  }
}
