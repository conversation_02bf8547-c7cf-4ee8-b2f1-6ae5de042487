import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DeleteDialogConfirmation extends StatelessWidget {
  const DeleteDialogConfirmation({
    super.key,
    this.isLoading = false,
    this.onConfirm,
    this.onCancel,
  });

  final bool isLoading;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          isLoading
              ? const SpinKitLoader()
              : Text(
                  "Are you sure you want to delete this trusted device?",
                  textAlign: TextAlign.center,
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
          const YBox(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: CustomBtn.solid(
                  height: 36,
                  onTap: onCancel ??
                      () {
                        Navigator.pop(context);
                      },
                  online: !isLoading,
                  text: "Cancel",
                ),
              ),
              const XBox(20),
              Expanded(
                child: CustomBtn.solid(
                  height: 36,
                  onlineColor: AppColors.red,
                  onTap: onConfirm,
                  online: !isLoading,
                  text: "Delete",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
