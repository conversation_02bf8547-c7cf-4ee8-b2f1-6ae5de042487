import 'package:flutter/material.dart';

class LangModel {
  final Locale locale;
  final String name;

  const LangModel({
    required this.locale,
    required this.name,
  });

  String get code => locale.languageCode;
  String? get countryCode => locale.countryCode;

  // Full locale string (e.g., 'en_US', 'en_GB', 'fr')
  String get localeString => locale.toString();

  static const List<LangModel> _languages = [
    LangModel(
      locale: Locale('en', 'US'),
      name: 'English (United States)',
    ),
    LangModel(
      locale: Locale('en', 'GB'),
      name: 'English (United Kingdom)',
    ),
    LangModel(
      locale: Locale('fr'),
      name: 'Français',
    ),
  ];

  static List<LangModel> get languages => _languages;

  // Get list of Locale objects for Flutter's localizationsDelegates
  static List<Locale> get supportedLocales =>
      _languages.map((lang) => lang.locale).toList();

  // Find language model by locale
  static LangModel? findByLocale(Locale locale) {
    try {
      return _languages.firstWhere(
        (lang) => lang.locale == locale,
      );
    } catch (e) {
      return null;
    }
  }

  // Find language model by language code and optional country code
  static LangModel? findByCode(String languageCode, [String? countryCode]) {
    try {
      return _languages.firstWhere(
        (lang) =>
            lang.locale.languageCode == languageCode &&
            (countryCode == null || lang.locale.countryCode == countryCode),
      );
    } catch (e) {
      return null;
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LangModel &&
          runtimeType == other.runtimeType &&
          locale == other.locale;

  @override
  int get hashCode => locale.hashCode;

  @override
  String toString() => 'LangModel(locale: $locale, name: $name)';
}
