import 'dart:convert';

import 'package:korrency/core/core.dart';

List<Offers> offersFromJson(String str) =>
    List<Offers>.from(json.decode(str).map((x) => Offers.fromJson(x)));

String offersToJson(List<Offers> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Offers {
  int? id;
  User? user;
  String? tradingAmount;
  IngCurrency? tradingCurrency;
  String? tradingFees;
  String? askingAmount;
  IngCurrency? askingCurrency;
  String? askingFees;
  String? type;
  String? rate;
  String? rateValue;
  bool? allowsPartialTrade;
  String? minimumPartialTradeAmount;
  String? balance;
  String? deliveryMethod;
  String? acceptingTradeText;
  String? p2pFees;
  String? partialAmountPaid;
  String? status; //active, completed, expired
  DateTime? expiresAt;
  DateTime? createdAt;

  Offers({
    this.id,
    this.user,
    this.tradingAmount,
    this.tradingCurrency,
    this.tradingFees,
    this.askingAmount,
    this.askingCurrency,
    this.askingFees,
    this.type,
    this.rate,
    this.rateValue,
    this.allowsPartialTrade,
    this.minimumPartialTradeAmount,
    this.balance,
    this.deliveryMethod,
    this.acceptingTradeText,
    this.p2pFees,
    this.partialAmountPaid,
    this.status,
    this.expiresAt,
    this.createdAt,
  });

  factory Offers.fromJson(Map<String, dynamic> json) => Offers(
        id: json["id"],
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        tradingAmount: json["trading_amount"],
        tradingCurrency: json["trading_currency"] == null
            ? null
            : IngCurrency.fromJson(json["trading_currency"]),
        tradingFees: json["trading_fees"],
        askingAmount: json["asking_amount"],
        askingCurrency: json["asking_currency"] == null
            ? null
            : IngCurrency.fromJson(json["asking_currency"]),
        askingFees: json["asking_fees"],
        type: json["type"],
        rate: json["rate"],
        rateValue: json["rate_value"],
        allowsPartialTrade: json["allows_partial_trade"],
        minimumPartialTradeAmount: json["minimum_partial_trade_amount"],
        balance: json["balance"],
        deliveryMethod: json["delivery_method"],
        acceptingTradeText: json["accepting_trade_text"],
        p2pFees: json["p2p_fees"],
        partialAmountPaid: json["partial_amount_paid"],
        status: json["status"],
        expiresAt: json["expires_at"] == null
            ? null
            : DateTime.parse(json["expires_at"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "user": user?.toJson(),
        "trading_amount": tradingAmount,
        "trading_currency": tradingCurrency?.toJson(),
        "trading_fees": tradingFees,
        "asking_amount": askingAmount,
        "asking_currency": askingCurrency?.toJson(),
        "asking_fees": askingFees,
        "type": type,
        "rate": rate,
        "rate_value": rateValue,
        "allows_partial_trade": allowsPartialTrade,
        "minimum_partial_trade_amount": minimumPartialTradeAmount,
        "balance": balance,
        "delivery_method": deliveryMethod,
        "accepting_trade_text": acceptingTradeText,
        "p2p_fees": p2pFees,
        "partial_amount_paid": partialAmountPaid,
        "status": status,
        "expires_at": expiresAt?.toIso8601String(),
        "created_at": createdAt?.toIso8601String(),
      };

  @override
  String toString() {
    return 'Offers(id: $id, user: $user, tradingAmount: $tradingAmount, tradingCurrency: $tradingCurrency, tradingFees: $tradingFees, askingAmount: $askingAmount, askingCurrency: $askingCurrency, askingFees: $askingFees, type: $type, rate: $rate, allowsPartialTrade: $allowsPartialTrade, minimumPartialTradeAmount: $minimumPartialTradeAmount, balance: $balance, partialAmountPaid: $partialAmountPaid, status: $status, expiresAt: $expiresAt, createdAt: $createdAt)';
  }
}

class IngCurrency {
  int? id;
  String? name;
  String? code;
  String? country;
  String? category;
  String? symbol;
  String? flag;

  IngCurrency({
    this.id,
    this.name,
    this.code,
    this.country,
    this.category,
    this.symbol,
    this.flag,
  });

  factory IngCurrency.fromJson(Map<String, dynamic> json) => IngCurrency(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        country: json["country"],
        category: json["category"],
        symbol: json["symbol"],
        flag: json["flag"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "country": country,
        "category": category,
        "symbol": symbol,
        "flag": flag,
      };
}

class User {
  String? userId;
  String? userName;
  String? avatarUrl;
  String? city;
  String? state;
  String? country;
  DateTime? createdAt;
  int? numberOfOffers;
  String? totalVolumeExchanged;

  User({
    this.userId,
    this.userName,
    this.avatarUrl,
    this.city,
    this.state,
    this.country,
    this.createdAt,
    this.numberOfOffers,
    this.totalVolumeExchanged,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        userId: json["user_id"],
        userName: json["user_name"],
        avatarUrl: json["avatar_url"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        numberOfOffers: json["number_of_offers"],
        totalVolumeExchanged: json["total_volume_exchanged"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "user_name": userName,
        "avatar_url": avatarUrl,
        "city": city,
        "state": state,
        "country": country,
        "created_at": createdAt?.toIso8601String(),
        "number_of_offers": numberOfOffers,
        "total_volume_exchanged": totalVolumeExchanged,
      };
}

List<OfferDealActivity> offerDealActivityFromJson(String str) =>
    List<OfferDealActivity>.from(
        json.decode(str).map((x) => OfferDealActivity.fromJson(x)));

String offerDealActivityToJson(List<OfferDealActivity> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OfferDealActivity {
  int? id;
  Offers? offer;
  String? acceptingUserUsername;
  Currency? currency;
  String? amount;
  DateTime? createdAt;

  OfferDealActivity({
    this.id,
    this.offer,
    this.acceptingUserUsername,
    this.currency,
    this.amount,
    this.createdAt,
  });

  factory OfferDealActivity.fromJson(Map<String, dynamic> json) =>
      OfferDealActivity(
        id: json["id"],
        offer: json["offer"] == null ? null : Offers.fromJson(json["offer"]),
        acceptingUserUsername: json["accepting_user_username"],
        currency: json["currency"] == null
            ? null
            : Currency.fromJson(json["currency"]),
        amount: json["amount"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "offer": offer?.toJson(),
        "accepting_user_username": acceptingUserUsername,
        "currency": currency?.toJson(),
        "amount": amount,
        "created_at": createdAt?.toIso8601String(),
      };
}
