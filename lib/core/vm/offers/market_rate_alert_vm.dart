import 'dart:convert';

import 'package:korrency/core/core.dart';

class MarketRateAlertVM extends BaseVM {
  TextEditingController rateC = TextEditingController();
  TextEditingController minC = TextEditingController();
  TextEditingController maxC = TextEditingController();

  List<MarketRateAlert> _rateAlerts = [];
  List<MarketRateAlert> get rateAlerts => _rateAlerts;

  bool get btnEnabled =>
      rateC.text.isNotEmpty && minC.text.isNotEmpty && maxC.text.isNotEmpty;

  Future<ApiResponse> getAlerts() async {
    return await performApiCall(
      url: "/marketplace/alerts",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _rateAlerts = marketRateAlertFromJson(jsonEncode(data["data"]));
        printty(_rateAlerts, level: "Alerts");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> viewAlert(int alertId) async {
    return await performApiCall(
      url: "/marketplace/alerts/$alertId/view",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        // _transactionFee = TransactionFee.fromJson(data["data"]);
        // printty(_transactionFee, logLevel: "Fees");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> createAlert({
    required String offerType,
  }) async {
    var body = {
      "rate": rateC.text.trim(),
      "minimum_amount": minC.text.trim(),
      "maximum_amount": maxC.text.trim(),
      "offer_type": offerType,
    };

    printty(body, level: "Create Alert");
    return await performApiCall(
      url: "/marketplace/alerts/create",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        // _createdOffer = Offers.fromJson(data["data"]);
        return ApiResponse(success: true, message: data['message']);
      },
    );
  }

  Future<ApiResponse> deleteAlert(int alertId) async {
    return await performApiCall(
      url: "/marketplace/alerts/$alertId/delete",
      method: apiService.deleteWithAuth,
      onSuccess: (data) {
        // getBeneficiaries();
        return ApiResponse(success: true, message: data['message']);
      },
    );
  }

  clearData() {
    rateC.clear();
    minC.clear();
    maxC.clear();
  }

  @override
  void dispose() {
    printty('Market Rate Alert VM Disposed');
    rateC.dispose();
    minC.dispose();
    maxC.dispose();

    super.dispose();
  }
}
