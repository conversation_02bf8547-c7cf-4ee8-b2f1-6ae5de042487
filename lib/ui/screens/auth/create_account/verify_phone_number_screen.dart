import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class VerifyPhoneNumberScreen extends StatefulWidget {
  const VerifyPhoneNumberScreen({super.key});

  @override
  State<VerifyPhoneNumberScreen> createState() =>
      _VerifyPhoneNumberScreenState();
}

class _VerifyPhoneNumberScreenState extends State<VerifyPhoneNumberScreen> {
  final FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: 'Verify your Phone Number',
              onBackBtnTap: () {
                Navigator.pop(context);
              },
            ),
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Please enter the 6-digit code sent to ",
                      style: AppTypography.text16
                          .copyWith(color: AppColors.gray93),
                    ),
                    Text(
                      "${vm.countryModel.dialCode} ${AppUtils.formatPhoneNumber(vm.phoneNumC.text)}",
                      style: AppTypography.text16
                          .copyWith(color: AppColors.primaryBlue),
                    ),
                    const YBox(46),
                    Center(
                      child: Pinput(
                        defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                        followingPinTheme: PinInputTheme.changePinTheme(),
                        focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                        submittedPinTheme: PinInputTheme.changePinTheme(),
                        length: 6,
                        controller: vm.phoneOtp,
                        focusNode: pinFocusNode,
                        showCursor: true,
                        onCompleted: (pin) {
                          printty("Completed");
                          _verifyOtp();
                        },
                      ),
                    ),
                    const YBox(24),
                    ResendCode(
                      onResendCode: () async {
                        final res = await vm.requestOtp(otpType: OtpType.phone);
                        handleApiResponse(response: res);
                      },
                    ),
                    const Spacer(),
                    CustomBtn.withChild(
                      onTap: () => _verifyOtp(),
                      online: vm.phoneOtp.text.length == 6,
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      child:
                          ContinueText(isOnline: vm.phoneOtp.text.length == 6),
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _verifyOtp() {
    pinFocusNode.unfocus();
    context.read<OnBoardVM>().verifyOtp().then((value) {
      if (value.success) {
        _moveToEmailAndPasswordScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
        _clearOtp();
      }
    });
  }

  _clearOtp() {
    context.read<OnBoardVM>().phoneOtp.clear();
  }

  _moveToEmailAndPasswordScreen() {
    context.read<OnBoardVM>().phoneOtp.clear();
    Navigator.pushReplacementNamed(context, RoutePath.emailAndPasswordScreen);
  }
}
