import 'package:korrency/core/core.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class AccountLimitCard extends StatelessWidget {
  const AccountLimitCard({
    super.key,
    required this.title,
    required this.percent,
    required this.amountUsed,
    required this.totalAmount,
    this.showBorder = false,
  });

  final String title;
  final double percent; // 0 to 1
  final String amountUsed;
  final String totalAmount;
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(10),
        vertical: Sizer.height(8),
      ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
        border: showBorder
            ? Border.all(
                color: AppColors.blue8FB,
              )
            : null,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                title,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray79,
                ),
              ),
              Spacer(),
              Text(
                totalAmount,
                style: AppTypography.text14.copyWith(
                  color: AppColors.mainBlack,
                ),
              ),
            ],
          ),
          YBox(12),
          LinearPercentIndicator(
            animation: true,
            padding: EdgeInsets.zero,
            animationDuration: 2000,
            lineHeight: Sizer.height(4),
            percent: percent,
            backgroundColor: AppColors.grayFO,
            progressColor: AppColors.primaryBlue,
            animateFromLastPercent: true,
            restartAnimation: false,
          ),
          YBox(12),
          Row(
            children: [
              Text(
                "Used",
                style: AppTypography.text12.copyWith(
                  color: AppColors.mainBlack,
                ),
              ),
              Spacer(),
              Text(
                amountUsed,
                style: AppTypography.text12.copyWith(
                  color: AppColors.mainBlack,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
