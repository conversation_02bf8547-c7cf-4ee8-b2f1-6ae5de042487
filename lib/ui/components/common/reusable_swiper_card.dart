import 'package:flutter/material.dart';

/// A reusable swiper card widget that displays a stack of cards with swipe and tap functionality
class ReusableSwiperCard extends StatefulWidget {
  /// List of widgets to display as cards
  final List<Widget> cards;
  
  /// Initial index of the card to display as primary (front card)
  final int initialIndex;
  
  /// Callback when a card is swiped (left or right)
  /// Parameters: originalIndex, isRightSwipe, cardWidget
  final Function(int originalIndex, bool isRightSwipe, Widget cardWidget)? onSwipe;
  
  /// Callback when a background card is tapped
  /// Parameters: originalIndex, cardWidget
  final Function(int originalIndex, Widget cardWidget)? onTap;
  
  /// Callback when the primary (front) card is tapped
  /// Parameters: originalIndex, cardWidget
  final Function(int originalIndex, Widget cardWidget)? onPrimaryTap;
  
  /// Height of each card
  final double cardHeight;
  
  /// Vertical offset between stacked cards
  final double cardOffset;
  
  /// Animation duration for swipe animations
  final Duration animationDuration;
  
  /// Whether to enable swipe gestures
  final bool enableSwipe;
  
  /// Whether to enable tap gestures on background cards
  final bool enableBackgroundTap;
  
  /// Whether to enable tap gestures on primary card
  final bool enablePrimaryTap;
  
  /// Minimum drag distance to trigger swipe
  final double minDragDistance;
  
  /// Minimum velocity to trigger swipe
  final double minVelocity;

  const ReusableSwiperCard({
    super.key,
    required this.cards,
    this.initialIndex = 0,
    this.onSwipe,
    this.onTap,
    this.onPrimaryTap,
    this.cardHeight = 220,
    this.cardOffset = 40.0,
    this.animationDuration = const Duration(milliseconds: 300),
    this.enableSwipe = true,
    this.enableBackgroundTap = true,
    this.enablePrimaryTap = true,
    this.minDragDistance = 50.0,
    this.minVelocity = 100.0,
  });

  @override
  State<ReusableSwiperCard> createState() => _ReusableSwiperCardState();
}

class _ReusableSwiperCardState extends State<ReusableSwiperCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  List<Widget> _cards = [];
  List<int> _originalIndices = []; // Track original indices
  bool _isAnimating = false;
  double _dragOffset = 0.0;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _initializeAnimations();
    _initializeCards();
  }

  void _initializeAnimations() {
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeCards() {
    if (widget.cards.isEmpty) {
      _cards = [];
      _originalIndices = [];
      return;
    }

    // Ensure initialIndex is within bounds
    final safeInitialIndex = widget.initialIndex.clamp(0, widget.cards.length - 1);
    
    // Initialize cards with the specified initial index as the primary card
    _cards = [];
    _originalIndices = [];
    
    // Add the initial card first (it will be the primary/front card)
    _cards.add(widget.cards[safeInitialIndex]);
    _originalIndices.add(safeInitialIndex);
    
    // Add the rest of the cards in order, skipping the initial card
    for (int i = 0; i < widget.cards.length; i++) {
      if (i != safeInitialIndex) {
        _cards.add(widget.cards[i]);
        _originalIndices.add(i);
      }
    }
  }

  @override
  void didUpdateWidget(ReusableSwiperCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.cards != widget.cards || oldWidget.initialIndex != widget.initialIndex) {
      setState(() {
        // Check if we should preserve the current order or reinitialize
        if (oldWidget.cards == widget.cards && oldWidget.initialIndex != widget.initialIndex) {
          // Only initialIndex changed, reinitialize with new index
          _initializeCards();
        } else if (_originalIndices.isNotEmpty && widget.cards.length == oldWidget.cards.length) {
          // Cards content changed but same length, preserve current order
          List<Widget> newCards = [];
          for (int originalIndex in _originalIndices) {
            if (originalIndex < widget.cards.length) {
              newCards.add(widget.cards[originalIndex]);
            }
          }
          _cards = newCards;
        } else {
          // Number of cards changed or first initialization, use initialIndex
          _initializeCards();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (_isAnimating || !widget.enableSwipe) return;

    setState(() {
      _dragOffset += details.delta.dx;
    });
  }

  void _onSwipe(DragEndDetails details) {
    if (_isAnimating || !widget.enableSwipe) return;

    final velocity = details.primaryVelocity ?? 0;
    final dragDistance = _dragOffset.abs();

    if (velocity.abs() < widget.minVelocity && dragDistance < widget.minDragDistance) {
      setState(() {
        _dragOffset = 0.0;
      });
      return;
    }

    _isAnimating = true;
    final isRightSwipe = velocity > 0 || (velocity == 0 && _dragOffset > 0);
    _animateSwipe(isRightSwipe);

    setState(() {
      _dragOffset = 0.0;
    });
  }

  void _animateSwipe(bool isRightSwipe) {
    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(isRightSwipe ? 1.0 : -1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward().then((_) {
      if (_cards.isNotEmpty && _originalIndices.isNotEmpty) {
        setState(() {
          final currentCard = _cards.removeAt(0);
          final currentOriginalIndex = _originalIndices.removeAt(0);
          _cards.add(currentCard);
          _originalIndices.add(currentOriginalIndex);
        });

        // Call the onSwipe callback
        if (_cards.isNotEmpty && _originalIndices.isNotEmpty) {
          widget.onSwipe?.call(_originalIndices[0], isRightSwipe, _cards[0]);
        }
      }

      _animationController.reset();
      _isAnimating = false;
    });
  }

  void _bringCardToFront(int index) {
    if (_isAnimating || _cards.isEmpty || _originalIndices.isEmpty || 
        index >= _cards.length || index >= _originalIndices.length) return;
    
    setState(() {
      final tappedCard = _cards.removeAt(index);
      final tappedOriginalIndex = _originalIndices.removeAt(index);
      _cards.insert(0, tappedCard);
      _originalIndices.insert(0, tappedOriginalIndex);
    });

    // Call the onTap callback
    widget.onTap?.call(_originalIndices[0], _cards[0]);
  }

  void _onPrimaryCardTap() {
    if (_isAnimating || _cards.isEmpty || _originalIndices.isEmpty) return;
    
    // Call the onPrimaryTap callback
    widget.onPrimaryTap?.call(_originalIndices[0], _cards[0]);
  }

  @override
  Widget build(BuildContext context) {
    if (_cards.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: widget.cardHeight + ((_cards.length - 1) * widget.cardOffset),
      child: Stack(
        children: [
          // Background cards: reverse render from back to front
          for (int i = _cards.length - 1; i >= 1; i--)
            Positioned(
              top: (_cards.length - 1 - i) * widget.cardOffset,
              left: 0,
              right: 0,
              child: widget.enableBackgroundTap
                  ? GestureDetector(
                      onTap: () => _bringCardToFront(i),
                      child: SizedBox(
                        height: widget.cardHeight,
                        child: _cards[i],
                      ),
                    )
                  : SizedBox(
                      height: widget.cardHeight,
                      child: _cards[i],
                    ),
            ),

          // Front card (primary card)
          Positioned(
            top: (_cards.length - 1) * widget.cardOffset,
            left: 0,
            right: 0,
            child: GestureDetector(
              onTap: widget.enablePrimaryTap ? _onPrimaryCardTap : null,
              onPanUpdate: widget.enableSwipe ? _onPanUpdate : null,
              onPanEnd: widget.enableSwipe ? _onSwipe : null,
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  final animationOffset = _slideAnimation.value *
                      MediaQuery.of(context).size.width;
                  final totalOffset = Offset(
                      animationOffset.dx + _dragOffset,
                      animationOffset.dy);

                  return Transform.translate(
                    offset: totalOffset,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: SizedBox(
                        height: widget.cardHeight,
                        child: _cards[0],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}