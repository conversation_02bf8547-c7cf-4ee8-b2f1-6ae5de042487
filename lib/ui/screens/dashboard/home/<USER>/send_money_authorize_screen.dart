// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';
// import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

// class SendMoneyAuthorizeScreen extends StatefulWidget {
//   const SendMoneyAuthorizeScreen({super.key});

//   @override
//   State<SendMoneyAuthorizeScreen> createState() =>
//       _SendMoneyAuthorizeScreenState();
// }

// class _SendMoneyAuthorizeScreenState extends State<SendMoneyAuthorizeScreen> {
//   String? _pin;
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<SendMoneyVM>(builder: (context, vM, _) {
//       return BusyOverlay(
//         show: vM.isBusy,
//         child: Scaffold(
//           backgroundColor: AppColors.bgWhite,
//           body: SafeArea(
//             bottom: false,
//             child: Container(
//               padding: EdgeInsets.symmetric(
//                 horizontal: Sizer.width(24),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const YBox(20),
//                   const ArrowBack(),
//                   const YBox(30),
//                   const AuthTextSubTitle(
//                     title: "Authorize Transaction",
//                     subtitle: "Enter your PIN to authorize this transaction",
//                   ),
//                   const YBox(30),
//                   Expanded(
//                     child: TransactionPIN(
//                       onSubmit: (pin) {},
//                       onChange: (pin) {
//                         _pin = pin;
//                         setState(() {});
//                       },
//                     ),
//                   ),
//                   const YBox(50),
//                   CustomBtn.solid(
//                     onTap: () {
//                       _transfer(_pin ?? '');
//                     },
//                     online: (_pin?.length ?? 0) > 3,
//                     text: "Authorize",
//                   ),
//                   const YBox(40),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       );
//     });
//   }

//   _transfer(String pin) {
//     final sendMoneyVM = context.read<SendMoneyVM>();
//     final transactionVm = context.read<TransactionVM>();
//     final bankVM = context.read<BankVM>();
//     final user = context.read<AuthUserVM>().user;

//     sendMoneyVM
//         .transfer(
//       pin,
//       transactionVm.selectedReason?.id,
//     )
//         .then((value) async {
//       if (value.success) {
//         // Track successful money transfer
//         try {
//           final data = value.data['data'];

//           await UnifiedAnalyticsManager.instance.trackSendMoney(
//             userId: user?.id ?? 'unknown',
//             amount:
//                 double.tryParse(sendMoneyVM.fromC.text.replaceAllCommas()) ??
//                     0.0,
//             fromCurrency:
//                 sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD',
//             toCurrency: sendMoneyVM.recipientCurrency?.code ?? 'NGN',
//             corridor:
//                 '${sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD'}-${sendMoneyVM.recipientCurrency?.code ?? 'NGN'}',
//             transactionId: data?['transaction_id']?.toString(),
//             additionalParameters: {
//               'transfer_method': sendMoneyVM.transferMethod?.name ?? 'unknown',
//               'recipient_name': sendMoneyVM.accountName ?? 'unknown',
//               'transaction_timestamp': DateTime.now().toIso8601String(),
//               'exchange_rate':
//                   sendMoneyVM.conversionRate?.rate?.rate?.toString(),
//               'recipient_amount': sendMoneyVM.recipientGetAmount,
//               'transfer_fee': sendMoneyVM.transferFee,
//             },
//           );
//         } catch (e) {
//           printty('❌ Error tracking send money: $e');
//         }

//         sendMoneyVM.resetData();
//         bankVM.resetData();
//         transactionVm.setSelectedReason(null);
//         _getWalletCredential();
//         final data = value.data['data'];
//         String? securityAnswer;
//         if (data != null && data is Map<String, dynamic>) {
//           securityAnswer = data['security_answer'];
//         }
//         _showSuccessConfirmationScreen(securityAnswer);
//       } else {
//         _showErrorConfirmationScreen(msg: value.message);
//         sendMoneyVM.resetData();
//         bankVM.resetData();
//         transactionVm.setSelectedReason(null);
//       }
//     });
//   }

//   _getWalletCredential() {
//     context.read<BankVM>().resetData();
//     context.read<WalletVM>().getWallets();
//     context.read<AuthUserVM>().getAuthUser();
//     context.read<CurrencyVM>().getCurrencies();
//   }

//   _showErrorConfirmationScreen({String? msg}) {
//     Navigator.pushNamed(
//       context,
//       RoutePath.successConfirmScreen,
//       arguments: SuccessConfirmArg(
//         title: msg ?? "Something went wrong",
//         imgPath: AppGifs.failure,
//         btnText: "Continue",
//         btnTap: () {
//           Navigator.pushNamedAndRemoveUntil(
//             NavigatorKeys.appNavigatorKey.currentContext!,
//             RoutePath.dashboardNav,
//             (route) => false,
//           );

//           Navigator.pushNamed(
//             NavigatorKeys.appNavigatorKey.currentContext!,
//             RoutePath.sendMoneyScreen,
//           );
//         },
//       ),
//     );
//   }

//   _showSuccessConfirmationScreen([String? interacSecAnswer]) {
//     var sendVM = context.read<SendMoneyVM>();
//     Navigator.pushNamed(
//       context,
//       RoutePath.successConfirmScreen,
//       arguments: SuccessConfirmArg(
//         interacSecurityAnswer: interacSecAnswer,
//         showRatingPrompt: true,
//         isInteracTransaction:
//             sendVM.transferMethod == TransferMethodType.interac,
//         title: sendVM.transferMethod == TransferMethodType.korrency
//             ? "Transaction Successful"
//             : "Transaction in Progress",
//         subTitle: sendVM.transferMethod == TransferMethodType.interac
//             ? "Check your phone or email for the Interac security answer to complete your transfer."
//             : "Recipient will receive the money in a few minutes",
//         btnText: "Send more money",
//         secondBtnText: "Home",
//         btnTap: () {
//           Navigator.pushNamedAndRemoveUntil(
//             NavigatorKeys.appNavigatorKey.currentContext!,
//             RoutePath.dashboardNav,
//             (route) => false,
//           );

//           Navigator.pushNamed(
//             NavigatorKeys.appNavigatorKey.currentContext!,
//             RoutePath.sendMoneyScreen,
//           );
//         },
//         secondBtnTap: () {
//           Navigator.pushNamedAndRemoveUntil(
//             NavigatorKeys.appNavigatorKey.currentContext!,
//             RoutePath.dashboardNav,
//             (route) => false,
//           );
//         },
//       ),
//     );
//   }

//   // _pop() {
//   //   Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
//   // }
// }
