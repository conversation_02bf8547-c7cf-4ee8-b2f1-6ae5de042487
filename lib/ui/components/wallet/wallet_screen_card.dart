import 'package:flutter/material.dart';

import '../common/reusable_swiper_card.dart';

// FOR TESTING PURPOSES
class WalletCardsScreen extends StatefulWidget {
  const WalletCardsScreen({super.key});

  @override
  State<WalletCardsScreen> createState() => _WalletCardsScreenState();
}

class _WalletCardsScreenState extends State<WalletCardsScreen> {
  List<Map<String, dynamic>> wallets = [
    {
      'currency': 'CAD',
      'flag': '🇨🇦',
      'balance': '400,228.76',
      'color': const Color(0xFF4A6CF7),
      'isPrimary': true,
      'subtitle': 'Primary Interac',
      'email': 's****<EMAIL>',
    },
    {
      'currency': 'NGN',
      'flag': '🇳🇬',
      'balance': '400,228.76',
      'color': const Color(0xFF2C3E50),
      'isPrimary': false,
    },
    {
      'currency': 'USD',
      'flag': '🇺🇸',
      'balance': '400,228.76',
      'color': const Color(0xFFB8C5E8),
      'isPrimary': false,
    },
    {
      'currency': 'GHS',
      'flag': '🇬🇭',
      'balance': '400,228.76',
      'color': const Color(0xFF6B7FE8),
      'isPrimary': false,
    },
    {
      'currency': 'KES',
      'flag': '🇰🇪',
      'balance': '400,228.76',
      'color': const Color(0xFFB8C5E8),
      'isPrimary': false,
    },
  ];

  void _onSwipe(int originalIndex, bool isRightSwipe, Widget cardWidget) {
    print('Card $originalIndex swiped ${isRightSwipe ? 'right' : 'left'}');
    // Handle swipe logic here
  }

  void _onBackgroundCardTap(int originalIndex, Widget cardWidget) {
    print('Background card $originalIndex tapped');
    // Handle background card tap logic here
  }

  void _onPrimaryCardTap(int originalIndex, Widget cardWidget) {
    print('Primary card $originalIndex tapped');
    // Handle primary card tap logic here
  }

  List<Widget> _buildWalletCards() {
    return wallets.map((wallet) {
      final isPrimary = wallet == wallets.first;
      return _buildWalletCard(
        currency: wallet['currency'],
        flag: wallet['flag'],
        balance: wallet['balance'],
        color: wallet['color'],
        isPrimary: isPrimary,
        subtitle: isPrimary ? wallet['subtitle'] : null,
        email: isPrimary ? wallet['email'] : null,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Row(
                    children: [
                      Text(
                        'Wallets',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(width: 8),
                      Icon(
                        Icons.visibility,
                        color: Colors.grey,
                        size: 20,
                      ),
                    ],
                  ),
                  TextButton.icon(
                    onPressed: () {},
                    icon: const Icon(Icons.add, color: Colors.blue),
                    label: const Text(
                      'Wallet',
                      style: TextStyle(color: Colors.blue),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Wallet Cards Stack using ReusableSwiperCard
              Expanded(
                child: ReusableSwiperCard(
                  cards: _buildWalletCards(),
                  onSwipe: _onSwipe,
                  onTap: _onBackgroundCardTap,
                  onPrimaryTap: _onPrimaryCardTap,
                  cardHeight: 220,
                  cardOffset: 50.0,
                  animationDuration: const Duration(milliseconds: 300),
                  enableSwipe: true,
                  enableBackgroundTap: true,
                  enablePrimaryTap: true,
                  minDragDistance: 50.0,
                  minVelocity: 100.0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWalletCard({
    required String currency,
    required String flag,
    required String balance,
    required Color color,
    bool isPrimary = false,
    String? subtitle,
    String? email,
    double scale = 1.0,
  }) {
    if (isPrimary) {
      return Transform.scale(
        scale: scale,
        child: Container(
          height: 220,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color,
                color.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              Positioned(
                right: -20,
                bottom: -20,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(flag, style: const TextStyle(fontSize: 24)),
                        const SizedBox(width: 12),
                        Text(
                          '$currency Balance',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Text(
                      balance,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (subtitle != null)
                      Text(
                        subtitle,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    if (subtitle != null) const SizedBox(height: 4),
                    if (email != null)
                      Text(
                        email,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Background card
    return Transform.scale(
      scale: scale,
      child: Container(
        height: 220,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color,
              color.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              right: -20,
              bottom: -20,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(flag, style: const TextStyle(fontSize: 24)),
                      const SizedBox(width: 12),
                      Text(
                        '$currency Balance',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Text(
                    balance,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
