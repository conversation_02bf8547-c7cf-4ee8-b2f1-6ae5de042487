import 'package:korrency/core/core.dart';

class InActivityListener extends StatefulWidget {
  final Widget child;
  final Duration timeDuration;
  const InActivityListener(
      {Key? key, required this.child, required this.timeDuration})
      : super(key: key);

  @override
  State<InActivityListener> createState() => _InActivityListenerState();
}

class _InActivityListenerState extends State<InActivityListener>
    with WidgetsBindingObserver {
  late InactivityVM inactivityViewModel;

  final GlobalKey _gestureDetectorKey =
      GlobalKey(debugLabel: 'GestureDetector');

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    inactivityViewModel = Provider.of<InactivityVM>(context, listen: false);
    inactivityViewModel.resetValues();
    inactivityViewModel.setTimeDuration(widget.timeDuration);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive ||
        state == AppLifecycleState.paused ||
        state == AppLifecycleState.detached) {
      printty(state.name.toString(), level: 'inactivity statexxx');
      printty(state.toString(), level: 'inactivity statexxx');
      printty("statexxx......");
      inactivityViewModel.onActivity();
    } else if (state == AppLifecycleState.resumed) {
      printty('active statexxx');
      inactivityViewModel.isActive();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InactivityVM>(
      builder: (context, model, child) {
        printty("Timer _pauseAction = ${model.pauseAction}");
        return Listener(
          key: _gestureDetectorKey,
          behavior: HitTestBehavior.opaque,
          onPointerDown: (_) => model.onActivity(),
          onPointerMove: (_) => model.onActivity(),
          onPointerUp: (_) => model.onActivity(),
          child: widget.child,
        );
      },
    );
  }
}
