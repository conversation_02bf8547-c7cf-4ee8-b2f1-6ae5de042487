import 'dart:convert';
import 'dart:io';

import 'package:korrency/core/core.dart';

class ConfigVM extends BaseVM {
  ConfigData? _configData;
  ConfigData? get configData => _configData;

  String? get referralBonusAmount => _configData?.referralBonusAmount;
  String? get amtForReferral => _configData?.transactionAmountForReferralBonus;
  String? get emailAddress => _configData?.emailAddress;
  String? get interacDepositEmail => _configData?.interacDepositEmail;
  String? get phoneNumber => _configData?.phoneNumber;
  String? get faceboookURL => _configData?.facebookUrl;
  String? get twitterURL => _configData?.twitterUrl;
  String? get instagramURL => _configData?.instagramUrl;
  String? get linkedInURL => _configData?.linkedinUrl;
  String? get playStoreURL => _configData?.playStoreUrl;
  String? get appStoreURL => _configData?.appStoreUrl;
  String? get marketplaceMinimumAmount => _configData?.marketplaceMinimumAmount;
  String? get marketplaceMaximumAmount => _configData?.marketplaceMaximumAmount;
  String? get newCustomersRateMinimumAmount =>
      _configData?.newCustomersRateMinimumAmount;

  String? _myAppCurrentVersion;
  String? get myAppCurrentVersion => _myAppCurrentVersion;

  double get rangeDoubleMIN =>
      double.tryParse(marketplaceMinimumAmount ?? '0') ?? 0.0;
  double get rangeDoubleMAX =>
      double.tryParse(marketplaceMaximumAmount ?? '0') ?? 10000;

  bool minCheck(double value) =>
      value < rangeDoubleMIN || value >= rangeDoubleMAX;
  bool maxCheck(double value) =>
      value > rangeDoubleMAX || value <= rangeDoubleMIN;

  bool get appIsDueForUpdate {
    return AppUtils.compareVersions(
      Platform.isAndroid
          ? _configData?.androidAppVersion ?? "0.0.0"
          : _configData?.iosAppVersion ?? "0.0.0",
      _myAppCurrentVersion ?? "0.0.0",
    );
  }

  setMyAppCurrentVersion(String value) {
    _myAppCurrentVersion = value;
    reBuildUI();
  }

  Future<ApiResponse> getConfigurations() async {
    return await performApiCall(
      url: "/configurations",
      method: apiService.get,
      onSuccess: (data) {
        _configData = configDataFromJson(json.encode(data["data"]));
        return ApiResponse(success: true, data: _configData);
      },
    );
  }
}
