import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CurrencySheet extends StatefulWidget {
  const CurrencySheet({super.key});

  @override
  State<CurrencySheet> createState() => _CurrencySheetState();
}

class _CurrencySheetState extends State<CurrencySheet> {
  @override
  Widget build(BuildContext context) {
    var currencyVm = context.read<CurrencyVM>();
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Container(
        height: Sizer.screenHeight * 0.80,
        margin: EdgeInsets.all(Sizer.radius(12)),
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Currencies",
                  style: AppTypography.text22.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (currencyVm.currencies.isEmpty) {
                  return SizedBox(
                    height: Sizer.height(200),
                    child: Center(
                      child: Text(
                        "No currency available",
                        style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.gray500),
                      ),
                    ),
                  );
                }
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: Sizer.width(24),
                    bottom: Sizer.width(50),
                  ),
                  shrinkWrap: true,
                  itemBuilder: (ctx, i) {
                    var currency = currencyVm.currencies[i];
                    return CurrencyItemSelector(
                      onTap: () {
                        vm.setSelectedCurrency(currency);
                      },
                      isSelected: vm.selectedCurrency == currency,
                      country: CountryModel(
                        code: currency.code,
                        name: currency.name,
                        flag: currency.flag,
                        dialCode: currency.code,
                      ),
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(8),
                  itemCount: currencyVm.currencies.length,
                );
              }),
            ),
            const YBox(10),
            CustomBtn.solid(
              onTap: () {
                vm.getFilteredTransactions(
                  currencyId: vm.selectedCurrency?.id,
                );
                vm.clearDates();
                vm.setStatusFilter(clear: true);
                Navigator.pop(context);
              },
              online: vm.selectedCurrency != null,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              text: "Apply Filter",
            ),
            const YBox(30),
          ],
        ),
      );
    });
  }
}
