import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class TwoFactorAuthScreen extends StatefulWidget {
  const TwoFactorAuthScreen({Key? key}) : super(key: key);

  @override
  State<TwoFactorAuthScreen> createState() => _TwoFactorAuthScreenState();
}

class _TwoFactorAuthScreenState extends State<TwoFactorAuthScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.white,
            body: SafeArea(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ).copyWith(
                  top: Sizer.height(20),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      child: const ArrowBack(),
                    ),
                    const YBox(30),
                    const AuthTextSubTitle(
                      title: "2FA",
                      subtitle: "Choose where you receive your OTP",
                    ),
                    const YBox(24),
                    SecurityTile(
                      title: 'E-mail',
                      subTitle: 'Send OTP to registered email',
                      showTrailing: true,
                      isSelected:
                          vm.user?.twoFactorNotificationPreference == "mail",
                      leadingWidget: const Icon(
                        Iconsax.sms_notification,
                      ),
                      onTap: () {
                        _set2FA(TwoFactorAuthType.mail);
                      },
                    ),
                    const YBox(24),
                    SecurityTile(
                      leadingIcon: AppSvgs.phone2,
                      title: 'Phone Number',
                      subTitle: 'Send OTP to registered phone number',
                      showTrailing: true,
                      isSelected:
                          vm.user?.twoFactorNotificationPreference == "sms",
                      onTap: () {
                        _set2FA(TwoFactorAuthType.sms);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _set2FA(TwoFactorAuthType twoFactorAuthType) {
    context
        .read<AuthUserVM>()
        .set2FA(twoFactorAuthType: twoFactorAuthType)
        .then((value) {
      if (value.success) {
        _showComfirmationScreen(
          msg: value.message,
        );
      } else {
        _showComfirmationScreen(
          msg: value.message,
          isFailed: true,
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? (isFailed ? "Failed to set 2FA" : "2FA set successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
