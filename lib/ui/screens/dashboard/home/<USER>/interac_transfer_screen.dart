import 'package:flutter/gestures.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class InteracTransferScreen extends StatefulWidget {
  const InteracTransferScreen({super.key});

  @override
  State<InteracTransferScreen> createState() => _InteracTransferScreenState();
}

class _InteracTransferScreenState extends State<InteracTransferScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<InteracVM>().getInteracEmails();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Fund your Wallet',
          ),
          body: InteracBody(),
        ),
      );
    });
  }
}

class InteracBody extends StatelessWidget {
  const InteracBody({
    super.key,
    this.padding,
  });

  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    final configVM = context.read<ConfigVM>();
    final authVm = context.read<AuthUserVM>();
    final interacVm = context.read<InteracVM>();
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
      children: [
        YBox(20),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            "Payment method",
            style: AppTypography.text12.copyWith(
              color: AppColors.gray93,
            ),
          ),
        ),
        YBox(4),
        Row(
          children: [
            SizedBox(
              height: Sizer.height(24),
              width: Sizer.width(24),
              child: Image.asset(AppImages.interac),
            ),
            XBox(8),
            Text(
              "Interac",
              style: AppTypography.text16.copyWith(
                color: AppColors.primaryBlue,
              ),
            ),
          ],
        ),
        const YBox(12),
        Container(
          padding: EdgeInsets.all(Sizer.radius(12)),
          decoration: BoxDecoration(
            color: AppColors.blueBFF,
            borderRadius: BorderRadius.circular(Sizer.height(8)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Payment email",
                      style: AppTypography.text12.copyWith(
                        color: AppColors.gray93,
                      ),
                    ),
                    Text(
                      configVM.interacDepositEmail ?? '',
                      style: AppTypography.text16.copyWith(
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ],
                ),
              ),
              CopyBtn(
                text: configVM.interacDepositEmail ?? '',
              )
            ],
          ),
        ),
        const YBox(12),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(8),
            vertical: Sizer.height(12),
          ),
          decoration: BoxDecoration(
            color: AppColors.bgWhite,
            borderRadius: BorderRadius.circular(Sizer.radius(12)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: [
              InteracTimeline(
                textOne:
                    "Copy the email above, open your bank app, and send an Interac e-Transfer to that email",
                step: "1",
              ),
              InteracTimeline(
                textOne:
                    "Make sure to send the Interac e-Transfer from your verified email shown below.",
                step: "2",
              ),
              InteracTimeline(
                textOne:
                    "Your bank account must match your Korrency legal name -",
                textTwo: "${authVm.user?.firstName} ${authVm.user?.lastName}",
                step: "3",
                isEnd: true,
              ),
            ],
          ),
        ),
        const YBox(12),
        Container(
          padding: EdgeInsets.all(Sizer.radius(10)),
          decoration: BoxDecoration(
            color: AppColors.yellowF5,
            border: Border.all(
              color: AppColors.yellowCD,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(Sizer.radius(12)),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgPicture.asset(
                AppSvgs.information,
                height: Sizer.height(16),
                width: Sizer.width(16),
              ),
              XBox(6),
              Expanded(
                child: Text(
                  "Your funds usually arrive in your CAD wallet within seconds",
                  style: AppTypography.text12.copyWith(
                    color: AppColors.gray93,
                  ),
                ),
              )
            ],
          ),
        ),
        const YBox(20),
        Text(
          "Your verified Interac email(s)",
          style: AppTypography.text14.copyWith(
            color: AppColors.gray51,
          ),
        ),
        const YBox(12),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(14),
            vertical: Sizer.height(8),
          ),
          decoration: BoxDecoration(
            // color: padding == EdgeInsets.zero
            //     ? AppColors.white
            //     : AppColors.blueBFF,
            image: DecorationImage(
              fit: BoxFit.cover,
              image: AssetImage(
                AppImages.interacBg,
              ),
            ),
            borderRadius: BorderRadius.circular(Sizer.radius(12)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...List.generate(
                interacVm.interacEmails.length,
                (index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: Sizer.height(4)),
                    child: Text(
                      interacVm.interacEmails[index],
                      style: AppTypography.text12.copyWith(
                        color: AppColors.primaryBlue90,
                      ),
                    ),
                  );
                },
              ),
              YBox(20),
              Align(
                alignment: Alignment.center,
                child: InkWell(
                  onTap: () {
                    if (interacVm.interacEmails.length > 2) {
                      FlushBarToast.fLSnackBar(
                        message:
                            'You\'ve hit the limit on Interac emails you can add. Need help or an update? Contact support!',
                      );
                      return;
                    }
                    Navigator.pushNamed(
                        context, RoutePath.anotherInteracEmailScreen);
                  },
                  child: Container(
                    height: Sizer.height(30),
                    width: Sizer.width(190),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(Sizer.radius(14)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Add Email',
                          style: AppTypography.text12.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        XBox(6),
                        Icon(
                          Icons.add,
                          color: AppColors.white,
                          size: Sizer.radius(16),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              YBox(8),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(
                    Iconsax.info_circle5,
                    color: AppColors.gray93,
                    size: Sizer.radius(12),
                  ),
                  const XBox(4),
                  Text(
                    interacVm.interacEmails.length >= 3
                        ? "You are not allowed to add a new email as you have reached your maximum email limi for this account"
                        : "Max of 3 emails allowed",
                    style: AppTypography.text10.copyWith(
                      color: AppColors.gray93,
                    ),
                  )
                ],
              )
            ],
          ),
        ),
      ],
    );
  }
}

class InteracTimeline extends StatelessWidget {
  const InteracTimeline({
    super.key,
    this.isEnd = false,
    required this.textOne,
    this.textTwo,
    this.textTwoColor,
    this.textItalicThree,
    this.onTapTextBoldTwo,
    required this.step,
  });

  final bool isEnd;
  final String textOne;
  final String? textTwo;
  final VoidCallback? onTapTextBoldTwo;
  final Color? textTwoColor;
  final String? textItalicThree;
  final String step;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: Sizer.width(24),
              height: Sizer.height(24),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
              ),
              child: Center(
                child: Text(
                  step,
                  style: AppTypography.text14.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
            if (!isEnd)
              Container(
                height: Sizer.height(62),
                width: Sizer.width(1),
                color: AppColors.primaryBlue,
              )
          ],
        ),
        const XBox(12),
        Expanded(
            child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: textOne,
                style: AppTypography.text14.copyWith(
                  color: AppColors.gray79,
                  fontWeight: FontWeight.w400,
                  fontFamily: AppFont.outfit.family,
                ),
              ),
              if (textTwo != null)
                TextSpan(
                  text: " $textTwo",
                  style: AppTypography.text14.copyWith(
                    color: textTwoColor ?? AppColors.blueD8,
                    fontFamily: AppFont.outfit.family,
                  ),
                  recognizer: TapGestureRecognizer()..onTap = onTapTextBoldTwo,
                ),
              if (textItalicThree != null)
                TextSpan(
                  text: " $textItalicThree",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w400,
                    fontFamily: AppFont.outfit.family,
                    fontStyle: FontStyle.italic,
                    color: AppColors.gray79,
                  ),
                ),
            ],
          ),
        )),
      ],
    );
  }
}
