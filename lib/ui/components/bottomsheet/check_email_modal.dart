// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class CheckEmailModal extends StatelessWidget {
//   const CheckEmailModal({super.key, required this.email});

//   final String email;

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.all(Sizer.radius(12)),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               InkWell(
//                 onTap: () {
//                   Navigator.pop(context);
//                 },
//                 child: Container(
//                   padding: EdgeInsets.all(Sizer.width(10)),
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(12),
//                     color: AppColors.white.withOpacity(0.15),
//                     border: Border.all(
//                       color: AppColors.white.withOpacity(0.70),
//                       width: 1,
//                     ),
//                   ),
//                   child: Icon(
//                     Icons.close,
//                     size: Sizer.width(16),
//                     color: AppColors.white,
//                   ),
//                 ),
//               ),
//             ],
//           ),
//           const YBox(12),
//           Container(
//             padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
//             decoration: BoxDecoration(
//               color: AppColors.bgWhite,
//               borderRadius: BorderRadius.circular(12),
//             ),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 const YBox(28),
//                 SvgPicture.asset(
//                   AppSvgs.confirmEmail,
//                   height: Sizer.height(160),
//                 ),
//                 const YBox(36),
//                 Text(
//                   'Check your Email',
//                   style: FontTypography.text22.semiBold,
//                 ),
//                 YBox(4),
//                 RichText(
//                   textAlign: TextAlign.center,
//                   text: TextSpan(
//                     children: [
//                       TextSpan(
//                         text:
//                             "We've sent you an email with instructions to reset password to \n",
//                         style: AppTypography.text16.copyWith(
//                           fontFamily: AppFont.outfit.family,
//                           color: AppColors.gray93,
//                           height: 1.5,
//                         ),
//                       ),
//                       TextSpan(
//                         text: email,
//                         style: AppTypography.text16.copyWith(
//                           fontFamily: AppFont.outfit.family,
//                           color: AppColors.primaryBlue,
//                           height: 1.5,
//                         ),
//                       ),
//                       TextSpan(
//                         text:
//                             " \nPlease follow the steps to complete the process",
//                         style: AppTypography.text16.copyWith(
//                           fontFamily: AppFont.outfit.family,
//                           color: AppColors.gray93,
//                           height: 1.5,
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//                 const YBox(37),
//                 CustomBtn.solid(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   online: true,
//                   isOutline: true,
//                   textColor: AppColors.primaryBlue,
//                   borderRadius: BorderRadius.circular(Sizer.radius(14)),
//                   textStyle: FontTypography.text15.medium
//                       .withCustomColor(AppColors.primaryBlue),
//                   text: "Continue to Otp",
//                 ),
//                 const YBox(16),
//                 CustomBtn.solid(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   online: true,
//                   borderRadius: BorderRadius.circular(Sizer.radius(14)),
//                   text: "Resend",
//                 ),
//                 const YBox(30),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
