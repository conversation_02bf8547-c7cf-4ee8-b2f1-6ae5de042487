// import 'package:korrency/core/core.dart';

// class CurrencyPrefix extends StatelessWidget {
//   const CurrencyPrefix({
//     super.key,
//     required this.countryFlag,
//     required this.countryDialCode,
//     this.showArrow = true,
//     this.countryCodeStyle,
//   });

//   final String countryFlag;
//   final String countryDialCode;
//   final bool showArrow;
//   final TextStyle? countryCodeStyle;

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         imageHelper(
//           countryFlag,
//           height: Sizer.height(20),
//           width: Sizer.width(20),
//         ),
//         const XBox(4),
//         if (showArrow)
//           const Icon(
//             Icons.expand_more,
//             color: AppColors.gray500,
//             size: 30,
//           ),
//         Text(
//           countryDialCode,
//           style: countryCodeStyle ??
//               AppTypography.text16.copyWith(
//                 color: AppColors.gray500,
//               ),
//         ),
//       ],
//     );
//   }
// }
