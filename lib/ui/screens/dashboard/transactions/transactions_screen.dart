import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    // Mixpanel.track("Transactions Screen");
    MixpanelService().trackScreen("Transaction History Viewed");
    var transVM = context.read<TransactionVM>();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        if (!transVM.gettingMore &&
            transVM.hasMore &&
            !transVM.isBusy &&
            !transVM.isQuerying) {
          printty("Getting more transactions");
          transVM.getPaginatedTransactions();
        }
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.read<TransactionVM>().recentTransactions.isEmpty) {
        _init();
      }
    });
    super.initState();
  }

  _init() {
    context.read<TransactionVM>()
      ..getTransactions()
      ..setStatusFilter(clear: true)
      ..clearDates()
      ..setSelectedCurrency(null);
  }

  bool _isExpanded = false;

  void _toggleExpand() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.only(
              top: Sizer.height(20),
            ),
            height: Sizer.screenHeight,
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Transactions",
                        style: AppTypography.text20.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          _toggleExpand();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(16),
                            vertical: Sizer.height(10),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.blueFD,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Iconsax.search_normal_1,
                                size: Sizer.radius(18),
                              ),
                              XBox(6),
                              Text(
                                "Search",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                AnimatedSize(
                  duration: const Duration(milliseconds: 300),
                  child: _isExpanded
                      ? Container(
                          padding: EdgeInsets.only(
                            left: Sizer.width(24),
                            right: Sizer.width(24),
                            top: Sizer.width(20),
                          ),
                          child: CustomTextField(
                            hintText: "Search transactions",
                            borderRadius: Sizer.height(12),
                            onChanged: (v) => vm.searchTransaction(v),
                          ),
                        )
                      : const SizedBox
                          .shrink(), // Use an empty Container when collapsed
                ),
                const YBox(26),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TransactionTab(
                          text: "Currencies",
                          isActive: true,
                          onTabChange: () {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const CurrencySheet(),
                            );
                          },
                        ),
                      ),
                      const XBox(10),
                      Expanded(
                        child: TransactionTab(
                          text: "Date Range",
                          onTabChange: () {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const AllTimeSheet(),
                            );
                          },
                        ),
                      ),
                      const XBox(10),
                      Expanded(
                        child: TransactionTab(
                          text: "Status",
                          onTabChange: () {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const ApplyStatusSheet(),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(10),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async {
                      _init();
                    },
                    child: Builder(builder: (context) {
                      if (vm.busy(TransactionState.all)) {
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                            // vertical: Sizer.height(30),
                          ),
                          child: const TransactionShimmer(),
                        );
                      }

                      return ListView(
                        shrinkWrap: true,
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: EdgeInsets.only(
                          // top: Sizer.height(20),
                          bottom: Sizer.height(50),
                          left: Sizer.width(24),
                          right: Sizer.width(24),
                        ),
                        children: [
                          if (vm.groupedTransactions.isEmpty && !vm.isBusy)
                            SizedBox(
                              height: Sizer.height(500),
                              child: Center(
                                child: EmptyState(
                                  title: "No transaction found",
                                ),
                              ),
                            ),
                          ...vm.groupedTransactions.entries.map((e) {
                            return TransactionListWidget(
                              title: e.key,
                              transactionList: e.value,
                            );
                          }),
                          if (vm.gettingMore)
                            const TransactionShimmer(itemCount: 2),
                          const YBox(150)
                        ],
                      );
                    }),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}

class TransactionShimmer extends StatelessWidget {
  const TransactionShimmer({
    super.key,
    this.itemCount,
  });

  final int? itemCount;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(
          // horizontal: Sizer.width(24),
          vertical: Sizer.height(30),
        ),
        itemBuilder: (ctx, i) {
          return TransactionCard(
            type: 'credit',
            category: 'Food',
            isCredit: true,
            title: 'Lunch',
            subTitle: 'Jan 25, 2024 | 14:34',
            amount: '₦ 2,000',
            onTap: () {},
          );
        },
        separatorBuilder: (ctx, _) => const YBox(20),
        itemCount: itemCount ?? 10,
      ),
    );
  }
}
