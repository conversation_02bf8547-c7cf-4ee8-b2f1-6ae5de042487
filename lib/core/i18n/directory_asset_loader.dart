import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:korrency/core/core.dart';

/// Enhanced DirectoryAssetLoader with caching, error handling, and performance optimizations
class DirectoryAssetLoader extends AssetLoader {
  const DirectoryAssetLoader();

  /// Cache for loaded translations to improve performance
  static final Map<String, Map<String, dynamic>> _cache = {};

  /// Cache for asset manifest to avoid repeated loading
  static Map<String, dynamic>? _assetManifest;

  @override
  Future<Map<String, dynamic>> load(String path, Locale locale) async {
    final cacheKey = '${locale.languageCode}_$path';

    // Return cached data if available
    if (_cache.containsKey(cacheKey)) {
      if (kDebugMode) {
        printty('📦 Loading translations from cache for ${locale.languageCode}',
            level: 'i18n');
      }
      return _cache[cacheKey]!;
    }

    try {
      if (kDebugMode) {
        printty('🌐 Loading translations for ${locale.languageCode}',
            level: 'i18n');
      }

      // Load asset manifest (cache it for performance)
      _assetManifest ??= await _loadAssetManifest();

      final prefix = '$path/${locale.languageCode}/';
      final files = _assetManifest!.keys
          .where((k) => k.startsWith(prefix) && k.endsWith('.json'))
          .toList();

      if (files.isEmpty) {
        printty(
            '⚠️ No translation files found for ${locale.languageCode} at $path',
            level: 'warning');
        return {};
      }

      final Map<String, dynamic> data = {};

      // Load all translation files concurrently for better performance
      final futures = files.map((file) => _loadTranslationFile(file));
      final results = await Future.wait(futures);

      // Merge all translation data
      for (final result in results) {
        if (result != null) {
          data.addAll(result);
        }
      }

      // Cache the loaded data
      _cache[cacheKey] = data;

      if (kDebugMode) {
        printty(
            '✅ Loaded ${files.length} translation files for ${locale.languageCode} (${data.length} keys)',
            level: 'i18n');
      }

      return data;
    } catch (e, stackTrace) {
      printty('❌ Failed to load translations for ${locale.languageCode}: $e',
          level: 'error');
      if (kDebugMode) {
        printty('Stack trace: $stackTrace', level: 'error');
      }

      // Return empty map instead of throwing to prevent app crashes
      return {};
    }
  }

  /// Load asset manifest with error handling
  Future<Map<String, dynamic>> _loadAssetManifest() async {
    try {
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      return json.decode(manifestContent) as Map<String, dynamic>;
    } catch (e) {
      printty('❌ Failed to load AssetManifest.json: $e', level: 'error');
      rethrow;
    }
  }

  /// Load individual translation file with error handling
  Future<Map<String, dynamic>?> _loadTranslationFile(String filePath) async {
    try {
      final content = await rootBundle.loadString(filePath);
      final Map<String, dynamic> map = json.decode(content);

      if (kDebugMode) {
        printty('📄 Loaded translation file: $filePath (${map.length} keys)',
            level: 'i18n');
      }

      return map;
    } catch (e) {
      printty('❌ Failed to load translation file $filePath: $e',
          level: 'error');
      // Return null instead of throwing to allow other files to load
      return null;
    }
  }

  /// Clear the translation cache (useful for testing or memory management)
  static void clearCache() {
    _cache.clear();
    _assetManifest = null;
    if (kDebugMode) {
      printty('🗑️ Translation cache cleared', level: 'i18n');
    }
  }

  /// Get cache statistics for debugging
  static Map<String, dynamic> getCacheStats() {
    return {
      'cached_locales': _cache.keys.toList(),
      'cache_size': _cache.length,
      'manifest_loaded': _assetManifest != null,
    };
  }

  /// Preload translations for better performance
  static Future<void> preloadTranslations(
      String path, List<Locale> locales) async {
    final loader = DirectoryAssetLoader();

    if (kDebugMode) {
      printty('🚀 Preloading translations for ${locales.length} locales',
          level: 'i18n');
    }

    final futures = locales.map((locale) => loader.load(path, locale));
    await Future.wait(futures);

    if (kDebugMode) {
      printty('✅ Preloading completed', level: 'i18n');
    }
  }
}
