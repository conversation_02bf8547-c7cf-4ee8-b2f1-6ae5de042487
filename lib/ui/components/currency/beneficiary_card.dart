import 'package:korrency/core/core.dart';

class BeneficiaryCard extends StatelessWidget {
  const BeneficiaryCard({
    super.key,
    required this.title,
    required this.imgPath,
    this.onTap,
    this.isSelected = false,
  });

  final String title;
  final String imgPath;
  final VoidCallback? onTap;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Opacity(
        opacity: isSelected ? 1 : 0.5,
        child: SizedBox(
          width: Sizer.width(58),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                  height: Sizer.height(40),
                  width: Sizer.width(40),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: AppColors.lightTextGray,
                  ),
                  // child: const Icon(Iconsax.user)
                  child: Image.network(imgPath)),
              const YBox(12),
              Text(
                title,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: AppTypography.text14.copyWith(
                  height: 1.2,
                  color: AppColors.textBlack100,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
