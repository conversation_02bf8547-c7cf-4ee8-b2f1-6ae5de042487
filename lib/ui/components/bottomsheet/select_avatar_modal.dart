import 'package:korrency/core/core.dart';

class SelectAvatarModal extends StatefulWidget {
  const SelectAvatarModal({super.key});

  @override
  State<SelectAvatarModal> createState() => _SelectAvatarModalState();
}

class _SelectAvatarModalState extends State<SelectAvatarModal> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthUserVM>().getAvatars();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authVm = context.watch<AuthUserVM>();
    return Container(
      height: Sizer.screenHeight * 0.84,
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
      margin: EdgeInsets.all(Sizer.radius(12)),
      decoration: BoxDecoration(
        color: AppColors.bgWhite,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          YB<PERSON>(20),
          Text(
            "Choose an Avatar",
            style: AppTypography.text18.copyWith(
              color: AppColors.mainBlack,
            ),
          ),
          YBox(10),
          Expanded(
            child: Builder(builder: (context) {
              if (authVm.isBusy) {
                return GridView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(
                    top: 26,
                    bottom: 100,
                  ),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 20,
                    mainAxisSpacing: 20,
                    childAspectRatio: 1,
                  ),
                  itemCount: authVm.avatars.length,
                  itemBuilder: (context, i) {
                    return Skeletonizer(
                        child: Bone(
                      height: Sizer.height(100),
                      width: Sizer.width(100),
                      borderRadius: BorderRadius.circular(100),
                    ));
                  },
                );
              }
              if (authVm.avatars.isEmpty && !authVm.isBusy) {
                return SizedBox(
                  height: Sizer.height(500),
                  child: const Center(
                    child: Text("No avatars found",
                        style: TextStyle(color: Colors.grey)),
                  ),
                );
              }
              return GridView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                  top: 26,
                  bottom: 100,
                ),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 20,
                  mainAxisSpacing: 20,
                  childAspectRatio: 1,
                ),
                itemCount: authVm.avatars.length,
                itemBuilder: (context, i) {
                  return InkWell(
                    onTap: () async {
                      Navigator.pop(context, authVm.avatars[i]);

                      // Navigator.pushNamed(
                      //   context,
                      //   RoutePath.previewAvatarScreen,
                      //   arguments: authVm.avatars[i],
                      // );
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: Sizer.height(76),
                      width: Sizer.width(76),
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: AppColors.blu000,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: cacheNetWorkImage(
                        authVm.avatars[i],
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
