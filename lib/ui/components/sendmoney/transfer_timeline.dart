import 'package:korrency/core/core.dart';

class TransferTimeLine extends StatelessWidget {
  const TransferTimeLine({
    super.key,
    this.isEnd = false,
    required this.title,
    required this.textOne,
    this.dotIocn,
    this.timelineHeight,
    this.boldWords,
    this.showTextContainerBg = false,
  });

  final bool isEnd;
  final String textOne;
  final String title;
  final String? dotIocn;
  final double? timelineHeight;
  final List<String>? boldWords;
  final bool showTextContainerBg;

  List<TextSpan> _buildTextSpans(String text) {
    final List<TextSpan> spans = [];

    // If no bold words specified, return original text
    if (boldWords == null || boldWords!.isEmpty) {
      spans.add(TextSpan(
        text: text,
        style: AppTypography.text12.copyWith(
          color: AppColors.gray79,
          fontWeight: FontWeight.w400,
          fontFamily: AppFont.outfit.family,
        ),
      ));
      return spans;
    }

    // Create regex pattern for all bold words
    final String pattern =
        boldWords!.map((word) => '\\b${RegExp.escape(word)}\\b').join('|');
    final RegExp boldRegex = RegExp(pattern, caseSensitive: false);

    int lastMatchEnd = 0;
    for (final match in boldRegex.allMatches(text)) {
      // Add text before bold word
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(
          text: text.substring(lastMatchEnd, match.start),
          style: AppTypography.text12.copyWith(
            color: AppColors.gray79,
            fontWeight: FontWeight.w400,
            fontFamily: AppFont.outfit.family,
          ),
        ));
      }

      // Add bold word with bold formatting
      spans.add(TextSpan(
        text: match.group(0),
        style: AppTypography.text12.copyWith(
          color: AppColors.gray79,
          fontWeight: FontWeight.bold,
          fontFamily: AppFont.outfit.family,
        ),
      ));

      lastMatchEnd = match.end;
    }

    // Add remaining text after last bold word
    if (lastMatchEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastMatchEnd),
        style: AppTypography.text12.copyWith(
          color: AppColors.gray79,
          fontWeight: FontWeight.w400,
          fontFamily: AppFont.outfit.family,
        ),
      ));
    }

    // If no bold words found, return the original text
    if (spans.isEmpty) {
      spans.add(TextSpan(
        text: text,
        style: AppTypography.text12.copyWith(
          color: AppColors.gray79,
          fontWeight: FontWeight.w400,
          fontFamily: AppFont.outfit.family,
        ),
      ));
    }

    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            dotIocn != null
                ? SvgPicture.asset(
                    dotIocn!,
                    height: Sizer.height(24),
                  )
                : SvgPicture.asset(
                    isEnd ? AppSvgs.dotOutline : AppSvgs.dot,
                    height: Sizer.height(24),
                  ),
            if (!isEnd)
              Container(
                height: Sizer.height(timelineHeight ?? 50),
                width: Sizer.width(1),
                color: AppColors.primaryBlue,
              )
          ],
        ),
        const XBox(12),
        Expanded(
            child: Container(
          padding: showTextContainerBg
              ? EdgeInsets.all(Sizer.radius(6))
              : EdgeInsets.zero,
          decoration: showTextContainerBg
              ? BoxDecoration(
                  color: AppColors.yellowF5,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.yellow87,
                  ),
                )
              : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text14.copyWith(
                  color: AppColors.mainBlack,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const YBox(4),
              RichText(
                text: TextSpan(
                  children: _buildTextSpans(textOne),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
}
