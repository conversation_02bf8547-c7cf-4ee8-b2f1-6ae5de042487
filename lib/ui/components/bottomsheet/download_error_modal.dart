import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DownloadErrorModal extends StatefulWidget {
  const DownloadErrorModal({super.key});

  @override
  State<DownloadErrorModal> createState() => _DownloadErrorModalState();
}

class _DownloadErrorModalState extends State<DownloadErrorModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(44)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Y<PERSON><PERSON>(60),
            svg<PERSON>elper(
              AppSvgs.tickOff,
              height: Sizer.height(140),
            ),
            YBox(30),
            Text(
              'Download Failed!',
              textAlign: TextAlign.center,
              style: FontTypography.text18.semiBold,
            ),
            YBox(4),
            Text(
              'We were unable to download your receipt, \nplease try again',
              textAlign: TextAlign.center,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray93,
              ),
            ),
            const YBox(36),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(20),
              onTap: () {},
              text: "Try Again",
            ),
            const YBox(30),
          ],
        ),
      ),
    );
  }
}
