import 'dart:convert';

import 'package:korrency/core/core.dart';

class NotificationVM extends BaseVM {
  List<UserNotification> _allNotifications = [];
  List<UserNotification> get allNotifications => _allNotifications;

  bool _gettingMore = false;
  bool get gettingMore => _gettingMore;
  bool _hasMore = true;
  bool get hasMore => _hasMore;
  int _currentPage = 1;
  int get currentPage => _currentPage;

  setGettingMore(bool value) {
    _gettingMore = value;
    reBuildUI();
  }

  int get totalUnreadNotifications =>
      _allNotifications.where((e) => e.readAt == null).length;

  Future<ApiResponse> getNotifications() async {
    setGettingMore(false);
    _currentPage = 1;
    return await performApiCall(
      url: "/notifications?page=1&limit=15",
      method: apiService.getWithAuth,
      busyObjectName: LoadState.all,
      onSuccess: (data) {
        var response = userNotificationFromJson(
            json.encode(apiResponse.data["datatable"]["data"]));
        _hasMore = response.length == 15;

        _allNotifications = response;
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getPaginatedNotifications() async {
    _currentPage++;

    setGettingMore(true);
    return await performApiCall(
      url: "/notifications?page=$_currentPage&limit=15",
      method: apiService.getWithAuth,
      busyObjectName: LoadState.paginated,
      onSuccess: (data) {
        var response = userNotificationFromJson(
            json.encode(apiResponse.data["datatable"]["data"]));
        _hasMore = response.length == 15;

        _allNotifications.addAll(response);
        setGettingMore(false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> viewNotifications(int id) async {
    return await performApiCall(
      url: "/notifications/$id/view",
      method: apiService.getWithAuth,
      busyObjectName: LoadState.one,
      onSuccess: (data) {
        // var notification = UserNotification.fromJson(data["data"]);
        getNotifications();
        return apiResponse;
      },
    );
  }

  @override
  void dispose() {
    printty('NotificationVM disposed');
    super.dispose();
  }
}
