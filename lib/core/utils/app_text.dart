import 'package:korrency/core/i18n/translation_helper.dart';

class AppText {
  static const String appName = "Korrency";

  /// Localized error messages using TranslationHelper
  static String get timeoutErrMsg => TranslationHelper.tr(
        'errors.timeout',
        fallback:
            "Connection time out. Check your internet connection.\nContact support if error persists",
      );

  static String get errorMsg => TranslationHelper.tr(
        'errors.general',
        fallback:
            "An error occurred. Please try again.\nContact support if error persists",
      );

  /// Additional localized error messages
  static String get networkErrorMsg => TranslationHelper.tr(
        'errors.network',
        fallback: "Network error occurred. Please check your connection",
      );

  static String get serverErrorMsg => TranslationHelper.tr(
        'errors.server',
        fallback: "Server error. Please try again later",
      );

  static String get validationFailedMsg => TranslationHelper.tr(
        'errors.validation_failed',
        fallback: "Validation failed",
      );

  /// KYC related messages
  static String kycIncompleteMsg(String action) => TranslationHelper.tr(
        'errors.kyc.incomplete',
        namedArgs: {'action': action},
        fallback: "Complete your KYC to $action",
      );

  static String get kycPendingMsg => TranslationHelper.tr(
        'errors.kyc.pending',
        fallback: "Your KYC is pending review",
      );

  /// Transaction related messages
  static String get insufficientFundsMsg => TranslationHelper.tr(
        'errors.transaction.insufficient_funds',
        fallback: "Insufficient funds in your wallet",
      );

  static String get transactionFailedMsg => TranslationHelper.tr(
        'errors.transaction.failed',
        fallback: "Transaction failed. Please try again",
      );
}
