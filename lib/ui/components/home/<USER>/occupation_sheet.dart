// import 'package:iconsax/iconsax.dart';
// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class OccupationSheet extends StatefulWidget {
//   const OccupationSheet({
//     Key? key,
//     this.fromDashboard = false,
//   }) : super(key: key);

//   final bool fromDashboard;

//   @override
//   State<OccupationSheet> createState() => _OccupationSheetState();
// }

// class _OccupationSheetState extends State<OccupationSheet> {
//   OccupationData? selectedOccupation;
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       context.read<KycVM>().getOccupationData();
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Consumer<KycVM>(builder: (context, vm, _) {
//       return Container(
//         padding: EdgeInsets.only(
//           bottom: MediaQuery.of(context).viewInsets.bottom,
//         ),
//         margin: EdgeInsets.only(
//           top: Sizer.height(80),
//         ),
//         child: Stack(
//           children: [
//             ContainerWithTopBorderRadius(
//               height: Sizer.screenHeight * (widget.fromDashboard ? 0.85 : 0.66),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const YBox(20),
//                   Text(
//                     'Select Your Occupation',
//                     style: AppTypography.text20.copyWith(
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                   const YBox(4),
//                   Text(
//                     'What field do you work in?',
//                     style: AppTypography.text16.copyWith(
//                       color: AppColors.textBlack800,
//                     ),
//                   ),
//                   const YBox(30),
//                   CustomTextField(
//                     borderRadius: Sizer.height(4),
//                     suffixIcon: Icon(
//                       Iconsax.search_normal_1,
//                       color: AppColors.black600,
//                       size: Sizer.radius(20),
//                     ),
//                     hintText: 'Search for occupation',
//                     onChanged: (val) {
//                       vm.searchoccupation(val.trim());
//                     },
//                   ),
//                   Expanded(
//                     child: ListView(
//                       shrinkWrap: true,
//                       padding: EdgeInsets.only(
//                         top: Sizer.height(10),
//                       ),
//                       children: [
//                         const YBox(24),
//                         Builder(builder: (context) {
//                           if (vm.isBusy) {
//                             return SizedBox(
//                               height: Sizer.height(400),
//                               child: const Center(
//                                 child: CircularProgressIndicator(),
//                               ),
//                             );
//                           }
//                           if (vm.occupationData.isEmpty) {
//                             return SizedBox(
//                               height: Sizer.height(400),
//                               child: Center(
//                                 child: Column(
//                                   mainAxisSize: MainAxisSize.min,
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     Icon(
//                                       Iconsax.box,
//                                       size: Sizer.radius(40),
//                                     ),
//                                     const YBox(10),
//                                     Text(
//                                       "No Occupation Found",
//                                       style: AppTypography.text16.copyWith(
//                                         color: AppColors.textBlack600,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             );
//                           }
//                           return ListView.separated(
//                             shrinkWrap: true,
//                             physics: const NeverScrollableScrollPhysics(),
//                             itemBuilder: (ctx, i) {
//                               final occupation = vm.occupationData[i];
//                               return InkWell(
//                                 onTap: () {
//                                   if (widget.fromDashboard) {
//                                     selectedOccupation = occupation;
//                                     setState(() {});
//                                   } else {
//                                     vm.setSelectedOccupation(occupation);
//                                     Navigator.pop(context);
//                                   }
//                                 },
//                                 child: ContainerWithBluewishBg(
//                                   padding: EdgeInsets.symmetric(
//                                     vertical: Sizer.height(12),
//                                     horizontal: Sizer.width(16),
//                                   ),
//                                   bgColor: AppColors.blueFA,
//                                   child: WalletListTile(
//                                     title: occupation.name ?? "",
//                                     titleFontSize: 14,
//                                     trailingIconSize: 16,
//                                     showTrailing: true,
//                                     trailingWidget: (vm.selectedOccupation ==
//                                                 occupation) ||
//                                             (selectedOccupation == occupation)
//                                         ? const Icon(
//                                             Icons.check_circle,
//                                             size: 26,
//                                             color: AppColors.baseGreen,
//                                           )
//                                         : Container(
//                                             width: Sizer.width(20),
//                                             height: Sizer.height(20),
//                                             decoration: BoxDecoration(
//                                               // color: AppColors.white,
//                                               borderRadius:
//                                                   BorderRadius.circular(30),
//                                               border: Border.all(
//                                                 color: AppColors.grayBF,
//                                                 width: 1,
//                                               ),
//                                             ),
//                                           ),
//                                   ),
//                                 ),
//                               );
//                             },
//                             separatorBuilder: (ctx, _) => const YBox(16),
//                             itemCount: vm.occupationData.length,
//                           );
//                         }),
//                         const YBox(100),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             if (widget.fromDashboard)
//               Positioned(
//                 bottom: 0,
//                 child: Consumer<KycVM>(builder: (ctx, kycVm, _) {
//                   return Container(
//                     width: Sizer.screenWidth,
//                     color: AppColors.white,
//                     padding: EdgeInsets.only(
//                       left: Sizer.width(16),
//                       right: Sizer.width(16),
//                       top: Sizer.height(16),
//                       bottom: Sizer.height(40),
//                     ),
//                     child: CustomBtn.solid(
//                       online: selectedOccupation != null,
//                       isLoading: kycVm.busy(occupationUpdate),
//                       onTap: () {
//                         kycVm
//                             .updateUserOccupation(selectedOccupation?.id ?? 0)
//                             .then((value) {
//                           if (value.success) {
//                             context.read<AuthUserVM>().getAuthUser();
//                             Navigator.pop(context);
//                             FlushBarToast.fLSnackBar(
//                               snackBarType: SnackBarType.success,
//                               message: value.message.toString(),
//                             );
//                           } else {
//                             FlushBarToast.fLSnackBar(
//                               message: value.message.toString(),
//                             );
//                           }
//                         });
//                       },
//                       text: "Continue",
//                     ),
//                   );
//                 }),
//               ),
//           ],
//         ),
//       );
//     });
//   }
// }
