import 'package:korrency/core/core.dart';

class ContinueText extends StatelessWidget {
  const ContinueText({super.key, this.isOnline = false});

  final bool isOnline;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Continue',
          style: AppTypography.text15.copyWith(
            color: isOnline ? AppColors.white : AppColors.gray51,
            fontWeight: FontWeight.w500,
          ),
        ),
        const XBox(8),
        SvgPicture.asset(
          AppSvgs.arrowRight,
          colorFilter: ColorFilter.mode(
            isOnline ? AppColors.white : AppColors.gray51,
            BlendMode.srcIn,
          ),
        )
      ],
    );
  }
}
