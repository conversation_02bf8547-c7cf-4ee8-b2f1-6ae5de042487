import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class VirtualAccountConfirmation extends StatefulWidget {
  const VirtualAccountConfirmation({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  State<VirtualAccountConfirmation> createState() =>
      _VirtualAccountConfirmationState();
}

class _VirtualAccountConfirmationState
    extends State<VirtualAccountConfirmation> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ContainerWithTopBorderRadius(
          height: Sizer.screenHeight * 0.4,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(20),
              AuthTextSubTitle(
                title: "Create Wallet",
                subtitle:
                    "Clicking on continue creates a ${widget.currency.name} wallet",
              ),
              const YBox(30),
              // CustomTextField(
              //   labelText: "Enter BVN",
              //   focusNode: bvnFocusNode,
              //   showLabelHeader: true,
              //   controller: vm.bvnC,
              //   borderRadius: Sizer.height(4),
              //   hintText: '***********',
              //   onChanged: (val) => vm.notify(),
              // ),
              const Spacer(),
              CustomBtn.solid(
                onTap: () {
                  vm
                      .createVirtualAccount(
                    currencyId: widget.currency.id!,
                  )
                      .then((value) {
                    if (value.success) {
                      Navigator.pop(
                          NavigatorKeys.appNavigatorKey.currentContext ??
                              context);
                      FlushBarToast.fLSnackBar(
                        message: value.message.toString(),
                        snackBarType: SnackBarType.success,
                      );
                      _getWalletCredential();
                    } else {
                      FlushBarToast.fLSnackBar(
                        message: value.message.toString(),
                      );
                    }
                  });
                },
                isLoading: vm.isBusy,
                online: true,
                text: "Continue",
              ),
              const YBox(50),
            ],
          ),
        ),
      );
    });
  }

  _getWalletCredential() {
    context.read<WalletVM>().getWallets();
    context.read<AuthUserVM>().getAuthUser();
    context.read<CurrencyVM>().getCurrencies();
    context.read<TransactionVM>().getTransactions();
  }
}
