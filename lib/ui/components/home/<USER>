import 'package:korrency/core/core.dart';

class NotificationWidget extends StatelessWidget {
  const NotificationWidget({super.key, this.onTap, this.count = '0'});

  final VoidCallback? onTap;
  final String count;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        // color: AppColors.red,
        width: Sizer.height(25),
        height: Sizer.width(25),
        child: <PERSON><PERSON>(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            Icon(
              Iconsax.notification5,
              color: AppColors.primaryBlue,
              size: Sizer.radius(24),
            ),
            if ((int.tryParse(count) ?? 0) > 0)
              Positioned(
                right: -3,
                top: -5,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(3),
                    vertical: Sizer.height(1),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.iconBadge,
                    borderRadius: BorderRadius.circular(
                      Sizer.width(10),
                    ),
                  ),
                  child: Text(
                    count,
                    style: AppTypography.text10.copyWith(
                      color: AppColors.bgWhite,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
