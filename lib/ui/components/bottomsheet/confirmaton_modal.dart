import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConfirmationModalArgs {
  final String title;
  final String description;
  final String btnText;
  final VoidCallback btnTap;

  ConfirmationModalArgs({
    required this.title,
    required this.description,
    required this.btnText,
    required this.btnTap,
  });
}

class ConfirmationModal extends StatefulWidget {
  const ConfirmationModal({
    super.key,
    required this.args,
  });

  final ConfirmationModalArgs args;

  @override
  State<ConfirmationModal> createState() => _ConfirmationModalState();
}

class _ConfirmationModalState extends State<ConfirmationModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(44)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: <PERSON><PERSON>n(
          mainAxisSize: MainAxisSize.min,
          children: [
            YB<PERSON>(60),
            svg<PERSON><PERSON><PERSON>(
              AppSvgs.tickOn,
              height: Sizer.height(140),
            ),
            YBox(30),
            Text(
              widget.args.title,
              textAlign: TextAlign.center,
              style: FontTypography.text18.semiBold,
            ),
            YBox(4),
            Text(
              widget.args.description,
              textAlign: TextAlign.center,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray93,
              ),
            ),
            const YBox(36),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(20),
              onTap: widget.args.btnTap,
              text: widget.args.btnText,
            ),
            const YBox(30),
          ],
        ),
      ),
    );
  }
}
