// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';
// import 'package:korrency/ui/screens/auth/kyc/bottomsheets/bottomsheet.dart';

// class CompleteProfilePageViewTwo extends StatefulWidget {
//   const CompleteProfilePageViewTwo({
//     Key? key,
//     this.onPageChange,
//     required this.kycVm,
//   }) : super(key: key);

//   final VoidCallback? onPageChange;
//   final KycVM kycVm;

//   @override
//   State<CompleteProfilePageViewTwo> createState() =>
//       _CompleteProfilePageViewTwoState();
// }

// class _CompleteProfilePageViewTwoState
//     extends State<CompleteProfilePageViewTwo> {
//   DateTime? selectedDate = DateTime.now();
//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       child: Container(
//         padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const AuthTextSubTitle(
//               title: "Complete your profile",
//               subtitle: "Let’s get to know more about you",
//             ),
//             const YBox(20),
//             const InfoContainer(
//                 text:
//                     "Please enter your details as it appears on your government issued identity document."),
//             const YBox(20),
//             CustomTextField(
//               labelText: "Gender",
//               showLabelHeader: true,
//               isReadOnly: true,
//               controller: widget.kycVm.genderC,
//               borderRadius: Sizer.height(4),
//               hintText: 'Gender',
//               showSuffixIcon: true,
//               suffixIcon: Icon(
//                 Icons.expand_more,
//                 color: AppColors.gray500,
//                 size: Sizer.height(26),
//               ),
//               onChanged: (p0) => widget.kycVm.reBuildUI(),
//               onTap: () {
//                 BsWrapper.bottomSheet(
//                   context: context,
//                   widget: const GenderSheet(),
//                 );
//               },
//             ),
//             const YBox(24),
//             CustomTextField(
//               isReadOnly: true,
//               labelText: "Date of Birth",
//               showLabelHeader: true,
//               borderRadius: Sizer.height(4),
//               showSuffixIcon: true,
//               controller: widget.kycVm.formattedDobC,
//               suffixIcon: Icon(
//                 Icons.event,
//                 color: AppColors.gray500,
//                 size: Sizer.height(26),
//               ),
//               onTap: () {
//                 showCupertinoDatePicker(
//                   context,
//                   onDateTimeChanged: (val) {
//                     selectedDate = val;
//                   },
//                   onDone: () {
//                     if (selectedDate != null) {
//                       // Calculate age
//                       DateTime currentDate = DateTime.now();
//                       int age = currentDate.year - selectedDate!.year;
//                       if (currentDate.month < selectedDate!.month ||
//                           (currentDate.month == selectedDate!.month &&
//                               currentDate.day < selectedDate!.day)) {
//                         age--;
//                       }
//                       // Check if age is 18 or above
//                       if (age >= 18) {
//                         // Do something with the selected date
//                         printty('Selected date: $selectedDate');
//                         widget.kycVm.formattedDobC.text =
//                             AppUtils.dayWithSuffixMonthAndYear(selectedDate!);
//                         widget.kycVm.dobC.text =
//                             selectedDate!.toIso8601String().split("T").first;
//                         Navigator.pop(context);
//                       } else {
//                         FlushBarToast.fLSnackBar(
//                           message: 'Age must be 18 or above',
//                         );
//                       }
//                     }
//                   },
//                 );
//               },
//               onChanged: (val) => widget.kycVm.reBuildUI(),
//             ),
//             const YBox(24),
//             CustomTextField(
//               labelText: "Occupation",
//               hintText: 'Select your occupation',
//               isReadOnly: true,
//               showLabelHeader: true,
//               borderRadius: Sizer.height(4),
//               controller: widget.kycVm.occupationC,
//               suffixIcon: Icon(
//                 Icons.expand_more,
//                 color: AppColors.gray500,
//                 size: Sizer.height(26),
//               ),
//               onChanged: (val) => widget.kycVm.reBuildUI(),
//               onTap: () {
//                 BsWrapper.bottomSheet(
//                   context: context,
//                   widget: const OccupationSheet(),
//                 );
//               },
//             ),
//             const YBox(120),
//             CustomBtn.solid(
//               onTap: () {
//                 _submitKycStep2();
//               },
//               online: widget.kycVm.iskycStep2Active,
//               text: "Continue",
//             ),
//             const YBox(50),
//           ],
//         ),
//       ),
//     );
//   }

//   _submitKycStep2() {
//     widget.kycVm.kycStep2().then((value) {
//       if (value.success && widget.onPageChange != null) {
//         // showToast(value.message.toString(), true);
//         context.read<AuthUserVM>().setUser(value.data);

//         widget.onPageChange!();
//         widget.kycVm.clearKyc2();
//       } else {
//         showToast(value.message.toString(), false);
//       }
//     });
//   }

//   showToast(String m, bool isSuccess) {
//     FlushBarToast.fLSnackBar(
//       message: m,
//       snackBarType: isSuccess ? SnackBarType.success : SnackBarType.warning,
//     );
//   }

//   // void showDatePickerDialog(BuildContext context) async {
//   //   DateTime? selectedDate = await showDatePicker(
//   //     context: context,
//   //     initialDate: DateTime.now(),
//   //     firstDate: DateTime(DateTime.now().year - 100),
//   //     lastDate: DateTime.now(),
//   //     initialDatePickerMode: DatePickerMode.year,
//   //   );

//   //   if (selectedDate != null) {
//   //     // Calculate age
//   //     DateTime currentDate = DateTime.now();
//   //     int age = currentDate.year - selectedDate.year;
//   //     if (currentDate.month < selectedDate.month ||
//   //         (currentDate.month == selectedDate.month &&
//   //             currentDate.day < selectedDate.day)) {
//   //       age--;
//   //     }

//   //     // Check if age is 18 or above
//   //     if (age >= 18) {
//   //       // Do something with the selected date
//   //       print('Selected date: $selectedDate');
//   //     } else {
//   //       // Show error message or handle under 18 age case

//   //       /// Shows an age restriction dialog.
//   //       ///
//   //       /// This method displays an [AlertDialog] with a title and content
//   //       /// informing the user that they must be at least 18 years old.
//   //       /// The dialog includes an "OK" button to dismiss it.
//   //       void showAgeRestrictionDialog(BuildContext context) {
//   //         showDialog(
//   //           context: context,
//   //           builder: (context) => AlertDialog(
//   //             title: const Text('Age Restriction'),
//   //             content: const Text('You must be at least 18 years old.'),
//   //             actions: [
//   //               TextButton(
//   //                 onPressed: () => Navigator.pop(context),
//   //                 child: const Text('OK'),
//   //               ),
//   //             ],
//   //           ),
//   //         );
//   //       }
//   //     }
//   //   }
//   // }
// }
