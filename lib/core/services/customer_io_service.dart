import 'dart:async';
import 'dart:io';

import 'package:customer_io/customer_io.dart';
import 'package:customer_io/customer_io_config.dart';
import 'package:customer_io/customer_io_enums.dart';
import 'package:korrency/core/core.dart';

class CustomerIOService {
  CustomerIOService._();
  static CustomerIOService? _instance;
  static CustomerIOService get instance => _instance ??= CustomerIOService._();

  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    try {
      final siteId = EnvConfig.customerIOSiteId;
      final apiKey = EnvConfig.customerIOApiKey;
      final regionStr = EnvConfig.customerIORegion.toLowerCase();
      // Use v1.x SDK enum
      final region = regionStr == 'eu' ? Region.eu : Region.us;

      if (siteId.isEmpty || apiKey.isEmpty) {
        printty('❌ Customer.io env values missing: SITE_ID/API_KEY');
        return;
      }

      // Initialize using Flutter SDK v1.x API (requires a config object)
      CustomerIO.initialize(
        config: CustomerIOConfig(
          siteId: siteId,
          apiKey: apiKey,
          region: region,
        ),
      );

      _initialized = true;
      printty('✅ Customer.io initialized');
    } catch (e) {
      printty('❌ Customer.io initialization error: $e');
    }
  }

  Future<void> identify(
      {required String userId, Map<String, dynamic>? attributes}) async {
    if (!_initialized) return;
    try {
      // Identify the user in Customer.io
      CustomerIO.identify(identifier: userId, attributes: attributes ?? {});
      await StorageService.storeStringItem(StorageKey.customerIoUserId, userId);
      await StorageService.storeBoolItem(StorageKey.customerIoIdentified, true);
      printty('👤 Customer.io identified: $userId');
    } catch (e) {
      printty('❌ Customer.io identify error: $e');
    }
  }

  Future<void> clearIdentify() async {
    if (!_initialized) return;
    try {
      CustomerIO.clearIdentify();
      await StorageService.removeStringItem(StorageKey.customerIoUserId);
      await StorageService.removeBoolItem(StorageKey.customerIoIdentified);
      printty('🚪 Customer.io cleared identification');
    } catch (e) {
      printty('❌ Customer.io clearIdentify error: $e');
    }
  }

  Future<void> registerDeviceToken(String token) async {
    if (!_initialized) return;
    try {
      await _executeWithRetry(() async {
        CustomerIO.registerDeviceToken(deviceToken: token);
      });
      printty('📲 Customer.io device token registered');
    } catch (e) {
      printty('❌ Customer.io token registration error: $e');
    }
  }

  Future<void> track(String eventName, Map<String, dynamic> meta) async {
    if (!_initialized) return;
    // Prevent tracking when not identified
    final identified =
        await StorageService.getBoolItem(StorageKey.customerIoIdentified) ??
            false;
    if (!identified) {
      printty('⚠️ Skipping Customer.io track; user not identified');
      return;
    }

    // Basic event validation
    if (eventName.isEmpty) {
      printty('⚠️ Invalid event name for Customer.io');
      return;
    }

    try {
      await _executeWithRetry(() async {
        CustomerIO.track(name: eventName, attributes: meta);
      });
      printty('📤 Customer.io tracked: $eventName');
    } catch (e) {
      printty('❌ Customer.io track error for "$eventName": $e');
    }
  }

  Future<void> setUserProperties(Map<String, dynamic> attributes) async {
    if (!_initialized) return;
    try {
      final userId =
          await StorageService.getStringItem(StorageKey.customerIoUserId);
      if (userId == null || userId.isEmpty) {
        printty('⚠️ Cannot set Customer.io attributes; user not identified');
        return;
      }
      CustomerIO.identify(identifier: userId, attributes: attributes);
      printty('📝 Customer.io attributes updated: $attributes');
    } catch (e) {
      printty('❌ Customer.io setUserProperties error: $e');
    }
  }

  Future<void> trackCreateAccount({
    required String userId,
    required String method,
    required DateTime timestamp,
    required bool hasReferral,
    Map<String, dynamic>? referralData,
  }) async {
    final meta = {
      'signup_method': method,
      'timestamp': timestamp.toIso8601String(),
      'has_referral': hasReferral,
      ...?referralData,
    };
    await track('create_account', meta);
  }

  Future<void> trackCompleteKyc({
    required String userId,
    required String verificationLevel,
    required DateTime completionTime,
    String? rejectionReason,
    Map<String, dynamic>? extra,
  }) async {
    final meta = {
      'verification_level': verificationLevel,
      'completion_time': completionTime.toIso8601String(),
      if (rejectionReason != null && rejectionReason.isNotEmpty)
        'rejection_reason': rejectionReason,
      ...?extra,
    };
    await track('complete_kyc', meta);
  }

  Future<void> trackDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    String? paymentMethod,
    String? corridor,
    DateTime? timestamp,
    Map<String, dynamic>? extra,
  }) async {
    final meta = {
      'user_id': userId,
      'amount': amount,
      'currency': currency,
      if (paymentMethod != null && paymentMethod.isNotEmpty)
        'payment_method': paymentMethod,
      if (corridor != null && corridor.isNotEmpty) 'corridor': corridor,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      ...?extra,
    };
    await track('deposit_money', meta);
  }

  Future<void> trackSendMoney({
    required String userId,
    required double amount,
    required String fromCurrency,
    required String toCurrency,
    required String corridor,
    String? transactionId,
    DateTime? timestamp,
    Map<String, dynamic>? extra,
  }) async {
    final meta = {
      'user_id': userId,
      'amount': amount,
      'from_currency': fromCurrency,
      'to_currency': toCurrency,
      'corridor': corridor,
      if (transactionId != null && transactionId.isNotEmpty)
        'transaction_id': transactionId,
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      ...?extra,
    };
    await track('send_money', meta);
  }

  Future<void> _executeWithRetry(Future<void> Function() action) async {
    const maxAttempts = 3;
    int attempt = 0;
    int delayMs = 500;

    while (true) {
      attempt++;
      try {
        // Simple network connectivity check
        await InternetAddress.lookup('example.com');
        await action();
        return;
      } catch (e) {
        final isRateLimit = e.toString().contains('429') ||
            e.toString().toLowerCase().contains('rate');
        if (attempt >= maxAttempts || (!isRateLimit && attempt > 1)) {
          rethrow;
        }
        await Future.delayed(Duration(milliseconds: delayMs));
        delayMs *= 2; // Exponential backoff
      }
    }
  }
}
