import 'package:korrency/core/core.dart';

class RefListTile extends StatelessWidget {
  const RefListTile({
    super.key,
    required this.name,
    this.avater,
    required this.status,
    this.date,
    this.onTap,
  });

  final String name;
  final String? avater;
  final String status;
  final DateTime? date;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            height: Sizer.height(44),
            width: Sizer.height(44),
            decoration: BoxDecoration(
              color: AppColors.yellowCD,
              borderRadius: BorderRadius.circular(Sizer.radius(100)),
            ),
            child: Center(
              child: Text(
                AppUtils.getInitials(name),
                style: AppTypography.text16.semiBold.copyWith(
                  color: AppColors.iconBlack800.withValues(alpha: 0.6),
                ),
              ),
            ),
          ),
          const XBox(12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textBlack100,
                ),
              ),
              // if (date != null)
              Padding(
                padding: EdgeInsets.only(top: Sizer.height(4)),
                child: Text(
                  AppUtils.formatDateTimeTwo(date ?? DateTime.now()),
                  style: AppTypography.text12.copyWith(
                    color: AppColors.gray79,
                  ),
                ),
              ),
            ],
          ),
          const Spacer(),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(8),
              vertical: Sizer.height(2),
            ),
            decoration: BoxDecoration(
              color: getReferralStatusBgColor(status),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              status,
              style: AppTypography.text12.copyWith(
                color: getReferralStatusColor(status),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

Color getReferralStatusColor(String status) {
  final stringLower = status.toLowerCase();
  switch (stringLower) {
    case 'pending':
      return AppColors.pending;
    case 'completed':
      return AppColors.greenA48;
    case 'earned':
      return AppColors.textBlue500;
    case 'ineligible':
      return AppColors.sent;
    default:
      return AppColors.primaryBlue;
  }
}

Color getReferralStatusBgColor(String status) {
  final stringLower = status.toLowerCase();
  switch (stringLower) {
    case 'pending':
      return AppColors.yellowAEB;
    case 'completed':
      return AppColors.greenDF3;
    case 'earned':
      return AppColors.textBlue500.withValues(alpha: 0.1);
    case 'ineligible':
      return AppColors.sent.withValues(alpha: 0.1);
    default:
      return AppColors.litGrey;
  }
}
