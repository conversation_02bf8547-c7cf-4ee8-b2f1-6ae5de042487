// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';

// class SelectCountrySheet extends StatelessWidget {
//   const SelectCountrySheet({Key? key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Consumer<OnBoardVM>(builder: (context, vm, _) {
//       return ContainerWithTopBorderRadius(
//           child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           const YBox(20),
//           InkWell(
//             onTap: () {
//               Navigator.pop(context);
//             },
//             child: Container(
//               alignment: Alignment.centerRight,
//               child: Icon(
//                 Icons.close,
//                 size: Sizer.radius(23),
//               ),
//             ),
//           ),
//           Container(
//             padding: EdgeInsets.only(
//               top: Sizer.height(20),
//             ),
//             alignment: Alignment.center,
//             child: Text(
//               "Select Country",
//               style: AppTypography.text16.copyWith(
//                 fontWeight: FontWeight.w600,
//               ),
//             ),
//           ),
//           const YBox(25),
//           ...List.generate(
//             countries.length,
//             (i) => Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 ContainerWithBluewishBg(
//                   padding: EdgeInsets.symmetric(
//                     vertical: Sizer.height(20),
//                     horizontal: Sizer.width(16),
//                   ),
//                   child: WalletListTile(
//                     title: countries[i].name,
//                     isSelected: vm.country == countries[i],
//                     currencyIcon: countries[i].flag,
//                     icon: Icons.check_circle,
//                     onTap: () {
//                       vm.setCounctry(countries[i]);
//                       Navigator.pop(context);
//                     },
//                   ),
//                 ),
//                 const YBox(16),
//               ],
//             ),
//           ),
//           const YBox(50),
//         ],
//       ));
//     });
//   }
// }
