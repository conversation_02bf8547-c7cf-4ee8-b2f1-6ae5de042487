import 'package:korrency/core/core.dart';

navigateToWalletScreen({
  required BuildContext context,
  required String currencyCode,
  required Wallet wallet,
}) {
  switch (currencyCode) {
    case "NGN":
      Navigator.pushNamed(
        context,
        RoutePath.ngnWalletDetailsScreen,
        arguments: wallet,
      );
      break;
    case "CAD":
      Navigator.pushNamed(
        context,
        RoutePath.cadWalletDetailsScreen,
        arguments: wallet,
      );
      break;

    case "GBP":
    case "EUR":
      Navigator.pushNamed(
        context,
        RoutePath.gbpWalletDetailsScreen,
        arguments: wallet,
      );
      break;
    default:
      Navigator.pushNamed(
        context,
        RoutePath.otherWalletDetailsScreen,
        arguments: wallet,
      );
      break;
  }
}
