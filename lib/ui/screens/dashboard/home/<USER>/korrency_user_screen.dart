// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:permission_handler/permission_handler.dart';

class KorrencyUserScreen extends StatefulWidget {
  const KorrencyUserScreen({super.key});

  @override
  State<KorrencyUserScreen> createState() => _KorrencyUserScreenState();
}

class _KorrencyUserScreenState extends State<KorrencyUserScreen> {
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() {
    context.read<PhoneBookVM>().fetchContacts();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PhoneBookVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Send to Korrency user',
        ),
        body: ListView(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          children: [
            const YBox(30),
            CustomTextField(
              borderRadius: Sizer.height(20),
              controller: _searchController,
              prefixIcon: Icon(
                Iconsax.search_normal_1,
                color: AppColors.black600,
                size: Sizer.radius(20),
              ),
              hintText: 'Search...',
              suffixIcon: CopyWithIcon(
                text: 'Paste',
                margin: EdgeInsets.only(
                  top: Sizer.height(11),
                  bottom: Sizer.height(11),
                  right: Sizer.width(16),
                ),
                onPressed: () async {
                  final data = await Clipboard.getData('text/plain');
                  if (data != null) {
                    _searchController.text = data.text ?? "";
                    vm.getPhoneBooks(searchQuery: data.text ?? "");
                  }
                },
              ),
              onChanged: (val) {
                printty(val);
                vm.getPhoneBooks(searchQuery: val.trim());
              },
            ),
            const YBox(12),
            Builder(builder: (context) {
              if (vm.permissionDenied) {
                return const CheckContactContent();
              }
              if (vm.busy(fetchContactsState)) {
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.only(top: 40),
                  itemBuilder: (ctx, i) {
                    return Skeletonizer(
                      child: UserListTile(
                        name: BoneMock.words(5),
                        showTrailing: true,
                        showSubTitle: true,
                        subTitle: BoneMock.date,
                        onTap: () {},
                      ),
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(26),
                  itemCount: 10,
                );
              }
              if (vm.korrencyUsers.isEmpty && !vm.isBusy) {
                return SizedBox(
                  height: Sizer.height(500),
                  child: Center(
                    child: Text(
                      "No Korrency user found",
                      style: AppTypography.text18.copyWith(
                        color: AppColors.textBlack600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              }
              return Column(
                children: [
                  const YBox(26),
                  ...vm.korrencyUsers.entries.map((e) {
                    return ContactListWidget(
                      title: e.key,
                      korrencyUsers: e.value,
                    );
                  }),
                  const YBox(100)
                ],
              );
            }),
          ],
        ),
      );
    });
  }
}

class ContactListWidget extends StatelessWidget {
  const ContactListWidget({
    super.key,
    required this.title,
    required this.korrencyUsers,
  });

  final String title;
  final List<KorrencyUser> korrencyUsers;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title.trim().isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(26),
              Text(
                title,
                style: AppTypography.text14,
              ),
              const YBox(16),
            ],
          ),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemBuilder: (ctx, i) {
            final user = korrencyUsers[i];
            return SendMoneyBenrficiaryListTile(
              padding: EdgeInsets.zero,
              name: (user.fullName == null || user.fullName?.trim() == "")
                  ? "Korrency User"
                  : user.fullName ?? '',
              title: (user.fullName == null || user.fullName?.trim() == "")
                  ? "Korrency User"
                  : user.fullName ?? '',
              subtitle: "${user.userName ?? "N/A"}",
              iconPath: user.avatar ?? "",
              onTap: () {
                context.read<SendMoneyVM>().setKorrencyUsername(user.userName);
                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                  arguments: SendMoneyReviewsArg(
                    name: (user.fullName == null || user.fullName?.trim() == "")
                        ? "Korrency User"
                        : user.fullName ?? '',
                    title:
                        (user.fullName == null || user.fullName?.trim() == "")
                            ? "Korrency User"
                            : user.fullName ?? '',
                    iconPath: user.avatar ?? "",
                    subTitle: "${user.userName ?? "N/A"}",
                  ),
                );
              },
            );
          },
          separatorBuilder: (ctx, i) => const YBox(24),
          itemCount: korrencyUsers.length,
        ),
      ],
    );
  }
}

class CheckContactContent extends StatelessWidget {
  const CheckContactContent({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const YBox(80),
          SizedBox(
            height: Sizer.height(236),
            width: Sizer.width(236),
            child: imageHelper(AppImages.phoneUser),
          ),
          const YBox(16),
          Text(
            "You can also check your contact list for a user who is on the platform.",
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack600,
            ),
          ),
          const YBox(120),
          CustomBtn.solid(
            onTap: () async {
              // context.read<PhoneBookVM>().fetchContacts();
              final res = await Permission.contacts.status;
              if (res.isDenied) {
                await openAppSettings();
              }
              context.read<PhoneBookVM>().fetchContacts();
            },
            online: true,
            text: "Give Permission",
          ),
          const YBox(80),
        ],
      ),
    );
  }
}
