import 'package:korrency/core/core.dart';

class QuickSendWidget extends StatelessWidget {
  const QuickSendWidget({
    super.key,
    required this.beneficiaryName,
    required this.currencyFlag,
    this.onTap,
  });

  final String beneficiaryName;
  final String currencyFlag;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(Sizer.radius(12)),
        width: Sizer.width(130),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.grayADA,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(16)),
        ),
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      Container(
                        height: Sizer.height(44),
                        width: Sizer.width(44),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(Sizer.radius(40)),
                          gradient: LinearGradient(
                            colors: [
                              AppColors.blueOFE,
                              AppColors.blue9FD,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: Center(
                          // beneficiaryName initials
                          child: Text(
                            AppUtils.getInitials(beneficiaryName),
                            style: AppTypography.text16.medium.copyWith(
                              color: AppColors.iconBlack800,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        child: Skeleton.replace(
                          replacement: Bone.circle(size: Sizer.radius(16)),
                          child: ClipRRect(
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(20)),
                            child: SizedBox(
                              height: Sizer.height(14),
                              width: Sizer.width(14),
                              child: SvgPicture.network(
                                currencyFlag,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  XBox(8),
                  Skeleton.replace(
                    replacement: Bone.circle(size: Sizer.radius(44)),
                    child: Container(
                      height: Sizer.height(44),
                      width: Sizer.width(44),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.grayADA,
                        ),
                        borderRadius: BorderRadius.circular(Sizer.radius(40)),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          AppSvgs.send2,
                          height: Sizer.height(24),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              YBox(8),
              Text(
                beneficiaryName,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray51,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
