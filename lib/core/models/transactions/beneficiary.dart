import 'dart:convert';

import 'package:korrency/core/enums/enums.dart';
import 'package:korrency/core/models/send_money_reason.dart';
import 'package:korrency/core/models/user/auth_user_res.dart';
import 'package:korrency/core/models/wallets/currency.dart';

List<Beneficiary> beneficiaryFromJson(String str) => List<Beneficiary>.from(
    json.decode(str).map((x) => Beneficiary.fromJson(x)));

String beneficiaryToJson(List<Beneficiary> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Beneficiary {
  int? id;
  String? institutionName;
  String? institutionCode;
  String? accountName;
  String? firstName;
  String? lastName;
  String? iconUrl;
  String? accountIdentifier;
  String? transferMethod;
  Currency? currency;
  LatestTransaction? latestTransaction;
  DateTime? createdAt;

  Beneficiary({
    this.id,
    this.institutionName,
    this.institutionCode,
    this.accountName,
    this.firstName,
    this.lastName,
    this.iconUrl,
    this.accountIdentifier,
    this.transferMethod,
    this.currency,
    this.latestTransaction,
    this.createdAt,
  });

  factory Beneficiary.fromJson(Map<String, dynamic> json) => Beneficiary(
        id: json["id"],
        institutionName: json["institution_name"],
        iconUrl: json["icon_url"],
        institutionCode: json["institution_code"],
        accountName: json["account_name"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        accountIdentifier: json["account_identifier"],
        transferMethod: json["transfer_method"],
        currency: json["currency"] == null
            ? null
            : Currency.fromJson(json["currency"]),
        latestTransaction: json["latest_transaction"] == null
            ? null
            : LatestTransaction.fromJson(json["latest_transaction"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "institution_name": institutionName,
        "icon_url": iconUrl,
        "institution_code": institutionCode,
        "account_name": accountName,
        "first_name": firstName,
        "last_name": lastName,
        "account_identifier": accountIdentifier,
        "currency": currency?.toJson(),
        "latest_transaction": latestTransaction?.toJson(),
        "created_at": createdAt?.toIso8601String(),
      };
}

class PostBeneficiaryArgs {
  final int currencyId;
  final String? institutionName;
  final String? institutionCode;
  final String? accountName;
  final String? firstName;
  final String? lastName;
  final String? accountIdentifier;
  final String? transferMethod;

  PostBeneficiaryArgs({
    required this.currencyId,
    this.institutionName,
    this.institutionCode,
    this.accountName,
    this.firstName,
    this.lastName,
    this.accountIdentifier,
    this.transferMethod,
  });

  @override
  String toString() {
    return 'PostBeneficiaryArgs(currencyId: $currencyId, institutionName: $institutionName, institutionCode: $institutionCode, accountName: $accountName, accountIdentifier: $accountIdentifier)';
  }
}

class BeneficiaryArg {
  final int currencyId;
  final TransferMethodType transferMethodType;

  BeneficiaryArg({
    required this.currencyId,
    this.transferMethodType = TransferMethodType.bankTransfer,
  });

  @override
  String toString() {
    return 'BeneficiaryArg(currencyId: $currencyId, transferMethodType: $transferMethodType)';
  }
}

class LatestTransaction {
  final int? id;
  final String? status;
  final String? amount;
  final String? rate;
  final dynamic promoRate;
  final String? rateType;
  final String? rateFormat;
  final Currency? currency;
  final String? description;
  final String? fees;
  final String? reference;
  final String? category;
  final String? type;
  final String? runningBalance;
  final String? convertedAmount;
  final Currency? convertedCurrency;
  final String? source;
  final String? destination;
  final bool? isFlagged;
  final String? channel;
  final PayoutProviders? payoutProviders;
  final String? responseMessage;
  final dynamic errors;
  final String? interacSecurityAnswer;
  final SendMoneyPurpose? purpose;
  final dynamic sessionId;
  final dynamic relatedTransactionId;
  final String? corridor;
  final AuthUser? user;
  final String? sendersName;
  final String? receivedAmount;
  final Currency? receivedCurrency;
  final String? recipientName;
  final bool? isHeldForReview;
  final DateTime? initiatedAt;
  final DateTime? completedAt;
  final String? timeToCompletion;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  LatestTransaction({
    this.id,
    this.status,
    this.amount,
    this.rate,
    this.promoRate,
    this.rateType,
    this.rateFormat,
    this.currency,
    this.description,
    this.fees,
    this.reference,
    this.category,
    this.type,
    this.runningBalance,
    this.convertedAmount,
    this.convertedCurrency,
    this.source,
    this.destination,
    this.isFlagged,
    this.channel,
    this.payoutProviders,
    this.responseMessage,
    this.errors,
    this.interacSecurityAnswer,
    this.purpose,
    this.sessionId,
    this.relatedTransactionId,
    this.corridor,
    this.user,
    this.sendersName,
    this.receivedAmount,
    this.receivedCurrency,
    this.recipientName,
    this.isHeldForReview,
    this.initiatedAt,
    this.completedAt,
    this.timeToCompletion,
    this.createdAt,
    this.updatedAt,
  });

  factory LatestTransaction.fromJson(Map<String, dynamic> json) =>
      LatestTransaction(
        id: json["id"],
        status: json["status"],
        amount: json["amount"],
        rate: json["rate"],
        promoRate: json["promo_rate"],
        rateType: json["rate_type"],
        rateFormat: json["rate_format"],
        currency: json["currency"] == null
            ? null
            : Currency.fromJson(json["currency"]),
        description: json["description"],
        fees: json["fees"],
        reference: json["reference"],
        category: json["category"],
        type: json["type"],
        runningBalance: json["running_balance"],
        convertedAmount: json["converted_amount"],
        convertedCurrency: json["converted_currency"] == null
            ? null
            : Currency.fromJson(json["converted_currency"]),
        source: json["source"],
        destination: json["destination"],
        isFlagged: json["is_flagged"],
        channel: json["channel"],
        payoutProviders: json["payout_providers"] == null
            ? null
            : PayoutProviders.fromJson(json["payout_providers"]),
        responseMessage: json["response_message"],
        errors: json["errors"],
        interacSecurityAnswer: json["interac_security_answer"],
        purpose: json["purpose"] == null
            ? null
            : SendMoneyPurpose.fromJson(json["purpose"]),
        sessionId: json["session_id"],
        relatedTransactionId: json["related_transaction_id"],
        corridor: json["corridor"],
        user: json["user"] == null ? null : AuthUser.fromJson(json["user"]),
        sendersName: json["senders_name"],
        receivedAmount: json["received_amount"],
        receivedCurrency: json["received_currency"] == null
            ? null
            : Currency.fromJson(json["received_currency"]),
        recipientName: json["recipient_name"],
        isHeldForReview: json["is_held_for_review"],
        initiatedAt: json["initiated_at"] == null
            ? null
            : DateTime.parse(json["initiated_at"]),
        completedAt: json["completed_at"] == null
            ? null
            : DateTime.parse(json["completed_at"]),
        timeToCompletion: json["time_to_completion"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "status": status,
        "amount": amount,
        "rate": rate,
        "promo_rate": promoRate,
        "rate_type": rateType,
        "rate_format": rateFormat,
        "currency": currency?.toJson(),
        "description": description,
        "fees": fees,
        "reference": reference,
        "category": category,
        "type": type,
        "running_balance": runningBalance,
        "converted_amount": convertedAmount,
        "converted_currency": convertedCurrency?.toJson(),
        "source": source,
        "destination": destination,
        "is_flagged": isFlagged,
        "channel": channel,
        "payout_providers": payoutProviders?.toJson(),
        "response_message": responseMessage,
        "errors": errors,
        "interac_security_answer": interacSecurityAnswer,
        "purpose": purpose?.toJson(),
        "session_id": sessionId,
        "related_transaction_id": relatedTransactionId,
        "corridor": corridor,
        "user": user?.toJson(),
        "senders_name": sendersName,
        "received_amount": receivedAmount,
        "received_currency": receivedCurrency?.toJson(),
        "recipient_name": recipientName,
        "is_held_for_review": isHeldForReview,
        "initiated_at": initiatedAt?.toIso8601String(),
        "completed_at": completedAt?.toIso8601String(),
        "time_to_completion": timeToCompletion,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class PayoutProviders {
  final String? payoutProvider;
  final String? payoutProviderStatus;
  final dynamic alternatePayoutProvider;
  final dynamic alternatePayoutProviderStatus;
  final dynamic error;

  PayoutProviders({
    this.payoutProvider,
    this.payoutProviderStatus,
    this.alternatePayoutProvider,
    this.alternatePayoutProviderStatus,
    this.error,
  });

  factory PayoutProviders.fromJson(Map<String, dynamic> json) =>
      PayoutProviders(
        payoutProvider: json["payout_provider"],
        payoutProviderStatus: json["payout_provider_status"],
        alternatePayoutProvider: json["alternate_payout_provider"],
        alternatePayoutProviderStatus: json["alternate_payout_provider_status"],
        error: json["error"],
      );

  Map<String, dynamic> toJson() => {
        "payout_provider": payoutProvider,
        "payout_provider_status": payoutProviderStatus,
        "alternate_payout_provider": alternatePayoutProvider,
        "alternate_payout_provider_status": alternatePayoutProviderStatus,
        "error": error,
      };
}
