// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransferMethodModal extends StatefulWidget {
  const TransferMethodModal({super.key});

  @override
  State<TransferMethodModal> createState() => _TransferMethodModalState();
}

class _TransferMethodModalState extends State<TransferMethodModal> {
  @override
  Widget build(BuildContext context) {
    final sendMoneyVM = context.watch<SendMoneyVM>();
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(12)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                YBox(20),
                Text(
                  "Select Transfer method",
                  style: AppTypography.text18.semiBold,
                ),
                YBox(32),
                Builder(builder: (context) {
                  if (sendMoneyVM.paymentMethods.isEmpty) {
                    return const Center(
                      child: Text("No payment methods available"),
                    );
                  }
                  return ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (ctx, i) {
                      return MethodOptionTile(
                        icon: sendMoneyVM.paymentMethods[i].icon ?? "",
                        title:
                            "Send to ${sendMoneyVM.paymentMethods[i].name ?? ""}",
                        subTitle: _getSubTitle(sendMoneyVM.paymentMethods[i]),
                        onTap: () {
                          printty(
                              'Single item ${sendMoneyVM.paymentMethods[i]}');
                          _switchMethod(sendMoneyVM.paymentMethods[i]);
                        },
                      );
                      // return CustomListViews.view(
                      //     // icon: Iconsax.bank,
                      //     isSvg: true,
                      //     isNetworkSvg: true,
                      //     icon: sendMoneyVM.paymentMethods[i].icon ?? "",
                      //     title: sendMoneyVM.paymentMethods[i].name ?? "",
                      //     onTap: () {
                      //       printty(
                      //           'Single item ${sendMoneyVM.paymentMethods[i]}');
                      //       _switchMethod(sendMoneyVM.paymentMethods[i]);
                      //     });
                    },
                    separatorBuilder: (_, __) => const YBox(8),
                    itemCount: sendMoneyVM.paymentMethods.length,
                  );
                }),
                YBox(60)
              ],
            ),
          ),
        ],
      ),
    );
  }

  _getSubTitle(PaymentMethod method) {
    final sendVm = context.read<SendMoneyVM>();
    if (method.id?.toLowerCase() == 'bank_transfer') {
      return "Send money to a ${sendVm.recipientCurrency?.name} Bank Account";
    } else if (method.id?.toLowerCase() == 'interac') {
      return "Send money to a ${sendVm.recipientCurrency?.name} Bank Account";
    } else if (method.id?.toLowerCase() == 'mobile_money') {
      return "Send money to a ${sendVm.recipientCurrency?.name} Mobile Wallet";
    } else if (method.id?.toLowerCase() == 'iban') {
      return "Send money to a ${sendVm.recipientCurrency?.code} Bank Account";
    } else {
      return "Send money to a friend on the Korrency app";
    }
  }

  /// Here we pass the payment method to validation
  /// To use namecheck
  _switchMethod(PaymentMethod method) async {
    final vm = context.read<SendMoneyVM>();
    if (method.id?.toLowerCase() == 'bank_transfer') {
      vm.setTransferMethodType(TransferMethodType.bankTransfer);
      Navigator.pushNamed(context, RoutePath.sendMoneyBeneficiaryListScreen,
          arguments: TransferMethodArg(
            title: 'Send to ${vm.recipientCurrency?.code} Bank Account',
            paymentMethod: method,
          ));
    } else if (method.id?.toLowerCase() == 'interac') {
      vm.setTransferMethodType(TransferMethodType.interac);
      Navigator.pushNamed(context, RoutePath.sendMoneyBeneficiaryListScreen,
          arguments: TransferMethodArg(
            title: 'Interac e-Transfer',
            paymentMethod: method,
          ));
    } else if (method.id?.toLowerCase() == 'mobile_money') {
      vm.setTransferMethodType(TransferMethodType.mobileMoney);
      Navigator.pushNamed(context, RoutePath.sendMoneyBeneficiaryListScreen,
          arguments: TransferMethodArg(
            title: 'Send to ${vm.recipientCurrency?.code} Mobile Wallet',
            paymentMethod: method,
          ));
    } else if (method.id?.toLowerCase() == 'iban') {
      vm.setTransferMethodType(TransferMethodType.iban);
      Navigator.pushNamed(context, RoutePath.sendMoneyBeneficiaryListScreen,
          arguments: TransferMethodArg(
            title: 'Send to ${vm.recipientCurrency?.code} Bank Account',
            paymentMethod: method,
          ));
    } else {
      vm.setTransferMethodType(TransferMethodType.korrency);
      if (await context.read<PhoneBookVM>().checkForPermission()) {
        Navigator.pushNamed(context, RoutePath.sendToKorrencyUser,
            arguments: method);
      } else {
        await BsWrapper.bottomSheet(
          context: context,
          widget: KorrencyUserPermissionModal(),
        );
      }
      // Navigator.pushNamed(context, RoutePath.sendToKorrencyUser,
      //     arguments: method);
      // Navigator.pushNamed(context, RoutePath.sendMoneyBeneficiaryListScreen,
      //     arguments: TransferMethodArg(
      //       title: 'Send to ${vm.recipientCurrency?.code} Bank Account',
      //       paymentMethod: method,
      //     ));
    }
  }
}

class MethodOptionTile extends StatelessWidget {
  const MethodOptionTile({
    super.key,
    required this.title,
    required this.icon,
    this.subTitle,
    this.isSelected = false,
    this.onTap,
  });

  final String title;
  final String? subTitle;
  final String icon;
  final bool isSelected;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(16),
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : AppColors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.network(
              icon,
              height: Sizer.radius(24),
              // color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
            ),
            XBox(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text16.medium.copyWith(
                      color:
                          isSelected ? AppColors.primaryBlue : AppColors.gray79,
                    ),
                  ),
                  if (subTitle != null)
                    Padding(
                      padding: EdgeInsets.only(top: Sizer.height(2)),
                      child: Text(
                        subTitle ?? '',
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.gray79,
                        ),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
