# Internationalization (i18n)

## Overview
- Uses `easy_localization` with JSON files per locale.
- Supported locales: `en`, `fr`.
- Fallback locale: `en` with fallback translations enabled.
- Persistence: Selected locale is saved and restored on app start.

## Adding a New Language
- Add the locale to `supportedLocales` in `main_*.dart`.
- Create directory `assets/translations/<locale>/`.
- Add JSON files mirroring existing structure (e.g., `menu.json`).
- Run `flutter pub get` then `flutter analyze`.

## Translation Keys Structure
- Organize by feature: `menu.json`, `authentication.json`, `dashboard.json`.
- Use nested keys for clarity, e.g. `menu.beneficiaries.title`.
- Prefer concise, reusable keys.

## Dynamic Content
- Use placeholders with `args` or `namedArgs`.
- Example: `'menu.invite_banner'.tr(args: [symbol, amount])`.
- Keep placeholders language-agnostic; avoid string concatenation.

## Fallback and Missing Translations
- Fallback locale enabled via `useFallbackTranslations: true`.
- When a key is missing, UI logs a warning and falls back to English.
- Use a helper when needed to log missing keys.

## Contributor Flow
- Add or update keys in both `en` and `fr` files.
- Avoid inline strings in widgets; always use translation keys.
- Validate layout after changes in both languages.

## Testing Checklist
- Change language and verify UI updates without restarting.
- Confirm persistence across restarts.
- Check all screens for overflow with long strings.
- Ensure critical paths have translations present.