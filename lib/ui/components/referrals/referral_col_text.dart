import 'package:korrency/core/core.dart';

class ReferralColText extends StatelessWidget {
  const ReferralColText({
    super.key,
    this.crossAxisAlignment,
    required this.title,
    required this.subtitle,
  });

  final CrossAxisAlignment? crossAxisAlignment;
  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray93,
          ),
        ),
        const YBox(4),
        Text(
          subtitle,
          style: AppTypography.text26.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.primaryBlue,
          ),
        ),
      ],
    );
  }
}
