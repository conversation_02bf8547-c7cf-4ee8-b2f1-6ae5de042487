import 'package:korrency/core/core.dart';

class WalletColTab extends StatelessWidget {
  const WalletColTab({
    super.key,
    required this.text,
    required this.iconPath,
    this.onTap,
  });

  final String text;
  final String iconPath;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          SvgPicture.asset(iconPath),
          YBox(8),
          Text(
            text,
            style: AppTypography.text14.medium
                .copyWith(color: AppColors.mainBlack),
          ),
        ],
      ),
    );
  }
}
