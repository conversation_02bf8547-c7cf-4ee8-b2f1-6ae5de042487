import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class P2PReviewScreen extends StatelessWidget {
  const P2PReviewScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // var myOfferVM = context.watch<MyOfferVM>();
    return Consumer<MarketPlaceOfferVM>(builder: (context, vm, _) {
      return Scaffold(
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(20),
                const CustomHeader(
                  // showBackBtn: true,
                  showHeader: true,
                  headerText: 'Review',
                ),
                const YBox(26),
                ContainerWithBluewishBg(
                  padding: EdgeInsets.symmetric(
                    vertical: Sizer.height(20),
                  ).copyWith(
                    left: Sizer.width(30),
                    right: Sizer.width(19),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ColumnText(
                        text: 'You Pay',
                        amount:
                            '${vm.amountForThisExchange} ${vm.offerCurrencyCode}',
                      ),
                      Text(
                        "=>",
                        style: AppTypography.text28.copyWith(
                          color: AppColors.blue800,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      ColumnText(
                        text: 'You Receive',
                        amount: vm.amountToReceive,
                      ),
                    ],
                  ),
                ),
                const YBox(24),
                DottedBorder(
                  dashPattern: const [8, 5],
                  strokeWidth: 2,
                  borderType: BorderType.RRect,
                  radius: const Radius.circular(4),
                  color: AppColors.dottedColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(26),
                  ),
                  child: SizedBox(
                    width: Sizer.screenWidth,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Fee',
                          rightText: vm.fees,
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Buyer’s rate',
                          rightText: '${vm.selectOffer?.rate}',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(30),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Delivery Method',
                          rightText: '${vm.selectOffer?.deliveryMethod}',
                          rightTextColor: AppColors.textBlue700,
                          rightFontWeight: FontWeight.w700,
                        ),
                        const YBox(16),
                        Text(
                          '- - - - - - - - - - - - - - - - - - - - - - - - - - - - ',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(8),
                        CustomListViews.currencyListText(
                          margin: EdgeInsets.zero,
                          leftText: 'Amount you’ll receive',
                          rightText: vm.amountToReceive,
                          rightTextColor: AppColors.iconGreen,
                          rightFontWeight: FontWeight.w700,
                        ),
                      ],
                    ),
                  ),
                ),
                const YBox(24),
                if (vm.selectOffer?.allowsPartialTrade ?? false)
                  ContainerWithBluewishBg(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Minimum Amount to buy",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray500,
                          ),
                        ),
                        Text(
                          "\$${AppUtils.formatAmountDoubleString(vm.selectOffer?.minimumPartialTradeAmount ?? "0")}",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.gray700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.p2pPinAuthorizeScreen,
                    );
                    // Navigator.pushNamed(
                    //   context,
                    //   RoutePath.transactopnStatusScreen,
                    //   arguments: ConfirmationArg(
                    //       title: "Transaction in Progress",
                    //       buttonText: 'Home',
                    //       imagePath: AppImages.transaction,
                    //       imageHeight: 270,
                    //       imageWidth: 270,
                    //       subtitle: const ConfirmationSubtitleText(
                    //         startText: "Recipient will",
                    //         endText: " get the money in a few minutes,",
                    //       ),
                    //       onBtnTap: () {
                    //         Navigator.pushNamed(
                    //           context,
                    //           RoutePath.dashboardNav,
                    //         );
                    //       }),
                    // );
                  },
                  online: true,
                  text: "Continue",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }
}
