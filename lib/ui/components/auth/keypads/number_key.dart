import 'package:korrency/core/core.dart';

class Number<PERSON>ey extends StatelessWidget {
  const NumberKey({
    super.key,
    required this.number,
    required this.onNumberSelected,
  });

  final int number;
  final Function(int) onNumberSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => onNumberSelected(number),
      child: Container(
        height: Sizer.height(65),
        width: Sizer.width(70),
        // padding: EdgeInsets.symmetric(
        //   horizontal: Sizer.width(27),
        //   vertical: Sizer.height(18),
        // ),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          color: AppColors.numPad,
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Text(
            number.toString(),
            style: AppTypography.text24.copyWith(
              color: AppColors.darkBlue100,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }
}
