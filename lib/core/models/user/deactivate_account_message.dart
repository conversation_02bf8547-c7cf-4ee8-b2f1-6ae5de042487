import 'dart:convert';

List<DeactivateAccountMessage> deactivateAccountMessageFromJson(String str) =>
    List<DeactivateAccountMessage>.from(
        json.decode(str).map((x) => DeactivateAccountMessage.fromJson(x)));

String deactivateAccountMessageToJson(List<DeactivateAccountMessage> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DeactivateAccountMessage {
  final String? reason;
  final String? description;

  DeactivateAccountMessage({
    this.reason,
    this.description,
  });

  factory DeactivateAccountMessage.fromJson(Map<String, dynamic> json) =>
      DeactivateAccountMessage(
        reason: json["reason"],
        description: json["description"],
      );

  Map<String, dynamic> toJson() => {
        "reason": reason,
        "description": description,
      };
}
