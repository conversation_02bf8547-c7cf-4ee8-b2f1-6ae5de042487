import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class NewBeneficiaryScreen extends StatefulWidget {
  const NewBeneficiaryScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  State<NewBeneficiaryScreen> createState() => _NewBeneficiaryScreenState();
}

class _NewBeneficiaryScreenState extends State<NewBeneficiaryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var bankVM = context.read<BankVM>();
      bankVM.getBanksByCurrencyId(widget.currency.id ?? 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BeneficiaryVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'New Beneficiary',
            onBackBtnTap: () {
              vm.clearData();
              context.read<BankVM>().resetData();
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                // const YBox(40),
                // CAD
                if (widget.currency.code == CurrencyConst.cadCurrency)
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomTextField(
                        labelText: "First Name",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        // hintText: '**********',
                        controller: vm.fNameC,
                        prefixIcon: Icon(
                          Iconsax.user,
                          color: AppColors.gray500,
                          size: Sizer.height(20),
                        ),
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Last Name",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        controller: vm.lNameC,
                        prefixIcon: Icon(
                          Iconsax.user,
                          color: AppColors.gray500,
                          size: Sizer.height(20),
                        ),
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Interac E-mail",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        errorText: vm.emailC.text.isNotEmpty && !vm.isValidEmail
                            ? "Invalid Email"
                            : null,
                        // hintText: '**********',
                        controller: vm.emailC,
                        prefixIcon: Icon(
                          Iconsax.sms,
                          color: AppColors.gray500,
                          size: Sizer.height(20),
                        ),
                        onChanged: (val) => vm.emailIsValid(),
                      ),
                    ],
                  ),
                // NGN
                if (widget.currency.code == CurrencyConst.nairaCurrency)
                  Column(
                    children: [
                      CustomTextField(
                        labelText: "Bank",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        hintText: 'Select Bank',
                        isReadOnly: true,
                        onTap: () {
                          BsWrapper.bottomSheet(
                              context: context,
                              widget: BankSheet(
                                recipientCurrencyId: widget.currency.id,
                              ));
                        },
                        suffixIcon: Icon(
                          Icons.expand_more,
                          size: Sizer.radius(25),
                          color: AppColors.gray500,
                        ),
                        controller: vm.bankNameC,
                        onChanged: (val) {},
                      ),
                      const YBox(24),
                      CustomTextField(
                        controller: vm.accountNumC,
                        labelText: "Account Number",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        hintText: '**********',
                        isReadOnly: vm.bankNameC.text.isEmpty,
                        keyboardType: KeyboardType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10),
                        ],
                        suffixIcon: vm.busy(verifyingBankState)
                            ? const CupertinoActivityIndicator()
                            : null,
                        onChanged: (val) {
                          if (val.trim().length == 10) {
                            FocusScope.of(context).unfocus();
                            vm
                                .verifyBankAcct(
                              currencyId: widget.currency.id ?? 0,
                              bankId: vm.bankUUID ?? '',
                              accountNum: vm.accountNumC.text,
                              amount: "100",
                            )
                                .then((value) {
                              if (!value.success) {
                                FlushBarToast.fLSnackBar(
                                  message: value.message ??
                                      'Unable to verify account, kindly try again',
                                );
                              }
                            });
                          }
                        },
                        onTap: () {
                          if (vm.bankNameC.text.isEmpty) {
                            FlushBarToast.fLSnackBar(
                              message: 'Please select bank before proceeding',
                            );
                          }
                        },
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Account Name",
                        isReadOnly: true,
                        fillColor: AppColors.litGrey100,
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        // hintText: 'RONE-ORUGBOH AJORITSEDERE',
                        hideBorder: true,
                        controller: vm.accountNameC,
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                    ],
                  ),
                const Spacer(),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  onTap: () async {
                    FocusScope.of(context).unfocus();
                    final result = await BsWrapper.bottomSheet(
                      context: context,
                      widget: BeneficiaryScamSheet(),
                    );
                    if (result is bool && result) {
                      _saveBeneficiary();
                    }
                  },
                  online: widget.currency.code == "CAD"
                      ? vm.cadButtonIsActive
                      : vm.ngnButtonIsActive,
                  text: "Save Beneficiary",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  _saveBeneficiary() {
    var beneficiaryVM = context.read<BeneficiaryVM>();
    PostBeneficiaryArgs arg = widget.currency.code == "CAD"
        ? PostBeneficiaryArgs(
            currencyId: widget.currency.id!,
            firstName: beneficiaryVM.fNameC.text.trim(),
            lastName: beneficiaryVM.lNameC.text.trim(),
            accountIdentifier: beneficiaryVM.emailC.text.trim(),
            transferMethod: TransferMethod.interac,
          )
        : PostBeneficiaryArgs(
            currencyId: widget.currency.id!,
            institutionName: beneficiaryVM.bankNameC.text.trim(),
            accountName: beneficiaryVM.accountNameC.text.trim(),
            accountIdentifier: beneficiaryVM.accountNumC.text.trim(),
            institutionCode: beneficiaryVM.bankUUID,
            transferMethod: TransferMethod.bankTransfer,
          );
    printty(arg.toString(), level: 'arg');
    beneficiaryVM
        .saveBeneficiary(
      arg: arg,
      transferMethodType: widget.currency.code == "CAD"
          ? TransferMethodType.interac
          : TransferMethodType.bankTransfer,
    )
        .then((value) {
      if (value.success) {
        context.read<BankVM>().resetData();
        beneficiaryVM
          ..clearData()
          ..getBeneficiaries();
        _showComfirmationScreen(msg: value.message.toString());
      } else {
        _showComfirmationScreen(
          isFailed: true,
          msg: value.message.toString(),
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Beneficiary Saved\n Failed"
                : "Beneficiary Saved\n Successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
          if (!isFailed) {
            _pop();
            _pop();
            _pop();
            Navigator.pushNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.beneficiaryScreen,
            );
          }
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
