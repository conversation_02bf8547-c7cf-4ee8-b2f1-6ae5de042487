#!/bin/bash

# Script to check 16KB page size compatibility for Flutter Android app
# This script helps verify that your app supports 16KB memory page sizes

set -e

echo "🔍 Checking 16KB Page Size Compatibility for Korrency Flutter App"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if required tools are available
echo "Checking required tools..."

if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi
print_status "Flutter found"

if ! command -v zipalign &> /dev/null; then
    print_warning "zipalign not found in PATH. Please ensure Android SDK build-tools are installed."
fi

# Clean and get dependencies
echo ""
echo "📦 Updating dependencies..."
flutter clean
flutter pub get

# Build the app for release
echo ""
echo "🔨 Building app for release..."
flutter build apk --release --flavor production -t lib/main_prod.dart

# Check if APK was built successfully
APK_PATH="build/app/outputs/flutter-apk/app-production-release.apk"
if [ ! -f "$APK_PATH" ]; then
    print_error "APK not found at $APK_PATH"
    print_error "Build may have failed. Please check the build output above."
    exit 1
fi

print_status "APK built successfully: $APK_PATH"

# Extract APK to check native libraries
echo ""
echo "🔍 Analyzing native libraries..."
TEMP_DIR=$(mktemp -d)
unzip -q "$APK_PATH" -d "$TEMP_DIR"

# Check for native libraries
if [ -d "$TEMP_DIR/lib" ]; then
    print_status "Native libraries found. Checking architectures..."
    
    for arch in arm64-v8a armeabi-v7a x86 x86_64; do
        if [ -d "$TEMP_DIR/lib/$arch" ]; then
            echo "  📁 $arch:"
            ls "$TEMP_DIR/lib/$arch" | head -5 | sed 's/^/    /'
            if [ $(ls "$TEMP_DIR/lib/$arch" | wc -l) -gt 5 ]; then
                echo "    ... and $(( $(ls "$TEMP_DIR/lib/$arch" | wc -l) - 5 )) more files"
            fi
        fi
    done
else
    print_warning "No native libraries found in APK"
fi

# Check alignment using zipalign if available
echo ""
echo "🔧 Checking APK alignment..."
if command -v zipalign &> /dev/null; then
    if zipalign -c -P 16 -v 4 "$APK_PATH" > /dev/null 2>&1; then
        print_status "APK is properly aligned for 16KB page sizes"
    else
        print_error "APK alignment check failed for 16KB page sizes"
        echo "This may indicate that native libraries are not properly aligned."
        echo "Please ensure you're using AGP 8.5.1+ and NDK r28+."
    fi
else
    print_warning "zipalign not available. Cannot verify alignment."
    echo "To check alignment manually, run:"
    echo "zipalign -c -P 16 -v 4 $APK_PATH"
fi

# Cleanup
rm -rf "$TEMP_DIR"

echo ""
echo "📋 Summary and Next Steps:"
echo "========================="
echo "1. ✅ Updated Android Gradle Plugin to 8.5.2"
echo "2. ✅ Updated NDK to version 28.0.12433566"
echo "3. ✅ Added 16KB page size support configuration"
echo "4. ✅ Updated Onfido SDK to version 22.10.0 (supports 16KB)"
echo "5. ✅ Configured packaging options for proper alignment"
echo ""
echo "🚀 Your app should now support 16KB memory page sizes!"
echo ""
echo "To test on a 16KB device or emulator:"
echo "1. Set up Android 15 emulator with 16KB page size"
echo "2. Install and test the APK: $APK_PATH"
echo "3. Verify with: adb shell getconf PAGE_SIZE (should return 16384)"
echo ""
echo "For Google Play Console compliance, upload this APK as your next release."

print_status "16KB page size compatibility check completed!"
