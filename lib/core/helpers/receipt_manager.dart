import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:korrency/core/utils/printty.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

enum ShareFormat { image, pdf }

class ReceiptShareManager {
  static Future<void> shareAsImage({
    required BuildContext receiptKeyContext,
  }) async {
    try {
      // Find the render object
      RenderRepaintBoundary boundary =
          receiptKeyContext.findRenderObject() as RenderRepaintBoundary;

      // Convert to image with a high pixel ratio
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);

      // Convert to bytes
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        throw Exception('Failed to convert image to bytes');
      }

      // Get temporary directory
      final directory = await getTemporaryDirectory();

      // Create file path
      final file = File('${directory.path}/receipt.png');

      // Write image bytes to file
      await file.writeAsBytes(byteData.buffer.asUint8List());

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Check out my receipt!',
      );
    } catch (e) {
      printty('Failed to share receipt: $e', level: 'error');
    }
  }

  static Future<void> shareAsPDF({
    required GlobalKey receiptKey,
  }) async {
    // Create PDF document
    final pdf = pw.Document();

    // Find the render object
    RenderRepaintBoundary boundary =
        receiptKey.currentContext!.findRenderObject() as RenderRepaintBoundary;

    // Convert to image with a high pixel ratio
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);

    // Convert to bytes
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData == null) {
      throw Exception('Failed to convert image to bytes');
    }

    // Add image to PDF
    pdf.addPage(
      pw.Page(
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              pw.MemoryImage(byteData.buffer.asUint8List()),
            ),
          );
        },
      ),
    );

    // Get temporary directory
    final directory = await getTemporaryDirectory();

    // Create file path
    final file = File('${directory.path}/receipt.pdf');

    // Write PDF to file
    await file.writeAsBytes(await pdf.save());

    // Share the file
    await Share.shareXFiles(
      [XFile(file.path)],
      text: 'Check out my receipt!',
    );
  }
}
