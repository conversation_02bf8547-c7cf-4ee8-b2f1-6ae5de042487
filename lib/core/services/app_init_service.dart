// ignore_for_file: use_build_context_synchronously

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/firebase_options.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInitService {
  init(Future<void> Function(RemoteMessage) fcmBackgroundHandler) async {
    //screen orientation
    await _screenOrientationInit();

    //firebase
    await _firebaseInit();

    //fcm
    FirebasePushNotificationService.init(fcmBackgroundHandler);

    // Sec Init
    await secondaryInit();
  }

  secondaryInit() async {
    printty("secondaryInit======");
    // remote config
    // await RemoteConfigService().init();

    //push notification
    LocalPushNotificationService.init();

    //package info
    // _packageInfoInit();

    //header info service
    await HeaderService().getDeviceInfo();

    // AppsflyerService
    await AppsFlyerService.instance.initialize();

    // Initialize Customer.io
    await CustomerIOService.instance.initialize();

    // Initialize Mixpanel
    await MixpanelService().init();

    // Initialize Unified Analytics Manager (includes Firebase Analytics, Facebook Analytics, and Attribution Service)
    await UnifiedAnalyticsManager.instance.initialize();
  }

  Future<String> packageInfoInit() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      await StorageService.storeStringItem(
          StorageKey.appVersion, packageInfo.version);

      printty("===> package info initialized... ${packageInfo.version}");

      return packageInfo.version;
    } catch (e) {
      printty(e.toString(), level: 'PackageInfo Error check');
      return "";
    }
  }

  _firebaseInit() async {
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      printty("===> firebase initialized...");
    } catch (e) {
      printty(e.toString(), level: 'Firebase Error check');
    }
  }

  // _crashlyticsInit() async {
  //   FlutterError.onError = (errorDetails) {
  //     FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  //   };
  //   // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  //   PlatformDispatcher.instance.onError = (error, stack) {
  //     FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
  //     return true;
  //   };
  //   printty("===> crashlytics initialized...");
  // }

  _screenOrientationInit() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    printty("===> screen orientation initialized...");
  }
}
