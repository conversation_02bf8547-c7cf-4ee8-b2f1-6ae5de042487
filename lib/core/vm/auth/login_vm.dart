import 'package:korrency/core/core.dart';

class LoginVM extends BaseVM {
  final bool _isValidEmail = false;
  bool get isValidEmail => _isValidEmail;

  bool _isTrustedDevice = false;
  bool get isTrustedDevice => _isTrustedDevice;

  bool _rememberMe = false;
  bool get rememberMe => _rememberMe;

  setRememberMe(bool value) {
    _rememberMe = value;
    reBuildUI();
  }

  Future<ApiResponse> login({
    required String email,
    required String password,
  }) async {
    printty("Login got called");
    return await performApiCall(
      url: "/auth/login",
      method: apiService.post,
      body: {
        "user_name": email,
        "password": password,
      },
      onSuccess: (data) {
        String token = data["data"]["token"];
        StorageService.storeAccessToken(token);
        _isTrustedDevice = data["data"]["is_trusted_device"];
        return ApiResponse(success: true, data: apiResponse.data);
      },
    );
  }

  Future<ApiResponse> confirmPassword(String password) async {
    return await performApiCall(
      url: "/auth/confirm-password",
      method: apiService.postWithAuth,
      body: {"password": password},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> logout() async {
    try {
      setBusy(true);
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pushNamedAndRemoveUntil(
        NavigatorKeys.appNavigatorKey.currentContext!,
        RoutePath.korrencyWelcomeScreen,
        (r) => false,
      );
      setBusy(false);
      String url = "/auth/logout";
      apiResponse = await apiService.postWithAuth(body: null, url: url);

      await StorageService.logout();

      printty(apiResponse, level: "Logout Response");
      return apiResponse;
    } catch (e) {
      printty(e.toString(), level: "Logout Error");
      setBusy(false);
      return ApiResponse(success: false, message: e.toString());
    }
  }
}
