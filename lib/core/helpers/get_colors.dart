import 'package:korrency/core/core.dart';

Color getTextColor(String status) {
  final normalizedStatus = status.toLowerCase();
  switch (normalizedStatus) {
    case "successful":
      return AppColors.iconGreen;
    case "pending":
      return AppColors.red34;
    case "sent":
      return AppColors.sent;
    case "reversed":
      return AppColors.blueCD3;
    default:
      return AppColors.failed;
  }
}
