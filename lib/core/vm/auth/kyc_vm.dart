import 'dart:convert';

import 'package:korrency/core/core.dart';
import 'package:onfido_sdk/onfido_sdk.dart';

const String occupationUpdate = "occupationUpdate";

class KycVM extends BaseVM {
  OccupationData? _selectedOccupation;
  OccupationData? get selectedOccupation => _selectedOccupation;

  OnfidoData? _onfidoData;
  OnfidoData? get onfidoData => _onfidoData;

  Future<ApiResponse> updateProfile({
    required int occupationId,
    required String address,
    required String state,
    required String city,
    required String postalCode,
    int? kycStep,
    String? gender,
  }) async {
    final payload = {
      "occupation_id": occupationId,
      "gender": gender,
      "address": address,
      "state": state,
      "city": city,
      "postal_code": postalCode,
      "kyc_step": kycStep,
    };
    payload.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      body: payload,
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        var user = authUserFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: user,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> updateOnfidoUser() async {
    return await performApiCall(
      url: "/verifications/onfido/update-user",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        printty(data, level: "onfido/update-user");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> generateOnfidoToken() async {
    return await performApiCall(
      url: "/verifications/onfido/generate-sdk-token",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        _onfidoData = onfidoDataFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<bool> startOnfidoWorkflow(
      {required String sdkToken, required String workFlowRunId}) async {
    try {
      final Onfido onfido = Onfido(
        sdkToken: sdkToken,
      );
      await onfido.startWorkflow(workFlowRunId);
      printty('Onfido Success from VM');
      return Future.value(true);
    } catch (error) {
      printty('Onfido Error: $error');
      return Future.value(false);
    }
  }

  Future<ApiResponse> updateUserOccupation(int id) async {
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      busyObjectName: occupationUpdate,
      body: {
        "occupation_id": id,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  List<OccupationData> _occupationData = [];
  List<OccupationData> get occupationData => _occupationData;
  Future<ApiResponse> getOccupationData() async {
    return await performApiCall(
      url: "/auth/profile/occupations",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _occupationData = occupationDataFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}
