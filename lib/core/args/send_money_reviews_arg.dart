import 'package:korrency/core/core.dart';

class SendMoneyReviewsArg {
  final bool isKorrencyUser;
  final String name;
  final String title;
  final String subTitle;
  final String iconPath;
  //Only for Send again approach for now
  final SendMoneyParams? transferAgainParams;

  SendMoneyReviewsArg({
    this.isKorrencyUser = false,
    required this.name,
    required this.title,
    required this.subTitle,
    required this.iconPath,
    this.transferAgainParams,
  });
}
