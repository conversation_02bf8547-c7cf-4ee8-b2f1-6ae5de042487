import 'dart:convert';

List<CurrencyRates> currencyRatesFromJson(String str) =>
    List<CurrencyRates>.from(
        json.decode(str).map((x) => CurrencyRates.fromJson(x)));

class CurrencyRates {
  CurrencyRates({
    required this.id,
    required this.name,
    required this.code,
    required this.country,
    required this.category,
    required this.symbol,
    required this.flag,
    required this.isCreatable,
    required this.isFundable,
    required this.rates,
  });

  final int? id;
  final String? name;
  final String? code;
  final String? country;
  final String? category;
  final String? symbol;
  final String? flag;
  final bool? isCreatable;
  final bool? isFundable;
  final List<KRate> rates;

  factory CurrencyRates.fromJson(Map<String, dynamic> json) {
    return CurrencyRates(
      id: json["id"],
      name: json["name"],
      code: json["code"],
      country: json["country"],
      category: json["category"],
      symbol: json["symbol"],
      flag: json["flag"],
      isCreatable: json["is_creatable"],
      isFundable: json["is_fundable"],
      rates: json["rates"] == null
          ? []
          : List<KRate>.from(json["rates"]!.map((x) => KRate.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "country": country,
        "category": category,
        "symbol": symbol,
        "flag": flag,
        "is_creatable": isCreatable,
        "is_fundable": isFundable,
        "rates": rates.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$id, $name, $code, $country, $category, $symbol, $flag, $rates, ";
  }
}

class KRate {
  KRate({
    required this.currencyId,
    required this.to,
    required this.flag,
    required this.rate,
  });

  final int? currencyId;
  final String? to;
  final String? flag;
  final String? rate;

  factory KRate.fromJson(Map<String, dynamic> json) {
    return KRate(
      currencyId: json["currency_id"],
      to: json["to"],
      flag: json["flag"],
      rate: json["rate"],
    );
  }

  Map<String, dynamic> toJson() => {
        "currency_id": currencyId,
        "to": to,
        "flag": flag,
        "rate": rate,
      };

  @override
  String toString() {
    return "$currencyId, $to, $rate, $flag ";
  }
}
