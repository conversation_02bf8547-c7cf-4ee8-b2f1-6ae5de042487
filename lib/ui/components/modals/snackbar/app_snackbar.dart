import 'package:flutter/scheduler.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AppSnackbar {
  static void show(
    BuildContext context, {
    Color? backgroundColor,
    EdgeInsetsGeometry? padding,
    SnackType? contentType,
    String? title,
    String? message,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: SnackContent(
          contentType: contentType ?? SnackType.failure,
          title: title ?? "Error",
          message: message ?? "An error occurred",
        ),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            // Some code to undo the change.
          },
        ),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 220,
          left: Sizer.width(10),
          right: Sizer.width(10),
        ),
        dismissDirection: DismissDirection.up,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        animation: _createAnimation(context),
        elevation: 0,
      ),
    );
  }

  static Animation<double> _createAnimation(BuildContext context) {
    return Tween<double>(
      begin: 0,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: AnimationController(
          vsync: _SnackBarAnimationVsync(),
          duration: const Duration(milliseconds: 1000),
        )..forward(),
        curve: Curves.easeInOut,
      ),
    );
  }
}

class _SnackBarAnimationVsync extends TickerProvider {
  @override
  Ticker createTicker(TickerCallback onTick) => Ticker(onTick);
}
