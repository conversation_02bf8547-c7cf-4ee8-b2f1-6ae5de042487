import 'dart:convert';

List<UserNotification> userNotificationFromJson(String str) =>
    List<UserNotification>.from(
        json.decode(str).map((x) => UserNotification.fromJson(x)));

String userNotificationToJson(List<UserNotification> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class UserNotification {
  int? id;
  String? subject;
  String? message;
  dynamic readAt;
  DateTime? createdAt;

  UserNotification({
    this.id,
    this.subject,
    this.message,
    this.readAt,
    this.createdAt,
  });

  factory UserNotification.fromJson(Map<String, dynamic> json) =>
      UserNotification(
        id: json["id"],
        subject: json["subject"],
        message: json["message"],
        readAt: json["read_at"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]).toLocal(),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "subject": subject,
        "message": message,
        "read_at": readAt,
        "created_at": createdAt?.toIso8601String(),
      };

  @override
  String toString() {
    return 'UserNotification(id: $id, subject: $subject, message: $message, readAt: $readAt, createdAt: $createdAt)';
  }
}
