import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ChangeConfirmationScreen extends StatelessWidget {
  const ChangeConfirmationScreen({super.key, required this.arg});

  final ChangeConfirmationArg arg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(36)),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                arg.isSuccess ? AppSvgs.emailSuccess : AppSvgs.emailError,
                height: Sizer.height(292),
              ),
              YBox(36),
              Text(
                arg.title,
                style: FontTypography.text26.semiBold,
              ),
              YBox(4),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${arg.subtitle}  ',
                      style: FontTypography.text16.semiBold.withCustomColor(
                        AppColors.gray79,
                      ),
                    ),
                    TextSpan(
                      text: '\n${arg.desc}',
                      style: FontTypography.text16.copyWith(
                        color: AppColors.grayAB,
                      ),
                    ),
                  ],
                ),
              ),
              YBox(40),
              CustomBtn.solid(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                text: "Back to Settings",
                onTap: () {
                  // Navigator.pop(context);
                  // Navigator.pop(context);
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    RoutePath.dashboardNav,
                    (route) => false,
                    arguments: 4,
                  );
                  Navigator.pushNamed(
                    context,
                    RoutePath.securityScreen,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
