import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class KorrencyUserPermissionModal extends StatelessWidget {
  const KorrencyUserPermissionModal({super.key});

  @override
  Widget build(BuildContext context) {
    final phoneVm = context.watch<PhoneBookVM>();
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(30),
                SvgPicture.asset(
                  AppSvgs.korrencyUser,
                  height: Sizer.height(138),
                ),
                const YBox(30),
                Text(
                  'Allow Contact',
                  style: AppTypography.text22.semiBold,
                ),
                Y<PERSON>ox(4),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            'Allow Korrency to access your Contacts, this allows us to import korrency users to your list',
                        style: FontTypography.text16.withCustomColor(
                          AppColors.gray93,
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(50),
                CustomBtn.solid(
                  onTap: () async {
                    final res =
                        await context.read<PhoneBookVM>().fetchContacts();
                    if (res == true) {
                      Navigator.pop(context, true);
                      return;
                    }

                    showWarningToast('Failed to fetch contacts');
                  },
                  isLoading: phoneVm.busy(fetchContactsState),
                  online: true,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  text: "Enable Permission",
                ),
                const YBox(30),
                InkWell(
                  onTap: () {
                    Navigator.pop(context, false);
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(4), horizontal: Sizer.width(12)),
                    child: Text(
                      "Do this Later",
                      style: AppTypography.text16.medium.copyWith(
                        color: AppColors.primaryBlue90,
                      ),
                    ),
                  ),
                ),
                const YBox(44),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
