import 'package:korrency/core/core.dart';

class EmailPhoneChangeVM extends BaseVM {
  Future<ApiResponse> changeInfoReq(ChangeInfoParams arg) async {
    printty(arg.toJson(), level: "changeInfoReq");
    final body = arg.toJson();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/profile/change-info-request",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> changeInfoVerification(ChangeInfoParams arg) async {
    printty(arg.toJson(), level: "changeInfoVerification");
    final body = arg.toJson();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/profile/change-info-verification",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}
