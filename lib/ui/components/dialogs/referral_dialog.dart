// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReferralDialog extends StatefulWidget {
  const ReferralDialog({super.key});

  @override
  State<ReferralDialog> createState() => _ReferralDialogState();
}

class _ReferralDialogState extends State<ReferralDialog> {
  final passwordC = TextEditingController();
  final focusNode = FocusNode();

  @override
  dispose() {
    passwordC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          decoration: BoxDecoration(
            color: AppColors.bgWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          child: <PERSON>umn(
            mainAxisSize: MainAxisSize.min,
            children: [
              const YBox(28),
              SvgPicture.asset(
                AppSvgs.referrals,
                height: Sizer.height(160),
              ),
              const YBox(20),
              Text(
                'Have a Referral Code?',
                style: FontTypography.text22.semiBold,
              ),
              YBox(4),
              Text(
                'Enter the code of the person who referred you to Korrency and earn rewards',
                textAlign: TextAlign.center,
                style: FontTypography.text16.withCustomColor(
                  AppColors.gray93,
                ),
              ),
              const YBox(40),
              Row(
                children: [
                  Expanded(
                    child: CustomBtn.solid(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      online: true,
                      isOutline: true,
                      textColor: AppColors.primaryBlue,
                      borderRadius: BorderRadius.circular(Sizer.radius(14)),
                      height: Sizer.height(44),
                      textStyle: FontTypography.text15.medium
                          .withCustomColor(AppColors.primaryBlue),
                      text: "No",
                    ),
                  ),
                  const XBox(16),
                  Expanded(
                    child: CustomBtn.solid(
                      onTap: () {
                        Navigator.pop(context, true);
                      },
                      online: true,
                      height: Sizer.height(44),
                      borderRadius: BorderRadius.circular(Sizer.radius(14)),
                      textStyle: FontTypography.text15.medium.withCustomColor(
                        AppColors.white,
                      ),
                      text: "Yes",
                    ),
                  ),
                ],
              ),
              const YBox(30),
            ],
          ),
        ),
      );
    });
  }
}
