import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/custom_list_tile/wallet_list_tile.dart';

class DottedWalletListTile extends StatelessWidget {
  const DottedWalletListTile({
    Key? key,
    required this.title,
    required this.subTitle,
    required this.currencyIcon,
    this.onTap,
  }) : super(key: key);

  final String title;
  final String subTitle;
  final String currencyIcon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: DottedBorder(
        dashPattern: const [8, 5],
        strokeWidth: 2,
        borderType: BorderType.RRect,
        radius: const Radius.circular(4),
        color: AppColors.dottedColor,
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(14),
        ),
        child: WalletListTile(
          title: title,
          subTitle: subTitle,
          useNetworkSvg: true,
          currencyIcon: currencyIcon,
          icon: Icons.check_circle,
          showTrailing: true,
          trailingWidget: Container(
            width: Sizer.width(32),
            height: Sizer.height(32),
            decoration: BoxDecoration(
              color: AppColors.gray500,
              borderRadius: BorderRadius.circular(30),
            ),
            child: const Icon(
              Icons.add,
              color: AppColors.white,
            ),
          ),
        ),
      ),
    );
  }
}
