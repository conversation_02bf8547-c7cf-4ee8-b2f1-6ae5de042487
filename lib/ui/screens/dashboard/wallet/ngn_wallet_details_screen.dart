import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NgnWalletDetailsScreen extends StatefulWidget {
  const NgnWalletDetailsScreen({super.key, required this.wallet});

  final Wallet wallet;

  @override
  State<NgnWalletDetailsScreen> createState() => _NgnWalletDetailsScreenState();
}

class _NgnWalletDetailsScreenState extends State<NgnWalletDetailsScreen> {
  bool _hideAccountDetails = false;
  bool _hideWalletAmount = false;
  bool _hideAccountLimits = true;

  Wallet? walletDetails;

  @override
  void initState() {
    super.initState();
    _loadWalletMaskingState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      walletDetails = widget.wallet;
      _fetchSetup();
    });
  }

  Future<void> _fetchSetup() async {
    final currencyId = widget.wallet.currency?.id;
    context.read<CurrencyVM>().getCurrenciesLimits();
    context.read<BeneficiaryVM>().getWalletFreqBeneficiaries(
          currencyId: currencyId ?? 0,
        );
    context.read<TransactionVM>().getFilteredTransactions(
          isAllTransaction: false,
          currencyId: currencyId,
        );
  }

  // For pull to refresh
  Future<void> _refreshInit() async {
    final walletVm = context.read<WalletVM>();
    final currencyCode = widget.wallet.currency?.code;
    final res = await walletVm.getWallets();
    if (res.success && res.data is List<Wallet>) {
      setState(() {
        final wallets = res.data as List<Wallet>;
        walletDetails = wallets.firstWhereOrNull(
          (element) => element.currency?.code == currencyCode,
        );
      });
    }
  }

  Future<void> _loadWalletMaskingState() async {
    final isMasked = await WalletMaskingService.getWalletMaskingState('NGN');
    if (mounted) {
      setState(() {
        _hideWalletAmount = isMasked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final walletVm = context.watch<WalletVM>();
    final currencyVm = context.watch<CurrencyVM>();
    final beneficiaryVm = context.watch<BeneficiaryVM>();
    return BusyOverlay(
      show: currencyVm.isBusy,
      child: Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Wallet Details',
          textSize: Sizer.text(16),
          onBackBtnTap: () {
            Navigator.pop(context);
          },
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await _refreshInit();
            await _fetchSetup();
          },
          child: ListView(
            padding: EdgeInsets.only(
              top: Sizer.height(24),
              bottom: Sizer.height(80),
            ),
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Align(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(100),
                      child: SizedBox(
                        width: Sizer.width(48),
                        height: Sizer.height(48),
                        child: SvgPicture.asset(AppSvgs.nigeria),
                      ),
                    ),
                  ),
                  YBox(12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Nigerian Naira",
                        style: AppTypography.text17.medium
                            .copyWith(color: AppColors.mainBlack),
                      ),
                      XBox(8),
                      InkWell(
                        onTap: () async {
                          final res = await BsWrapper.bottomSheet(
                            context: context,
                            widget: SelectWalletModal(),
                          );

                          if (res is Wallet) {
                            // Check if its same wallet
                            if (res.id == walletDetails?.id) {
                              return;
                            }
                            if (context.mounted) {
                              Navigator.pop(context);
                              navigateToWalletScreen(
                                context: context,
                                currencyCode: res.currency?.code ?? '',
                                wallet: res,
                              );
                            }
                          }
                        },
                        child: SvgPicture.asset(
                          AppSvgs.arrowDown,
                          width: Sizer.width(24),
                          height: Sizer.height(24),
                        ),
                      ),
                    ],
                  ),
                  YBox(4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _hideWalletAmount
                          ? Text(
                              "*** *** ***",
                              style: AppTypography.text32.semiBold.copyWith(
                                color: AppColors.mainBlack,
                                fontFamily: AppFont.outfit.family,
                              ),
                            )
                          : RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: walletDetails?.currency?.symbol ?? "",
                                    style:
                                        AppTypography.text32.semiBold.copyWith(
                                      color: AppColors.mainBlack,
                                      fontFamily: AppFont.inter.family,
                                    ),
                                  ),
                                  TextSpan(
                                    text: walletVm.homeBalance(walletDetails),
                                    style:
                                        AppTypography.text32.semiBold.copyWith(
                                      color: AppColors.mainBlack,
                                      fontFamily: AppFont.outfit.family,
                                    ),
                                  ),
                                  TextSpan(
                                    text: ".",
                                    style: AppTypography.text32.medium.copyWith(
                                      color: AppColors.mainBlack,
                                      fontFamily: AppFont.outfit.family,
                                    ),
                                  ),
                                  TextSpan(
                                    text:
                                        walletVm.decimalBalance(walletDetails),
                                    style:
                                        AppTypography.text20.semiBold.copyWith(
                                      color: AppColors.mainBlack,
                                      fontFamily: AppFont.outfit.family,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                      XBox(10),
                      InkWell(
                        onTap: () async {
                          final newState = await WalletMaskingService
                              .toggleWalletMaskingState('NGN');
                          if (mounted) {
                            setState(() {
                              _hideWalletAmount = newState;
                            });
                          }
                        },
                        child: SvgPicture.asset(
                          _hideWalletAmount
                              ? AppSvgs.walletEyeSlash
                              : AppSvgs.walletEye,
                        ),
                      ),
                    ],
                  ),
                  YBox(30),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      WalletColTab(
                        text: "Add",
                        iconPath: AppSvgs.request,
                        onTap: () {
                          // Navigator.pushNamed(
                          //     context, RoutePath.accountDetailsScreen);
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: FundYourWalletModal(wallet: widget.wallet),
                          );
                        },
                      ),
                      XBox(26),
                      WalletColTab(
                        text: "Convert",
                        iconPath: AppSvgs.convert,
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.convertCurrencyScreen);
                        },
                      ),
                      XBox(26),
                      WalletColTab(
                        text: "Send",
                        iconPath: AppSvgs.walletSend,
                        onTap: () {
                          final authVm = context.read<AuthUserVM>();

                          if (authVm.user?.hasCreditTransaction == false) {
                            showWarningToast("Fund your wallet to send money");
                            return null;
                          }

                          final int fromCurrencyId =
                              walletDetails?.currency?.id ?? 0;

                          int? toCurrencyId =
                              authVm.user?.frequentDestinationCurrency?.id;

                          // if from and to currency is same
                          // then use user country currency as default
                          if (fromCurrencyId == toCurrencyId) {
                            toCurrencyId = authVm.countryCurrency?.id;
                          }
                          Navigator.pushNamed(
                            context,
                            RoutePath.sendMoneyScreen,
                            arguments: SendMoneyArg(
                              fromCurrencyId: fromCurrencyId,
                              toCurrencyId: toCurrencyId ?? 0,
                              fromWallet: walletDetails,
                            ),
                          );
                        },
                      ),
                      XBox(26),
                      WalletColTab(
                        text: "More",
                        iconPath: AppSvgs.more,
                        onTap: () {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: WalletMoreOptionsModal(
                              wallet: walletDetails ?? Wallet(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  YBox(40),
                  HeaderHideToggle(
                    header: "Account details",
                    hidden: _hideAccountDetails,
                    onTap: () {
                      _hideAccountDetails = !_hideAccountDetails;
                      setState(() {});
                    },
                  ),
                  LoadableContentBuilder(
                      isBusy: walletVm.isBusy,
                      isError: false,
                      items: walletDetails?.virtualAccounts ?? [],
                      loadingBuilder: (ctx) {
                        return SizedBox(
                          height: Sizer.height(200),
                          child: const Center(
                            child: CupertinoActivityIndicator(),
                          ),
                        );
                      },
                      emptyBuilder: (ctx) {
                        return Center(
                          child: EmptyState(
                            title: "No virtual account found",
                          ),
                        );
                      },
                      contentBuilder: (ctx) {
                        return AnimatedSize(
                          duration: const Duration(milliseconds: 500),
                          curve: Curves.easeInOut,
                          child: _hideAccountDetails
                              ? SizedBox.shrink()
                              : Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: Sizer.width(24),
                                  ),
                                  child: NgnAccountDetailsWidget(),
                                ),
                        );
                      }),
                  YBox(10),
                  HeaderHideToggle(
                    header: "Account Limits",
                    hidden: _hideAccountLimits,
                    onTap: () {
                      _hideAccountLimits = !_hideAccountLimits;
                      setState(() {});
                    },
                  ),
                  AnimatedSize(
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                    child: _hideAccountLimits
                        ? SizedBox.shrink()
                        : Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(16),
                              vertical: Sizer.height(20),
                            ),
                            margin: EdgeInsets.symmetric(
                              horizontal: Sizer.width(24),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.blueBFF,
                              borderRadius:
                                  BorderRadius.circular(Sizer.radius(16)),
                            ),
                            child: Column(
                              children: buildAccountLimitCards(
                                limits: currencyVm.getCurrencyLimit(
                                    walletDetails?.currency?.code ?? ''),
                                currency: currencyVm.getCurrencyByCode(
                                    walletDetails?.currency?.code ?? ''),
                              ),
                            ),
                          ),
                  ),
                  YBox(28),
                  Consumer<TransactionVM>(
                      builder: (context, transactionVm, child) {
                    final transactionList =
                        transactionVm.transactionByCurrencyId.take(3).toList();
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        HeaderHideToggle(
                          header: "Transactions",
                          trailingText: "View all",
                          onTap: transactionList.isNotEmpty
                              ? () {
                                  Navigator.pushNamed(
                                      context, RoutePath.dashboardNav,
                                      arguments: 1);
                                }
                              : null,
                        ),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          itemCount: transactionList.length,
                          separatorBuilder: (_, __) =>
                              HDivider(verticalPadding: 16),
                          itemBuilder: (ctx, i) {
                            return TransactionListTile(
                              title: transactionList[i].title ?? "N/A",
                              subTitle: transactionList[i].subtitle ?? "N/A",
                              amount:
                                  "${AppUtils.formatAmountDoubleString(transactionList[i].amount ?? "0")} ${transactionList[i].currency?.code ?? ""}",
                              status: transactionList[i].status ?? '',
                              category: transactionList[i].category ?? '',
                              onTap: () {
                                Navigator.of(context).pushNamed(
                                    RoutePath.transactionDetailsScreen,
                                    arguments: TransactionArg(
                                        transaction: transactionList[i]));
                              },
                            );
                          },
                        ),
                      ],
                    );
                  }),
                  YBox(28),
                  HeaderHideToggle(
                    header: "Send again",
                  ),
                  LoadableContentBuilder(
                    isBusy: beneficiaryVm.isBusy,
                    items: beneficiaryVm.walletFreqBeneficiaries,
                    loadingBuilder: (ctx) {
                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: List.generate(
                            5,
                            (i) => Padding(
                              padding: EdgeInsets.only(
                                left: i == 0 ? Sizer.width(24) : 0,
                                right: Sizer.width(12),
                              ),
                              child: Skeletonizer(
                                enabled: true,
                                child: QuickSendWidget(
                                  beneficiaryName: "Beneficiary Name",
                                  currencyFlag: AppUtils.kCadFlag,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                    emptyBuilder: (ctx) {
                      return Center(
                        child: EmptyState(
                          title: "No beneficiary found",
                        ),
                      );
                    },
                    contentBuilder: (context) {
                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: List.generate(
                              beneficiaryVm.walletFreqBeneficiaries.length,
                              (i) {
                            final beneficiary =
                                beneficiaryVm.walletFreqBeneficiaries[i];
                            return Padding(
                              padding: EdgeInsets.only(
                                left: i == 0 ? Sizer.width(24) : 0,
                                right: Sizer.width(12),
                              ),
                              child: QuickSendWidget(
                                beneficiaryName: beneficiary.accountName ?? "",
                                currencyFlag: beneficiary.currency?.flag ?? "",
                                onTap: () {
                                  final fromCurrencyId = beneficiary
                                          .latestTransaction?.currency?.id ??
                                      0;
                                  final toCurrencyId = beneficiary
                                          .latestTransaction
                                          ?.receivedCurrency
                                          ?.id ??
                                      0;
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.sendMoneyScreen,
                                    arguments: SendMoneyArg(
                                      fromCurrencyId: fromCurrencyId,
                                      toCurrencyId: toCurrencyId,
                                      fromAmount: beneficiary
                                              .latestTransaction?.amount ??
                                          "",
                                      beneficiary: beneficiary,
                                    ),
                                  );
                                },
                              ),
                            );
                          }),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
