import 'package:korrency/core/core.dart';

class ModalCloseBtn extends StatelessWidget {
  const ModalCloseBtn({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap ??
          () {
            Navigator.pop(context);
          },
      child: Container(
        padding: EdgeInsets.all(Sizer.width(10)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.white.withOpacity(0.15),
          border: Border.all(
            color: AppColors.white.withOpacity(0.70),
            width: 1,
          ),
        ),
        child: Icon(
          Icons.close,
          size: Sizer.width(16),
          color: AppColors.white,
        ),
      ),
    );
  }
}
