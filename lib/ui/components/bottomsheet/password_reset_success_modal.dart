import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class PasswordResetSuccessModal extends StatelessWidget {
  const PasswordResetSuccessModal({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(30)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(28),
                SvgPicture.asset(
                  AppSvgs.confirmPhone,
                  height: Sizer.height(134),
                ),
                const YBox(16),
                Text(
                  'Password Reset Successful!',
                  style: FontTypography.text22.semiBold,
                ),
                YBox(4),
                Text(
                  'Your password has been updated, you can now log in with your new credentials',
                  textAlign: TextAlign.center,
                  style: FontTypography.text16.withCustomColor(
                    AppColors.gray93,
                  ),
                ),
                const YBox(37),
                CustomBtn.solid(
                  onTap: () {
                    Navigator.pushNamedAndRemoveUntil(
                        context, RoutePath.loginScreen, (route) => false);
                  },
                  borderRadius: BorderRadius.circular(Sizer.radius(14)),
                  text: "Login",
                ),
                const YBox(30),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
