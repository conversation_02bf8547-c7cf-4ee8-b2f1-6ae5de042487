// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ChangeTransactionPinScreen extends StatefulWidget {
  const ChangeTransactionPinScreen({
    super.key,
  });

  @override
  State<ChangeTransactionPinScreen> createState() =>
      _ChangeTransactionPinScreenState();
}

class _ChangeTransactionPinScreenState
    extends State<ChangeTransactionPinScreen> {
  final pinC = TextEditingController();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TransactionPinVM>().requestOtpTransactionPin();
    });
  }

  @override
  void dispose() {
    pinC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authVm = context.watch<AuthUserVM>();
    final pinVm = context.watch<TransactionPinVM>();
    return BusyOverlay(
      show: pinVm.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.white,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Change Transaction Pin',
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                              text:
                                  "To change your Transaction PIN, please enter the 6-digit code sent to",
                              style: FontTypography.text16
                                  .withCustomColor(AppColors.gray93)
                                  .withCustomHeight(1.6)),
                          TextSpan(
                              text:
                                  "\n${AppUtils.maskEmail(authVm.user?.email ?? 'N/A')}",
                              style: FontTypography.text16
                                  .withCustomColor(AppColors.primaryBlue)
                                  .withCustomHeight(1.5)),
                        ],
                      ),
                    ),
                    const YBox(46),
                    Center(
                      child: Pinput(
                        defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                        followingPinTheme: PinInputTheme.changePinTheme(),
                        focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                        submittedPinTheme: PinInputTheme.changePinTheme(),
                        length: 6,
                        controller: pinC,
                        showCursor: true,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        keyboardType: TextInputType.number,
                        onChanged: (value) => authVm.reBuildUI(),
                        onCompleted: (pin) {
                          FocusScope.of(context).unfocus();
                          _verifyOtp();
                        },
                      ),
                    ),
                    const YBox(24),
                    ResendCode(
                      onResendCode: () async {
                        final res = await context
                            .read<TransactionPinVM>()
                            .requestOtpTransactionPin();
                        handleApiResponse(response: res);
                      },
                    ),
                  ],
                ),
              ),
              CustomBtn.withChild(
                online: pinC.text.length == 6,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                onTap: _verifyOtp,
                child: ContinueText(isOnline: pinC.text.length == 6),
              ),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }

  void _verifyOtp() async {
    FocusScope.of(context).unfocus();
    final pinVm = context.read<TransactionPinVM>();

    final result = await pinVm.verifyOtpTransactionPin(
      pinC.text.trim(),
    );

    handleApiResponse(
      response: result,
      onSuccess: () {
        Navigator.pushReplacementNamed(context, RoutePath.setupYourPinScreen);
      },
    );
  }
}
