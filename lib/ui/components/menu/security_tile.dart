import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SecurityTile extends StatelessWidget {
  const SecurityTile({
    Key? key,
    this.leadingIcon,
    required this.title,
    this.subTitle,
    this.subTitleTwo,
    this.trailingWidget,
    this.leadingWidget,
    this.showTrailing = false,
    this.isSelected = false,
    this.subTitleColor,
    this.bgColor,
    this.leadIconColor,
    this.trailIconColor,
    this.onTap,
    this.onTrailingPress,
    this.padding,
  }) : super(key: key);

  final String? leadingIcon;
  final String title;
  final String? subTitle;
  final String? subTitleTwo;
  final Widget? trailingWidget;
  final Widget? leadingWidget;
  final bool showTrailing;
  final bool isSelected;
  final Color? subTitleColor;
  final Color? bgColor;
  final Color? leadIconColor;
  final Color? trailIconColor;
  final VoidCallback? onTap;
  final VoidCallback? onTrailingPress;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ContainerWithBluewishBg(
        padding: padding,
        bgColor: trailIconColor ?? bgColor,
        child: Row(
          children: [
            Skeleton.replace(
              replacement: const Bone.square(
                size: 40,
              ),
              child: leadingIcon == null
                  ? Container(
                      child: leadingWidget,
                    )
                  : svgHelper(
                      leadingIcon!,
                      color: leadIconColor,
                    ),
            ),
            const XBox(4),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text14.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.textBlack900,
                    ),
                  ),
                  if (subTitle != null) const YBox(4),
                  if (subTitle != null)
                    Text(
                      subTitle!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: AppTypography.text12.copyWith(
                        color: subTitleColor ?? AppColors.textBlack900,
                      ),
                    ),
                  if (subTitleTwo != null) const YBox(4),
                  if (subTitleTwo != null)
                    Text(
                      subTitleTwo!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: AppTypography.text12.copyWith(
                        color: subTitleColor ?? AppColors.textBlack900,
                      ),
                    ),
                ],
              ),
            ),
            if (showTrailing)
              Skeleton.replace(
                replacement: const Bone.icon(
                  size: 30,
                ),
                child: InkWell(
                  onTap: onTrailingPress,
                  child: _buildTrailingWidget(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrailingWidget() {
    if (trailingWidget != null) {
      return trailingWidget!;
    } else if (isSelected) {
      return const Icon(
        Icons.check_circle,
        size: 24,
        color: AppColors.blue800,
      );
    } else {
      return Container(
        width: Sizer.width(20),
        height: Sizer.height(20),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: AppColors.gray0000,
            width: 2,
          ),
        ),
      );
    }
  }
}
