// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/confirmation.dart';
import 'package:pinput/pinput.dart';

class TransactionPinDialog extends StatefulWidget {
  const TransactionPinDialog({
    super.key,
    this.params,
  });

  final SendMoneyParams? params;

  @override
  State<TransactionPinDialog> createState() => _TransactionPinDialogState();
}

class _TransactionPinDialogState extends State<TransactionPinDialog> {
  final _pinC = TextEditingController();
  final _pinF = FocusNode();

  @override
  initState() {
    super.initState();
    _pinF.requestFocus();
  }

  @override
  void dispose() {
    _pinC.dispose();
    _pinF.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          decoration: BoxDecoration(
            color: AppColors.bgWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              YBox(20),
              Text(
                "Please Input your Pin",
                style: AppTypography.text22.semiBold,
              ),
              YBox(4),
              Text(
                "Please input the pin to authenticate this \ntransaction",
                textAlign: TextAlign.center,
                style: AppTypography.text16.copyWith(
                  color: AppColors.gray93,
                ),
              ),
              YBox(40),
              Center(
                child: Pinput(
                  defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                  followingPinTheme: PinInputTheme.changePinTheme(),
                  focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                  submittedPinTheme: PinInputTheme.changePinTheme(),
                  length: 4,
                  focusNode: _pinF,
                  controller: _pinC,
                  showCursor: true,
                  obscureText: true,
                  // obscuringCharacter: "*",
                  obscuringWidget: Padding(
                    padding: EdgeInsets.only(
                      top: Sizer.height(10),
                    ),
                    child: Text(
                      "*",
                      style: AppTypography.text34.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  onChanged: (value) => setState(() {}),
                  onCompleted: (pin) {
                    FocusScope.of(context).unfocus();
                    if (widget.params != null) {
                      _transferAgain(pin);
                    } else {
                      _transfer(pin);
                    }
                  },
                ),
              ),
              YBox(60),
              CustomBtn.solid(
                onTap: () async {
                  FocusScope.of(context).unfocus();
                  await _transfer(_pinC.text);
                },
                isLoading: context.watch<SendMoneyVM>().isBusy,
                online: _pinC.length > 3,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                text: "Confirm",
              ),
              const YBox(30),
            ],
          ),
        ),
      );
    });
  }

  _transferAgain(String pin) {
    final sendMoneyVM = context.read<SendMoneyVM>();
    final transactionVm = context.read<TransactionVM>();
    final bankVM = context.read<BankVM>();
    final user = context.read<AuthUserVM>().user;

    sendMoneyVM
        .transferAgain(SendMoneyParams(
      sourceCurrencyId: widget.params?.sourceCurrencyId ?? 0,
      targetCurrencyId: widget.params?.targetCurrencyId ?? 0,
      amount: widget.params?.amount ?? "0",
      pin: pin,
      rate: widget.params?.rate ?? "0",
      transactionPurposeId: widget.params?.transactionPurposeId,
      destinationBankUuid: widget.params?.destinationBankUuid,
      destinationBankAccountNumber: widget.params?.destinationBankAccountNumber,
      accountName: widget.params?.accountName,
      transferMethod: widget.params?.transferMethod,
      bankName: widget.params?.bankName,
      korrencyUsername: widget.params?.korrencyUsername,
      interacEmail: widget.params?.interacEmail,
      interacFirstName: widget.params?.interacFirstName,
      interacLastName: widget.params?.interacLastName,
      // Since you can only send again to a saved beneficiar
      saveBeneficiary: 1,
    ))
        .then((value) async {
      if (value.success) {
        // Track successful money transfer
        try {
          // final data = value.data['data'];

          await UnifiedAnalyticsManager.instance.trackSendMoney(
            userId: user?.id ?? 'unknown',
            amount:
                double.tryParse(sendMoneyVM.fromC.text.replaceAllCommas()) ??
                    0.0,
            fromCurrency:
                sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD',
            toCurrency: sendMoneyVM.recipientCurrency?.code ?? 'NGN',
            corridor:
                '${sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD'}-${sendMoneyVM.recipientCurrency?.code ?? 'NGN'}',
            // transactionId: data?['transaction_id']?.toString(),
            additionalParameters: {
              'transfer_method': sendMoneyVM.transferMethod?.name ?? 'unknown',
              'recipient_name':
                  sendMoneyVM.transferParams?.accountName ?? 'unknown',
              'transaction_timestamp': DateTime.now().toIso8601String(),
              'exchange_rate':
                  sendMoneyVM.conversionRate?.rate?.rate?.toString(),
              'recipient_amount': sendMoneyVM.recipientGetAmount,
              'transfer_fee': sendMoneyVM.transferFee,
            },
          );
        } catch (e) {
          printty('❌ Error tracking send money: $e');
        }

        final data = value.data['data'];
        String? securityAnswer;
        if (data != null && data is Map<String, dynamic>) {
          securityAnswer = data['security_answer'];
        }
        Navigator.pop(context, {
          'success': true,
          'securityAnswer': securityAnswer,
        });
        // _showSuccessConfirmationScreen(securityAnswer);
      } else {
        _showErrorConfirmationScreen(msg: value.message);
        sendMoneyVM.resetData();
        bankVM.resetData();
        transactionVm.setSelectedReason(null);
      }
    });
  }

  _transfer(String pin) {
    final sendMoneyVM = context.read<SendMoneyVM>();
    final transactionVm = context.read<TransactionVM>();
    final bankVM = context.read<BankVM>();
    final user = context.read<AuthUserVM>().user;

    sendMoneyVM
        .transfer(pin, transactionVm.selectedReason?.id)
        .then((value) async {
      if (value.success) {
        // Track successful money transfer
        try {
          // final data = value.data['data'];

          await UnifiedAnalyticsManager.instance.trackSendMoney(
            userId: user?.id ?? 'unknown',
            amount:
                double.tryParse(sendMoneyVM.fromC.text.replaceAllCommas()) ??
                    0.0,
            fromCurrency:
                sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD',
            toCurrency: sendMoneyVM.recipientCurrency?.code ?? 'NGN',
            corridor:
                '${sendMoneyVM.fromConvertWallet?.currency?.code ?? 'CAD'}-${sendMoneyVM.recipientCurrency?.code ?? 'NGN'}',
            // transactionId: data?['transaction_id']?.toString(),
            additionalParameters: {
              'transfer_method': sendMoneyVM.transferMethod?.name ?? 'unknown',
              'recipient_name':
                  sendMoneyVM.transferParams?.accountName ?? 'unknown',
              'transaction_timestamp': DateTime.now().toIso8601String(),
              'exchange_rate':
                  sendMoneyVM.conversionRate?.rate?.rate?.toString(),
              'recipient_amount': sendMoneyVM.recipientGetAmount,
              'transfer_fee': sendMoneyVM.transferFee,
            },
          );
        } catch (e) {
          printty('❌ Error tracking send money: $e');
        }

        final data = value.data['data'];
        String? securityAnswer;
        if (data != null && data is Map<String, dynamic>) {
          securityAnswer = data['security_answer'];
        }
        Navigator.pop(context, {
          'success': true,
          'securityAnswer': securityAnswer,
        });
        // _showSuccessConfirmationScreen(securityAnswer);
      } else {
        _showErrorConfirmationScreen(msg: value.message);
        sendMoneyVM.resetData();
        bankVM.resetData();
        transactionVm.setSelectedReason(null);
      }
    });
  }

  _showErrorConfirmationScreen({String? msg}) {
    final ctx = NavigatorKeys.appNavigatorKey.currentContext;
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(ctx!);
          Navigator.pushNamedAndRemoveUntil(
              ctx, RoutePath.dashboardNav, (route) => false);

          Navigator.pushNamed(ctx, RoutePath.sendMoneyScreen);
        },
      ),
    );
  }
}
