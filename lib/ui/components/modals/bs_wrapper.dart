import 'dart:ui';

import 'package:korrency/core/core.dart';

class BsWrapper {
  const BsWrapper._();
  static Future<T?> bottomSheet<T>({
    required BuildContext context,
    required Widget widget,
    isScrollControlled = true,
    bool? canDismiss,
    double? topRadius,
    Color? color,
  }) {
    return showModalBottomSheet<T>(
      backgroundColor: color ?? AppColors.transparent,
      barrierColor: AppColors.blue34.withValues(alpha: 0.60),
      isScrollControlled: true,
      isDismissible: canDismiss ?? true,
      enableDrag: false,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(topRadius ?? 16),
          topRight: Radius.circular(topRadius ?? 16),
        ),
      ),
      context: context,
      builder: (BuildContext bc) {
        return BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: 4.0, sigmaY: 4.0), // Adjust blur intensity
          child: SizedBox(
            width: Sizer.screenWidth,
            child: widget,
          ),
        );
      },
    );
  }

  static Future<T?> showCustomDialog<T>(
    BuildContext context, {
    required Widget child,
    bool? canDismiss,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: canDismiss ?? true,
      barrierColor: AppColors.blue34.withValues(alpha: 0.60),
      transitionDuration: const Duration(milliseconds: 700),
      pageBuilder: (_, __, ___) {
        return Center(
          child: Container(child: child),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        Tween<Offset> tween;
        if (anim.status == AnimationStatus.reverse) {
          tween = Tween(begin: const Offset(0, 1), end: Offset.zero);
        } else {
          tween = Tween(begin: const Offset(0, 1), end: Offset.zero);
        }

        return SlideTransition(
          position: tween.animate(anim),
          child: FadeTransition(
            opacity: anim,
            child: child,
          ),
        );
      },
    );
  }
}
