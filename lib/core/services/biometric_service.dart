// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';

class BiometricService {
  static final _auth = LocalAuthentication();

  static Future<bool> isBiometricAvailable() async {
    try {
      final bool canCheckBiometrics = await _auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        printty('Device does not support biometric authentication',
            level: 'BiometricService');
        return false;
      }

      final List<BiometricType> availableBiometrics =
          await _auth.getAvailableBiometrics();

      if (availableBiometrics.isEmpty) {
        printty('No biometric authentication methods are enrolled',
            level: 'BiometricService');
        return false;
      }

      printty('Available biometrics: $availableBiometrics',
          level: 'BiometricService');
      return true;
    } on PlatformException catch (e) {
      printty('Error checking biometric availability: ${e.code} - ${e.message}',
          level: 'BiometricService');
      return false;
    }
  }

  static Future<bool> authenticate() async {
    // First check if biometric authentication is available
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) {
      printty('Biometric authentication is not available on this device',
          level: 'BiometricService');
      return false;
    }

    try {
      final bool didAuthenticate = await _auth.authenticate(
        authMessages: const <AuthMessages>[
          AndroidAuthMessages(
            cancelButton: 'Cancel',
            signInTitle: 'Biometric Authentication',
          ),
          IOSAuthMessages(
            cancelButton: 'Cancel',
          ),
        ],
        localizedReason: 'Use fingerprint to authorize action',
      );

      printty('Biometric authentication result: $didAuthenticate',
          level: 'BiometricService');
      return didAuthenticate;
    } on PlatformException catch (e) {
      printty('Biometric error: ${e.code} - ${e.message}',
          level: 'BiometricService');

      // Handle specific error codes
      switch (e.code) {
        case 'NotAvailable':
          printty('Biometric authentication is not available',
              level: 'BiometricService');
          break;
        case 'NotEnrolled':
          printty('No biometric credentials are enrolled',
              level: 'BiometricService');
          break;
        case 'UserCancel':
          printty('User canceled biometric authentication',
              level: 'BiometricService');
          break;
        case 'PermanentlyLockedOut':
          printty('Biometric authentication is permanently locked out',
              level: 'BiometricService');
          break;
        case 'LockedOut':
          printty('Biometric authentication is temporarily locked out',
              level: 'BiometricService');
          break;
        case 'no_fragment_activity':
          printty('Fragment activity error - check MainActivity implementation',
              level: 'BiometricService');
          break;
        default:
          printty('Unknown biometric error: ${e.code}',
              level: 'BiometricService');
      }

      return false;
    } catch (e) {
      printty('Unexpected error during biometric authentication: $e',
          level: 'BiometricService');
      return false;
    }
  }

  static Future<String> getBiometricStatus() async {
    try {
      final bool canCheckBiometrics = await _auth.canCheckBiometrics;
      if (!canCheckBiometrics) {
        return 'Device does not support biometric authentication';
      }

      final List<BiometricType> availableBiometrics =
          await _auth.getAvailableBiometrics();

      if (availableBiometrics.isEmpty) {
        return 'No biometric authentication methods are enrolled';
      }

      return 'Biometric authentication is available: ${availableBiometrics.join(', ')}';
    } on PlatformException catch (e) {
      return 'Error checking biometric status: ${e.code} - ${e.message}';
    }
  }
}
