import 'package:flutter/gestures.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';

class MyDevicesScreen extends StatefulWidget {
  const MyDevicesScreen({super.key});

  @override
  State<MyDevicesScreen> createState() => _MyDevicesScreenState();
}

class _MyDevicesScreenState extends State<MyDevicesScreen> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
    printty(context.read<TrustedDeviceVM>().otherDevices,
        level: "trusted devices");
    super.initState();
  }

  _init() {
    context.read<TrustedDeviceVM>().getTrustedDevices();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TrustedDeviceVM>(
      builder: (context, vm, _) {
        return Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomHeader(
                    // showBackBtn: true,
                    showHeader: true,
                    headerText: 'My Devices',
                  ),
                  const YBox(30),
                  RichText(
                    text: TextSpan(
                      children: <TextSpan>[
                        TextSpan(
                          text:
                              'Don\'t recognize a device? Reset your password immediately or ',
                          style: TextStyle(
                            color: AppColors.gray700,
                            fontSize: Sizer.text(15),
                            fontFamily: 'Inter',
                            height: 1.2,
                          ),
                        ),
                        TextSpan(
                          text: 'contact support',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: Sizer.text(15),
                            fontFamily: 'Inter',
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.createFreshDeskTicketWebview,
                                arguments: WebViewArg(
                                  webURL: AppUtils.korrencyCreateTicket,
                                ),
                              );
                            },
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Builder(builder: (context) {
                      if (vm.isBusy) {
                        return Center(child: const SpinKitLoader());
                      }
                      return RefreshIndicator(
                        onRefresh: () async {
                          _init();
                        },
                        child: ListView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          children: [
                            const YBox(30),
                            Text(
                              "Primary Device",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.textBlack900,
                              ),
                            ),
                            const YBox(8),
                            SecurityTile(
                              leadingIcon: AppSvgs.phone2,
                              title: vm.primaryDevice?.deviceName ?? "",
                              subTitle:
                                  'Last Active: ${AppUtils.dayWithSuffixMonthAndYear(vm.primaryDevice?.lastActive ?? DateTime.now())}',
                              showTrailing: true,
                              trailingWidget: const Icon(
                                Iconsax.trash,
                                color: AppColors.gray0000,
                              ),
                              isSelected: true,
                            ),
                            const YBox(30),
                            Text(
                              "Other Devices",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.textBlack900,
                              ),
                            ),
                            const YBox(8),
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (ctx, i) {
                                var trustDevice = vm.otherDevices[i];
                                return SecurityTile(
                                  bgColor: AppColors.gray100,
                                  leadingIcon: AppSvgs.phone2,
                                  title: trustDevice.deviceName ?? "",
                                  subTitle:
                                      'Last Active: ${AppUtils.dayWithSuffixMonthAndYear(trustDevice.lastActive ?? DateTime.now())}',
                                  showTrailing: true,
                                  trailingWidget: InkWell(
                                    onTap: () {
                                      BsWrapper.bottomSheet(
                                          context: context,
                                          widget: ConfirmationSheet(
                                            title: "Delete Device",
                                            message:
                                                'Are you sure you want to delete this device?',
                                            secondBtnText: "Delete",
                                            secendBtnTap: () {
                                              _deleteDevice(trustDevice.id!);
                                              Navigator.pop(context);
                                            },
                                          ));
                                    },
                                    child: const Icon(
                                      Iconsax.trash,
                                      color: AppColors.iconRed,
                                    ),
                                  ),
                                  isSelected: true,
                                );
                              },
                              separatorBuilder: (ctx, _) => const YBox(16),
                              itemCount: vm.otherDevices.length,
                            ),
                            const YBox(24),
                          ],
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  _deleteDevice(int id) {
    printty("deleting device with id: $id", level: "delete device");
    context.read<TrustedDeviceVM>().deleteTrustedDevice(id).then((value) {
      if (value.success) {
        printty(value.message, level: "delete device success");
        context.read<TrustedDeviceVM>().getTrustedDevices();
        _showSuccessScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _showSuccessScreen() {
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: "Trusted Device Deleted\n Successfully",
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }
}
