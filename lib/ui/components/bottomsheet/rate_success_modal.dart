import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class RateSuccessModal extends StatefulWidget {
  const RateSuccessModal({super.key});

  @override
  State<RateSuccessModal> createState() => _RateSuccessModalState();
}

class _RateSuccessModalState extends State<RateSuccessModal>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _starAnimationController;

  // Animations
  late Animation<double> _starRotationAnimation;
  late Animation<double> _starScaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Star animation controller
    _starAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Star rotation animation
    _starRotationAnimation = Tween<double>(
      begin: -0.2,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));

    // Star scale animation
    _starScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _starAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _startInitialAnimations();
  }

  void _startInitialAnimations() {
    // Start star animation with slight delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _starAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _starAnimationController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(80),
                Text(
                  'Thanks for your feedback',
                  textAlign: TextAlign.center,
                  style: FontTypography.text20.semiBold,
                ),
                YBox(4),
                Text(
                  'We have received your feedback and would work on it, Thank you!',
                  textAlign: TextAlign.center,
                  style: FontTypography.text14.withCustomColor(
                    AppColors.gray93,
                  ),
                ),
                const YBox(28),
                CustomBtn.solid(
                  borderRadius: BorderRadius.circular(20),
                  onTap: () {
                    Navigator.pop(context);
                  },
                  text: "Done",
                ),
                const YBox(36),
              ],
            ),
          ),
          Positioned(
            top: -Sizer.height(62),
            right: 0,
            left: 0,
            child: AnimatedBuilder(
                animation: Listenable.merge([
                  _starAnimationController,
                  _starRotationAnimation,
                  _starScaleAnimation,
                ]),
                builder: (context, child) {
                  return Transform.scale(
                      scale: _starScaleAnimation.value,
                      child: Transform.rotate(
                        angle: _starRotationAnimation.value,
                        child: imageHelper(
                          AppImages.ratingHeart,
                          height: Sizer.height(124),
                          width: Sizer.width(124),
                        ),
                      ));
                }),
          )
        ],
      ),
    );
  }
}
