import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:shimmer/shimmer.dart';

class SelectAvatarScreen extends StatefulWidget {
  const SelectAvatarScreen({Key? key}) : super(key: key);

  @override
  State<SelectAvatarScreen> createState() => _SelectAvatarScreenState();
}

class _SelectAvatarScreenState extends State<SelectAvatarScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthUserVM>().getAvatars();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(10),
                Expanded(
                  child: SingleChildScrollView(
                    // controller: _scrollController,
                    child: Column(
                      children: [
                        const YBox(20),
                        const AuthTextSubTitle(
                          title: "Select an Avatar",
                          subtitle:
                              "Choose an avatar to personalize your profile.",
                        ),
                        const YBox(24),
                        Builder(builder: (context) {
                          if (vm.isBusy) {
                            return Wrap(
                              spacing: 20,
                              runSpacing: 20,
                              children: List.generate(
                                30,
                                (i) {
                                  return const AvatarLoadingShimmer();
                                },
                              ),
                            );
                          }
                          if (vm.avatars.isEmpty && !vm.isBusy) {
                            return SizedBox(
                              height: Sizer.height(500),
                              child: const Center(
                                child: Text("No avatars found",
                                    style: TextStyle(color: Colors.grey)),
                              ),
                            );
                          }
                          return Wrap(
                            spacing: 20,
                            runSpacing: 20,
                            children: [
                              ...List.generate(
                                vm.avatars.length,
                                (i) {
                                  return InkWell(
                                    onTap: () {
                                      Navigator.pushNamed(
                                        context,
                                        RoutePath.previewAvatarScreen,
                                        arguments: vm.avatars[i],
                                      );
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: Sizer.height(100),
                                      width: Sizer.width(100),
                                      padding: const EdgeInsets.all(5),
                                      decoration: BoxDecoration(
                                        color: AppColors.blu000,
                                        borderRadius:
                                            BorderRadius.circular(100),
                                      ),
                                      child: cacheNetWorkImage(
                                        vm.avatars[i],
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        }),
                        const YBox(120),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class AvatarLoadingShimmer extends StatelessWidget {
  const AvatarLoadingShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        height: 100,
        width: 100,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(100),
        ),
      ),
    );
  }
}
