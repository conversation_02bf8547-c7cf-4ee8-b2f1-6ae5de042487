import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SearchableDropdownExample extends StatefulWidget {
  const SearchableDropdownExample({super.key});

  @override
  State<SearchableDropdownExample> createState() => _SearchableDropdownExampleState();
}

class _SearchableDropdownExampleState extends State<SearchableDropdownExample> {
  String? selectedOccupation;
  String? selectedGender;

  // Sample occupation data as shown in the screenshot
  final List<SearchableDropdownItem<String>> occupationItems = [
    const SearchableDropdownItem(
      value: 'construction_manager',
      label: 'Construction Manager',
    ),
    const SearchableDropdownItem(
      value: 'marketing_manager',
      label: 'Marketing Manager',
    ),
    const SearchableDropdownItem(
      value: 'social_media_manager',
      label: 'Social Media Manager',
    ),
    const SearchableDropdownItem(
      value: 'supply_chain_manager',
      label: 'Supply Chain Manager',
    ),
    const SearchableDropdownItem(
      value: 'risk_manager',
      label: 'Risk Manager',
    ),
    const SearchableDropdownItem(
      value: 'warehouse_manager',
      label: 'Warehouse Manager',
    ),
    const SearchableDropdownItem(
      value: 'software_engineer',
      label: 'Software Engineer',
    ),
    const SearchableDropdownItem(
      value: 'data_scientist',
      label: 'Data Scientist',
    ),
    const SearchableDropdownItem(
      value: 'product_manager',
      label: 'Product Manager',
    ),
    const SearchableDropdownItem(
      value: 'business_analyst',
      label: 'Business Analyst',
    ),
  ];

  final List<SearchableDropdownItem<String>> genderItems = [
    const SearchableDropdownItem(
      value: 'male',
      label: 'Male',
    ),
    const SearchableDropdownItem(
      value: 'female',
      label: 'Female',
    ),
    const SearchableDropdownItem(
      value: 'other',
      label: 'Other',
    ),
    const SearchableDropdownItem(
      value: 'prefer_not_to_say',
      label: 'Prefer not to say',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      appBar: AppBar(
        title: const Text('Searchable Dropdown Example'),
        backgroundColor: AppColors.bgWhite,
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(40),
            
            // Gender Dropdown
            SearchableDropdownWidget<String>(
              labelText: "Gender",
              hintText: "Select your gender",
              showLabelHeader: true,
              items: genderItems,
              selectedValue: selectedGender,
              borderRadius: Sizer.height(12),
              onChanged: (value) {
                setState(() {
                  selectedGender = value;
                });
              },
            ),
            
            const YBox(24),
            
            // Occupation Dropdown (as shown in screenshot)
            SearchableDropdownWidget<String>(
              labelText: "Occupation",
              hintText: "Search occupation",
              showLabelHeader: true,
              items: occupationItems,
              selectedValue: selectedOccupation,
              borderRadius: Sizer.height(12),
              onChanged: (value) {
                setState(() {
                  selectedOccupation = value;
                });
              },
            ),
            
            const YBox(40),
            
            // Display selected values
            if (selectedGender != null || selectedOccupation != null) ...[
              Text(
                'Selected Values:',
                style: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w600,
                  color: AppColors.gray700,
                ),
              ),
              const YBox(16),
              if (selectedGender != null)
                Text(
                  'Gender: ${genderItems.firstWhere((item) => item.value == selectedGender).label}',
                  style: TextStyle(
                    fontSize: Sizer.text(14),
                    color: AppColors.gray600,
                  ),
                ),
              if (selectedOccupation != null) ...[
                const YBox(8),
                Text(
                  'Occupation: ${occupationItems.firstWhere((item) => item.value == selectedOccupation).label}',
                  style: TextStyle(
                    fontSize: Sizer.text(14),
                    color: AppColors.gray600,
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }
}