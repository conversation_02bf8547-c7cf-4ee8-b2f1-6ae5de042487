import 'package:korrency/core/core.dart';

class SendMoneyBenrficiaryListTile extends StatelessWidget {
  const SendMoneyBenrficiaryListTile({
    super.key,
    // required this.beneficiary,
    required this.name,
    required this.title,
    required this.subtitle,
    required this.iconPath,
    this.padding,
    this.onTap,
  });

  // final Beneficiary beneficiary;
  final String name;
  final String title;
  final String subtitle;
  final String iconPath;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: padding ??
            EdgeInsets.symmetric(
              vertical: Sizer.width(8),
              horizontal: Sizer.width(12),
            ),
        child: Row(
          children: [
            Container(
              height: Sizer.height(40),
              width: Sizer.height(40),
              decoration: BoxDecoration(
                color: AppColors.blueFD,
                borderRadius: BorderRadius.circular(Sizer.radius(100)),
                border: Border.all(
                  color: AppColors.grayEFA,
                ),
              ),
              child: Center(
                child: Text(
                  AppUtils.getInitials(name),
                  style: AppTypography.text16.semiBold.copyWith(
                    color: AppColors.blueCEA,
                  ),
                ),
              ),
            ),
            XBox(10),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray51,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTypography.text12.copyWith(
                      color: AppColors.gray500,
                    ),
                  ),
                ],
              ),
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(Sizer.radius(4)),
              child: SizedBox(
                height: Sizer.width(28),
                width: Sizer.width(28),
                child: iconPath.isEmpty
                    ? Icon(
                        Iconsax.bank,
                      )
                    : Image.network(iconPath, fit: BoxFit.cover),
              ),
            )
          ],
        ),
      ),
    );
  }
}
