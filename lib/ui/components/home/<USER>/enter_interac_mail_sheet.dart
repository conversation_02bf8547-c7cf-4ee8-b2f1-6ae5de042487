import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class EnterInteractMailSheet extends StatelessWidget {
  const EnterInteractMailSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(20),
          const AuthTextSubTitle(
            title: "Please Enter Your Interac Mail",
            subtitle: "As a regulatory mandate, we need your \ninterac email.",
          ),
          const YBox(30),
          CustomTextField(
            labelText: "Enter Email",
            showLabelHeader: true,
            borderRadius: Sizer.height(4),
            hintText: '<EMAIL>',
            onChanged: (val) {
              // vm.validatePassword(vm.passwordC.text);
            },
          ),
          const Spacer(),
          CustomBtn.solid(
            onTap: () {
              Navigator.pop(context);
            },
            online: true,
            text: "Continue",
          ),
          const YBox(50),
        ],
      ),
    );
  }
}
