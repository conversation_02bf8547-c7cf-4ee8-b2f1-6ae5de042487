import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class EurAccountDetailsScreen extends StatelessWidget {
  const EurAccountDetailsScreen({super.key, required this.wallet});

  final Wallet wallet;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Your Account Details',
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Make a Bank transfer to the account displayed below",
                style: AppTypography.text15.copyWith(color: AppColors.gray93),
              ),
              const YBox(10),
              Expanded(
                child: LoadableContentBuilder(
                  isBusy: vm.isBusy,
                  isError: false,
                  items: wallet.virtualAccounts ?? [],
                  loadingBuilder: (ctx) {
                    return SizedBox(
                      height: Sizer.height(200),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                  emptyBuilder: (ctx) {
                    return Center(
                      child: EmptyState(
                        title: "No virtual account found",
                      ),
                    );
                  },
                  contentBuilder: (context) {
                    final currency = wallet.currency;
                    final vAcct = wallet.virtualAccounts?.isNotEmpty == true
                        ? wallet.virtualAccounts?.first
                        : VirtualAccount();
                    return ListView(
                      padding: EdgeInsets.only(top: Sizer.height(20)),
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.blueFD,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(4),
                              topRight: Radius.circular(4),
                              bottomLeft: Radius.circular(16),
                              bottomRight: Radius.circular(16),
                            ),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(20),
                                  vertical: Sizer.height(12),
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryBlue90,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(4),
                                    topRight: Radius.circular(4),
                                    bottomLeft: Radius.circular(16),
                                    bottomRight: Radius.circular(16),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        SvgPicture.network(
                                          currency?.flag ?? "",
                                          height: Sizer.height(16),
                                          width: Sizer.width(22),
                                        ),
                                        const XBox(10),
                                        Text(
                                          currency?.name ?? "",
                                          style: AppTypography.text10.medium
                                              .copyWith(
                                            color: AppColors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                    YBox(12),
                                    Text(
                                      "Account Number",
                                      style: AppTypography.text12.medium
                                          .copyWith(
                                              color: AppColors.white
                                                  .withValues(alpha: 0.8)),
                                    ),
                                    YBox(4),
                                    Row(
                                      children: [
                                        Text(
                                          getAccountNumber(
                                                  vAcct?.accountNumber ?? "") ??
                                              "",
                                          style: AppTypography.text20.semiBold
                                              .copyWith(
                                            color: AppColors.white,
                                          ),
                                        ),
                                        Spacer(),
                                        InkWell(
                                          onTap: () {
                                            Clipboard.setData(ClipboardData(
                                              text: getAccountNumber(
                                                      vAcct?.accountNumber ??
                                                          "") ??
                                                  "",
                                            ));
                                            showSuccessToastMessage(
                                                "Account number copied");
                                          },
                                          child: SvgPicture.asset(AppSvgs.copy),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              YBox(4),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(20),
                                  vertical: Sizer.height(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    _buildAccountdetails(
                                      title: "Account Name",
                                      subTitle: vAcct?.accountName ?? "",
                                    ),
                                    YBox(16),
                                    _buildAccountdetails(
                                      title: "Sort Code",
                                      subTitle: getSortCodeNumber(
                                              vAcct?.accountNumber ?? "") ??
                                          "",
                                    ),
                                    YBox(16),
                                    _buildAccountdetails(
                                      title: "IBAN",
                                      subTitle: vAcct?.accountNumber ?? "",
                                    ),
                                    YBox(8),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        YBox(20),
                        Container(
                          padding: EdgeInsets.all(Sizer.radius(20)),
                          decoration: BoxDecoration(
                            color: AppColors.grayFCF,
                            border: Border.all(
                              color: AppColors.blueFD,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    AppSvgs.infoBlue,
                                    height: Sizer.height(16),
                                    width: Sizer.width(16),
                                  ),
                                  XBox(6),
                                  Expanded(
                                    child: Text(
                                      "Send money only from a bank account in your name that matches your Korrency account.",
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.gray93,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              YBox(16),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SvgPicture.asset(
                                    AppSvgs.infoBlue,
                                    height: Sizer.height(16),
                                    width: Sizer.width(16),
                                  ),
                                  XBox(6),
                                  Expanded(
                                    child: Text(
                                      "Funds are usually delivered in just a few seconds",
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.gray93,
                                      ),
                                    ),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        YBox(28),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "This account is issued and serviced by",
                              style: AppTypography.text12.copyWith(
                                color: AppColors.gray93,
                              ),
                            ),
                            XBox(8),
                            Image.asset(
                              AppImages.clearJunction,
                              height: Sizer.height(20),
                            )
                          ],
                        ),
                        YBox(80),
                        CustomBtn.withChild(
                          borderRadius: BorderRadius.circular(20),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppSvgs.share,
                                colorFilter: ColorFilter.mode(
                                    AppColors.white, BlendMode.srcIn),
                              ),
                              XBox(8),
                              Text(
                                "Share Details",
                                style: AppTypography.text16.medium.copyWith(
                                  color: AppColors.white,
                                ),
                              ),
                            ],
                          ),
                          onTap: () async {
                            Share.share(
                              "IBAN: ${vAcct?.accountNumber} \nAccount Name: ${vAcct?.accountName} \nSort Code: ${getSortCodeNumber(vAcct?.bankName ?? "")}",
                              subject: "Korrency",
                            );
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildAccountdetails({
    required String title,
    required String subTitle,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray93,
                ),
              ),
              YBox(4),
              Text(
                subTitle.toUpperCase(),
                style: AppTypography.text12.bold.copyWith(
                  color: AppColors.gray51,
                ),
              ),
            ],
          ),
        ),
        CopyBtn(text: subTitle)
      ],
    );
  }
}
