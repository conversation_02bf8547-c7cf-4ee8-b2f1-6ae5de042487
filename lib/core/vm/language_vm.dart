import 'package:korrency/core/core.dart';
import 'package:easy_localization/easy_localization.dart';

class LanguageVM extends BaseVM {
  String _languageCode = 'en';
  String get languageCode => _languageCode;
  Locale get locale => Locale(_languageCode);
  bool _initialized = false;

  Future<void> ensureInitialized(BuildContext context) async {
    if (_initialized) return;
    final saved = await StorageService.getStringItem(StorageKey.languageCode);
    final code = saved ?? context.locale.languageCode;
    _languageCode = code;
    _initialized = true;
    reBuildUI();
  }

  Future<void> setLanguage(BuildContext context, String code) async {
    if (code == _languageCode) return;
    _languageCode = code;
    await StorageService.storeStringItem(StorageKey.languageCode, code);
    await context.setLocale(Locale(code));
    reBuildUI();
  }

  bool isSelected(String code) => _languageCode.toLowerCase() == code.toLowerCase();
}