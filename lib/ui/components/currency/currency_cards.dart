import 'package:korrency/core/core.dart';

class CurrencyCards extends StatelessWidget {
  const CurrencyCards({
    super.key,
    this.currencyCode,
    this.showCurrencyCode = false,
    this.showArrowIcon = true,
    this.isNetworkSvg = false,
    required this.flagIconPath,
    this.bgColor,
    this.onTap,
  });

  final String? currencyCode;
  final bool showCurrencyCode;
  final bool showArrowIcon;
  final bool isNetworkSvg;
  final String flagIconPath;
  final Color? bgColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(8),
          vertical: Sizer.height(4),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.grayC3,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(100),
              child: SizedBox(
                height: Sizer.height(20),
                width: Sizer.width(20),
                child: isNetworkSvg
                    ? SvgPicture.network(
                        flagIconPath,
                        fit: BoxFit.cover,
                      )
                    : imageHelper(
                        flagIconPath,
                      ),
              ),
            ),
            const XBox(6),
            if (showCurrencyCode)
              Padding(
                padding: EdgeInsets.only(
                  right: Sizer.width(4),
                ),
                child: Text(
                  currencyCode!,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            if (showArrowIcon)
              SvgPicture.asset(
                AppSvgs.expandMore,
                height: Sizer.height(16),
              ),
          ],
        ),
      ),
    );
  }
}
