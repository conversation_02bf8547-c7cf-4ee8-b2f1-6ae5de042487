import 'dart:convert';

List<StateProvince> stateProvinceFromJson(String str) =>
    List<StateProvince>.from(
        json.decode(str).map((x) => StateProvince.fromJson(x)));

String stateProvinceToJson(List<StateProvince> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class StateProvince {
  String? name;
  bool restricted;

  StateProvince({required this.name, this.restricted = false});

  factory StateProvince.fromJson(Map<String, dynamic> json) =>
      StateProvince(name: json["name"], restricted: json["restricted"]);

  Map<String, dynamic> toJson() => {"name": name, "restricted": restricted};
}

List<StateProvince> listOfStateProvince = [
  StateProvince(name: "Ontario"),
  StateProvince(name: "Nova Scotia"),
  StateProvince(name: "New Brunswick"),
  StateProvince(name: "Manitoba"),
  StateProvince(name: "British Columbia"),
  StateProvince(name: "Prince Edward Island"),
  StateProvince(name: "Saskatchewan"),
  StateProvince(name: "Alberta"),
  StateProvince(name: "Newfoundland and Labrador"),
  StateProvince(name: "Northwest Territories"),
  StateProvince(name: "Yukon"),
  StateProvince(name: "Quebec"),
  // StateProvince(name: "Quebec", restricted: true),
];
