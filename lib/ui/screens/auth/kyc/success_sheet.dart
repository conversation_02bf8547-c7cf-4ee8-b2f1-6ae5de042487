import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SuccessSheet extends StatelessWidget {
  const SuccessSheet({
    super.key,
    required this.arg,
  });

  final ConfirmationArg arg;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Container(
            margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
            height: Sizer.height(12),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.sheetLightBlue.withOpacity(0.25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              width: Sizer.screenWidth,
              decoration: const BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Container(
                  //   alignment: Alignment.centerLeft,
                  //   padding: EdgeInsets.only(top: Sizer.height(28)),
                  //   child: BackPop(
                  //       onTap: arg.onBtnTap ??
                  //           () {
                  //             Navigator.pop(context);
                  //           }),
                  // ),
                  Expanded(
                    child: ConfirmationBody(arg: arg),
                  ),
                  CustomBtn.solid(
                    onTap: arg.onBtnTap ?? () {},
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    online: true,
                    text: arg.buttonText,
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
