import 'dart:convert';

List<Bank> bankFromJson(String str) =>
    List<Bank>.from(json.decode(str).map((x) => Bank.fromJson(x)));

String bankToJson(List<Bank> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Bank {
  final String? name;
  final String? bic;
  final String? countryCode;
  final String? currencyCode;
  final String? domBankCode;
  final String? iban;
  final String? uuid;
  final BankLimit? bankLimit;

  Bank({
    this.name,
    this.bic,
    this.countryCode,
    this.currencyCode,
    this.domBankCode,
    this.iban,
    this.uuid,
    this.bankLimit,
  });

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
        name: json["name"],
        bic: json["bic"],
        countryCode: json["country_code"],
        currencyCode: json["currency_code"],
        domBankCode: json["dom_bank_code"],
        iban: json["iban"],
        uuid: json["uuid"],
        bankLimit: json["bank_limit"] == null
            ? null
            : BankLimit.fromJson(json["bank_limit"]),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "bic": bic,
        "country_code": countryCode,
        "currency_code": currencyCode,
        "dom_bank_code": domBankCode,
        "iban": iban,
        "uuid": uuid,
        "bank_limit": bankLimit?.toJson(),
      };
}

class BankLimit {
  final int? maxDailyValue;
  final int? maxMonthlyValue;
  final int? maxPerTxLimit;
  final int? maxWeeklyValue;
  final int? minPerTxLimit;

  BankLimit({
    this.maxDailyValue,
    this.maxMonthlyValue,
    this.maxPerTxLimit,
    this.maxWeeklyValue,
    this.minPerTxLimit,
  });

  factory BankLimit.fromJson(Map<String, dynamic> json) => BankLimit(
        maxDailyValue: json["max_daily_value"],
        maxMonthlyValue: json["max_monthly_value"],
        maxPerTxLimit: json["max_per_tx_limit"],
        maxWeeklyValue: json["max_weekly_value"],
        minPerTxLimit: json["min_per_tx_limit"],
      );

  Map<String, dynamic> toJson() => {
        "max_daily_value": maxDailyValue,
        "max_monthly_value": maxMonthlyValue,
        "max_per_tx_limit": maxPerTxLimit,
        "max_weekly_value": maxWeeklyValue,
        "min_per_tx_limit": minPerTxLimit,
      };
}

// List<Bank> bankFromJson(String str) =>
//     List<Bank>.from(json.decode(str).map((x) => Bank.fromJson(x)));

// String bankToJson(List<Bank> data) =>
//     json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

// class Bank {
//   String? name;
//   String? uuid;
//   String? interInstitutionCode;
//   String? sortCode;

//   Bank({
//     this.name,
//     this.uuid,
//     this.interInstitutionCode,
//     this.sortCode,
//   });

//   factory Bank.fromJson(Map<String, dynamic> json) => Bank(
//         name: json["name"],
//         uuid: json["uuid"],
//         interInstitutionCode: json["interInstitutionCode"],
//         sortCode: json["sortCode"],
//       );

//   Map<String, dynamic> toJson() => {
//         "name": name,
//         "uuid": uuid,
//         "interInstitutionCode": interInstitutionCode,
//         "sortCode": sortCode,
//       };

//   @override
//   String toString() {
//     return 'Bank(name: $name, uuid: $uuid, interInstitutionCode: $interInstitutionCode, sortCode: $sortCode)';
//   }
// }
