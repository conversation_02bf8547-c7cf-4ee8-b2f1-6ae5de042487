import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:store_redirect/store_redirect.dart';

class UpdateAvailableScreen extends StatelessWidget {
  const UpdateAvailableScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.blue200,
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            imageHelper(
              AppImages.update,
              // height: Sizer.height(418),
            ),
            Text(
              'New Update is Available',
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.baseBlack,
              ),
            ),
            const YBox(10),
            Text(
              'A new version of the Korrency app is available. \nPlease update to get the latest version.',
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.textBlack800,
              ),
            ),
            const YBox(100),
            CustomBtn.solid(
              onTap: () {
                printty('Update Now');
                StoreRedirect.redirect(
                  androidAppId: "korrency.mobile.com",
                  iOSAppId: "6495368627",
                );
              },
              text: "Update Now",
            ),
            const YBox(16),
            Align(
              alignment: Alignment.center,
              child: Text(
                'Version ${context.watch<ConfigVM>().myAppCurrentVersion ?? '1.0.0'}',
                style: AppTypography.text14.copyWith(
                  color: AppColors.grayE0,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const YBox(50),
          ],
        ),
      ),
    );
  }
}
