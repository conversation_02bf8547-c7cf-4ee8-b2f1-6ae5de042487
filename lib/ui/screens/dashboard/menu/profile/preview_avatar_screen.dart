import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class PreviewAvatarScreen extends StatelessWidget {
  const PreviewAvatarScreen({
    Key? key,
    required this.avatarString,
  }) : super(key: key);

  final String avatarString;

  @override
  Widget build(BuildContext context) {
    printty(avatarString, level: 'avatarString');
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const ArrowBack(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Preview Avatar",
                    subtitle:
                        "You can edit some parts of your profile and update it.",
                  ),
                  const YBox(50),
                  Container(
                    alignment: Alignment.center,
                    height: Sizer.height(310),
                    width: Sizer.width(310),
                    padding: EdgeInsets.all(Sizer.radius(8)),
                    decoration: BoxDecoration(
                      color: AppColors.blu000,
                      borderRadius: BorderRadius.circular(200),
                    ),
                    child: cacheNetWorkImage(
                      avatarString,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const Spacer(),
                  Consumer<AuthUserVM>(builder: (context, vm, _) {
                    return CustomBtn.solid(
                      onTap: () {
                        vm.updateProfileAvatar(avatarString).then((value) {
                          if (value.success) {
                            context.read<AuthUserVM>().getAuthUser();
                            Navigator.pop(context);
                            Navigator.pop(context);
                            FlushBarToast.fLSnackBar(
                              message: value.message.toString(),
                              snackBarType: SnackBarType.success,
                            );
                          } else {
                            FlushBarToast.fLSnackBar(
                              message: value.message.toString(),
                            );
                          }
                        });
                      },
                      isLoading: vm.isBusy,
                      text: "Save and Continue",
                    );
                  }),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
