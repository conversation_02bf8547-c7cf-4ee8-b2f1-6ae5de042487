import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

List<Widget> buildAccountLimitCards({
  CurrencyLimits? limits,
  Currency? currency,
  bool showBorder = false,
}) {
  final limitTypes = [
    {
      'title': 'Daily Limit',
      'data': limits?.limits?.dailySpend,
    },
    {
      'title': 'Weekly Limit',
      'data': limits?.limits?.weeklySpend,
    },
    {
      'title': 'Monthly Limit',
      'data': limits?.limits?.monthlySpend,
    },
  ];

  List<Widget> cards = [];

  for (int i = 0; i < limitTypes.length; i++) {
    final limitType = limitTypes[i];
    final data = limitType['data'] as LySpend?;

    // Get percentage from backend (0-100 scale) and convert to 0-1 scale
    double percent = 0.0;
    if (data?.percentageUsed != null) {
      final backendPercent = double.tryParse(data!.percentageUsed!) ?? 0.0;
      percent = backendPercent / 100.0; // Convert from 0-100 to 0-1 scale
    }

    cards.add(
      AccountLimitCard(
        showBorder: showBorder,
        title: limitType['title'] as String,
        percent: percent,
        amountUsed:
            '${AppUtils.formatAmountDoubleString(data?.amountUsed ?? "0")} ${currency?.code}',
        totalAmount:
            '${AppUtils.formatAmountDoubleString(data?.totalAmount ?? "0")} ${currency?.code}',
      ),
    );

    // Add spacing between cards (except for the last one)
    if (i < limitTypes.length - 1) {
      cards.add(YBox(20));
    }
  }

  return cards;
}
