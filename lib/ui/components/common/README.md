# Reusable Swiper Card Widget

A highly customizable and reusable swiper card widget that displays a stack of cards with swipe and tap functionality.

## Features

- ✅ **Swipe Gestures**: Left and right swipe support with customizable thresholds
- ✅ **Tap Interactions**: Separate callbacks for primary and background card taps
- ✅ **Customizable Animations**: Configurable animation duration and curves
- ✅ **Flexible Layout**: Adjustable card height and stack offset
- ✅ **Event Callbacks**: Rich callback system for handling user interactions
- ✅ **Performance Optimized**: Efficient rendering and animation handling
- ✅ **Type Safe**: Full TypeScript/Dart type safety

## Usage

### Basic Implementation

```dart
import 'package:flutter/material.dart';
import 'reusable_swiper_card.dart';

class MyScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ReusableSwiperCard(
      cards: [
        Container(color: Colors.red, child: Text('Card 1')),
        Container(color: Colors.blue, child: Text('Card 2')),
        Container(color: Colors.green, child: Text('Card 3')),
      ],
      onSwipe: (cardIndex, isRightSwipe) {
        print('Card $cardIndex swiped ${isRightSwipe ? 'right' : 'left'}');
      },
      onTap: (cardIndex) {
        print('Background card $cardIndex tapped');
      },
      onPrimaryTap: (cardIndex) {
        print('Primary card $cardIndex tapped');
      },
    );
  }
}
```

### Advanced Configuration

```dart
ReusableSwiperCard(
  cards: myWidgetList,
  cardHeight: 250.0,                    // Custom card height
  cardOffset: 30.0,                     // Spacing between stacked cards
  animationDuration: Duration(milliseconds: 400),
  enableSwipe: true,                    // Enable/disable swipe gestures
  enableBackgroundTap: true,            // Enable/disable background card taps
  enablePrimaryTap: false,              // Enable/disable primary card taps
  minDragDistance: 80.0,                // Minimum drag distance to trigger swipe
  minVelocity: 150.0,                   // Minimum velocity to trigger swipe
  onSwipe: (cardIndex, isRightSwipe) {
    // Handle swipe events
    if (isRightSwipe) {
      // Handle right swipe (e.g., like, accept)
    } else {
      // Handle left swipe (e.g., dislike, reject)
    }
  },
  onTap: (cardIndex) {
    // Handle background card tap (e.g., bring to front)
  },
  onPrimaryTap: (cardIndex) {
    // Handle primary card tap (e.g., open details)
  },
)
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `cards` | `List<Widget>` | **Required** | List of widgets to display as cards |
| `onSwipe` | `Function(int, bool)?` | `null` | Callback when card is swiped (cardIndex, isRightSwipe) |
| `onTap` | `Function(int)?` | `null` | Callback when background card is tapped |
| `onPrimaryTap` | `Function(int)?` | `null` | Callback when primary card is tapped |
| `cardHeight` | `double` | `220` | Height of each card |
| `cardOffset` | `double` | `40.0` | Vertical offset between stacked cards |
| `animationDuration` | `Duration` | `300ms` | Animation duration for swipe animations |
| `enableSwipe` | `bool` | `true` | Whether to enable swipe gestures |
| `enableBackgroundTap` | `bool` | `true` | Whether to enable tap on background cards |
| `enablePrimaryTap` | `bool` | `true` | Whether to enable tap on primary card |
| `minDragDistance` | `double` | `50.0` | Minimum drag distance to trigger swipe |
| `minVelocity` | `double` | `100.0` | Minimum velocity to trigger swipe |

## Use Cases

### 1. Wallet Cards
Perfect for displaying multiple wallet/account cards where users can swipe through them or tap to select.

### 2. Photo Gallery
Create a Tinder-like photo browsing experience with swipe-to-like functionality.

### 3. Product Showcase
Display products in a stack where users can swipe to dismiss or tap to view details.

### 4. Card Games
Implement card game interfaces with realistic card stacking and interaction.

### 5. Onboarding Screens
Create interactive onboarding experiences with swipeable information cards.

## Best Practices

1. **Card Content**: Ensure your card widgets have consistent dimensions for best visual results
2. **Performance**: Limit the number of cards (recommended: 5-10) for optimal performance
3. **Accessibility**: Add semantic labels and ensure tap targets are large enough
4. **Animation**: Use appropriate animation durations (200-400ms) for smooth user experience
5. **Feedback**: Provide visual or haptic feedback for user interactions

## Examples

See `swiper_card_example.dart` for comprehensive examples including:
- Photo cards with image loading
- Product cards with gradient backgrounds
- Different configurations and callbacks

## Migration from Old Implementation

If you're migrating from a custom swiper implementation:

1. Replace your custom gesture handling with `ReusableSwiperCard`
2. Move your card building logic to a separate method that returns `List<Widget>`
3. Replace animation controllers with the widget's built-in callbacks
4. Configure the widget parameters to match your previous behavior

```dart
// Old way
class MyCustomSwiper extends StatefulWidget with TickerProviderStateMixin {
  // Complex animation and gesture handling code...
}

// New way
ReusableSwiperCard(
  cards: _buildMyCards(),
  onSwipe: _handleSwipe,
  onTap: _handleTap,
)
```