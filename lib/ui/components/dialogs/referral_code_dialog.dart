// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReferralCodeDialog extends StatefulWidget {
  const ReferralCodeDialog({
    super.key,
  });

  @override
  State<ReferralCodeDialog> createState() => _ReferralCodeDialogState();
}

class _ReferralCodeDialogState extends State<ReferralCodeDialog> {
  final referralCodeC = TextEditingController();

  @override
  dispose() {
    referralCodeC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReferralVM>(builder: (ctx, refVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          decoration: BoxDecoration(
            color: AppColors.bgWhite,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(28),
              Text(
                'Enter Referral Code',
                style: FontTypography.text22.semiBold,
              ),
              YBox(4),
              Text(
                'Please enter a referral code below',
                textAlign: TextAlign.center,
                style: FontTypography.text16.withCustomColor(
                  AppColors.gray93,
                ),
              ),
              const YBox(32),
              CustomTextField(
                controller: referralCodeC,
                labelText: "Referral Code",
                showLabelHeader: true,
                borderRadius: Sizer.height(12),
                onChanged: (val) {
                  setState(() {});
                },
              ),
              const YBox(120),
              CustomBtn.solid(
                onTap: () async {
                  final res = await refVm.getAddReferralCode(
                    referralCodeC.text.trim(),
                  );

                  handleApiResponse(
                    response: res,
                    onSuccess: () {
                      referralCodeC.clear();
                      Navigator.pop(context);
                      BsWrapper.showCustomDialog(
                        context,
                        child: ConfirmationDialog(
                          args: ConfirmationDialogArgs(
                            title: "Referral Code Successfully \nEntered",
                            content: "Referal code has been validated",
                            btnText: "Done",
                            onTap: () {
                              final ctx =
                                  NavigatorKeys.appNavigatorKey.currentContext!;
                              Navigator.pop(ctx);
                            },
                          ),
                        ),
                      );
                    },
                  );
                },
                online: referralCodeC.text.trim().length > 5,
                isLoading: refVm.isBusy,
                borderRadius: BorderRadius.circular(Sizer.radius(14)),
                text: "Enter",
              ),
              const YBox(30),
            ],
          ),
        ),
      );
    });
  }
}
