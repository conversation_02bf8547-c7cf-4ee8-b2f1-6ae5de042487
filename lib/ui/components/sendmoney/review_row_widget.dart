import 'package:korrency/core/core.dart';

class ReviewRowWidget extends StatelessWidget {
  const ReviewRowWidget({
    super.key,
    required this.iconPath,
    required this.code,
    required this.title,
    required this.amount,
  });

  final String title;
  final String iconPath;
  final String code;
  final String amount;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(100),
          child: SizedB<PERSON>(
            height: Sizer.height(28),
            width: Sizer.width(28),
            child: SvgPicture.network(iconPath, fit: BoxFit.cover),
          ),
        ),
        const XBox(8),
        Padding(
          padding: EdgeInsets.only(
            right: Sizer.width(4),
          ),
          child: Text(
            code,
            style: AppTypography.text18.copyWith(
              color: AppColors.primaryBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        const Spacer(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              title,
              style: AppTypography.text12.copyWith(
                color: AppColors.gray79,
              ),
            ),
            Text(
              amount,
              style: AppTypography.text22.copyWith(
                color: AppColors.blue800,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        )
      ],
    );
  }
}
