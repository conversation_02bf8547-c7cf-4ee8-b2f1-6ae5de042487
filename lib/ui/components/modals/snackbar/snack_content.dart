import 'dart:ui' as ui;

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/modals/snackbar/snackbar.dart';

class SnackContent extends StatelessWidget {
  const SnackContent({
    super.key,
    required this.contentType,
    required this.title,
    required this.message,
  });

  final SnackType contentType;
  final String title;
  final String message;

  @override
  Widget build(BuildContext context) {
    /// for reflecting different color shades in the SnackBar
    final hsl = HSLColor.fromColor(contentType.color!);
    final hslDark = hsl.withLightness((hsl.lightness - 0.1).clamp(0.0, 1.0));
    return Container(
      color: AppColors.transparent,
      height: Sizer.height(90),
      child: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.topCenter,
        children: [
          Container(
            width: Sizer.screenWidth,
            height: Sizer.height(90),
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(10),
              vertical: Sizer.height(10),
            ),
            decoration: BoxDecoration(
              color: contentType.color,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
            ),
            child: Row(
              children: [
                const XBox(40),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // svgHelper(
                      //   AppSvgs.success,
                      //   height: 20,
                      //   width: 20,
                      //   color: AppColors.white,
                      // ),
                      Text(
                        title,
                        style: AppTypography.text14b
                            .copyWith(color: AppColors.white),
                      ),
                      const YBox(4),
                      Text(
                        message,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            right: 10,
            top: 8,
            child: InkWell(
              onTap: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
              child: const Icon(
                Icons.close,
                color: AppColors.white,
              ),
            ),
          ),
          Positioned(
            left: 0,
            bottom: 0,
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(Sizer.radius(20)),
              ),
              child: svgHelper(
                AppSvgs.bubbles,
                height: Sizer.height(50),
                width: Sizer.width(40),
                colorFilter:
                    _getColorFilter(hslDark.toColor(), ui.BlendMode.srcIn),
              ),
            ),
          ),
          Positioned(
            left: 0,
            top: Sizer.height(-12),
            child: Stack(
              alignment: Alignment.center,
              children: [
                svgHelper(
                  AppSvgs.back,
                  height: Sizer.height(40),
                  width: Sizer.width(35),
                  // color: AppColors.white.withOpacity(0.2),
                ),
                Positioned(
                  top: 10,
                  child: svgHelper(
                    assetSVG(contentType),
                    height: Sizer.height(20),
                    width: Sizer.width(20),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Reflecting proper icon based on the contentType
  String assetSVG(SnackType contentType) {
    if (contentType == SnackType.failure) {
      /// failure will show `CROSS`
      return AppSvgs.failure;
    } else if (contentType == SnackType.success) {
      /// success will show `CHECK`
      return AppSvgs.success;
    } else if (contentType == SnackType.warning) {
      /// warning will show `EXCLAMATION`
      return AppSvgs.warning;
    } else if (contentType == SnackType.help) {
      /// help will show `QUESTION MARK`
      return AppSvgs.help;
    } else {
      return AppSvgs.failure;
    }
  }

  static ColorFilter? _getColorFilter(
          ui.Color? color, ui.BlendMode colorBlendMode) =>
      color == null ? null : ui.ColorFilter.mode(color, colorBlendMode);
}
