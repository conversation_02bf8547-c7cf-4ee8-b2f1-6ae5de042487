import 'package:korrency/core/core.dart';

class HeaderHideToggle extends StatelessWidget {
  const HeaderHideToggle({
    super.key,
    required this.header,
    this.trailingText,
    this.hidden = false,
    this.onTap,
  });

  final String header;
  final String? trailingText;
  final bool hidden;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                header.toUpperCase(),
                style:
                    AppTypography.text14.copyWith(color: AppColors.mainBlack),
              ),
              Spacer(),
              if (onTap != null)
                InkWell(
                  onTap: onTap,
                  child: Row(
                    children: [
                      Text(
                        trailingText ?? (hidden ? "Show" : "Hide"),
                        style: AppTypography.text13
                            .copyWith(color: AppColors.gray79),
                      ),
                      XBox(6),
                      trailingText?.toLowerCase() == "view all"
                          ? SvgPicture.asset(
                              AppSvgs.arrowRight,
                              colorFilter: ColorFilter.mode(
                                AppColors.grayE91,
                                BlendMode.srcIn,
                              ),
                            )
                          : Icon(
                              hidden
                                  ? Iconsax.arrow_up_2
                                  : Iconsax.arrow_down_1,
                              color: AppColors.gray79,
                              size: Sizer.width(20),
                            )
                    ],
                  ),
                )
            ],
          ),
          YBox(10),
          Divider(color: AppColors.grayBEB),
          YBox(10),
        ],
      ),
    );
  }
}
