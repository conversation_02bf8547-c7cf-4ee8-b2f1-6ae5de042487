import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DeactivateAccountReasonSheet extends StatefulWidget {
  const DeactivateAccountReasonSheet({
    super.key,
  });

  @override
  State<DeactivateAccountReasonSheet> createState() =>
      _DeactivateAccountReasonSheetState();
}

class _DeactivateAccountReasonSheetState
    extends State<DeactivateAccountReasonSheet> {
  TextEditingController descriptionC = TextEditingController();
  FocusNode descriptionFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    descriptionFocus.requestFocus();
  }

  @override
  void dispose() {
    descriptionC.dispose();
    descriptionFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (ctx, vm, _) {
      return Container(
        margin: EdgeInsets.all(
          Sizer.width(12),
        ),
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ContainerWithTopBorderRadius(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(24),
              Text(
                'Tell Us More About Your Reason for Leaving',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                  fontSize: Sizer.text(22),
                ),
              ),
              const YBox(4),
              Text(
                'Take a moment to tell us how we can improve',
                // textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.gray700,
                  fontSize: Sizer.text(14),
                ),
              ),
              const YBox(24),
              Text(
                "Your Feedback",
                style: FontTypography.text12.withCustomColor(AppColors.gray93),
              ),
              YBox(8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  border: Border.all(color: AppColors.grayAB),
                  borderRadius: BorderRadius.circular(Sizer.height(12)),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        left: Sizer.width(12),
                        top: Sizer.height(10),
                      ),
                      child: Icon(
                        Iconsax.message_edit,
                        color: AppColors.gray500,
                        size: Sizer.height(20),
                      ),
                    ),
                    Expanded(
                      child: CustomTextField(
                        focusNode: descriptionFocus,
                        controller: descriptionC,
                        hideBorder: true,
                        maxLines: 3,
                        borderRadius: Sizer.radius(12),
                        onChanged: (_) => vm.reBuildUI(),
                      ),
                    ),
                  ],
                ),
              ),
              const YBox(40),
              CustomBtn.solid(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                online: descriptionC.text.trim().isNotEmpty,
                onTap: () {
                  Navigator.pop(context, true);

                  vm.setSelectedReason(
                    DeactivateAccountMessage(
                      description: descriptionC.text.trim(),
                      reason: 'Other',
                    ),
                  );
                },
                text: "Submit",
              ),
              const YBox(40)
            ],
          ),
        ),
      );
    });
  }
}
