// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';
// import 'package:share_plus/share_plus.dart';

// class EarningsScreen extends StatefulWidget {
//   const EarningsScreen({super.key});

//   @override
//   State<EarningsScreen> createState() => _EarningsScreenState();
// }

// class _EarningsScreenState extends State<EarningsScreen> {
//   @override
//   void initState() {
//     super.initState();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       context.read<ReferralVM>()
//         ..getReferralEarnings()
//         ..getReferrals();
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     final referralVm = context.watch<ReferralVM>();
//     final authVm = context.watch<AuthUserVM>();
//     return BusyOverlay(
//       show: referralVm.isBusy,
//       child: Scaffold(
//         body: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Stack(
//               clipBehavior: Clip.none,
//               children: [
//                 Container(
//                   height: Sizer.height(160),
//                   decoration: BoxDecoration(
//                     color: AppColors.primaryBlue90,
//                   ),
//                   child: Padding(
//                     padding: EdgeInsets.only(
//                       top: Sizer.height(40),
//                       left: Sizer.width(20),
//                     ),
//                     child: Row(
//                       children: [
//                         InkWell(
//                           onTap: () {
//                             Navigator.pop(context);
//                           },
//                           child: Icon(
//                             Iconsax.arrow_left_2,
//                             color: AppColors.white,
//                             size: Sizer.height(24),
//                           ),
//                         ),
//                         XBox(20),
//                         Text(
//                           'Earnings',
//                           style: AppTypography.text22.semiBold.copyWith(
//                             color: Colors.white,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//                 Positioned(
//                   bottom: -50,
//                   left: 0,
//                   right: 0,
//                   child: Padding(
//                     padding: EdgeInsets.symmetric(
//                       horizontal: Sizer.width(30),
//                     ),
//                     child: Container(
//                       padding: EdgeInsets.symmetric(
//                         horizontal: Sizer.width(28),
//                         vertical: Sizer.height(16),
//                       ),
//                       decoration: BoxDecoration(
//                         color: AppColors.white,
//                         border: Border.all(
//                           color: AppColors.blue5FF,
//                         ),
//                         borderRadius: BorderRadius.circular(Sizer.radius(6)),
//                       ),
//                       child: IntrinsicHeight(
//                         child: Row(
//                           mainAxisSize: MainAxisSize.min,
//                           children: [
//                             Expanded(
//                               child: Text(
//                                 "Bonuses Earned",
//                                 style: AppTypography.text14.copyWith(
//                                   color: AppColors.gray93,
//                                 ),
//                               ),
//                             ),
//                             VerticalDivider(
//                               color: AppColors.blue5FF,
//                             ),
//                             Expanded(
//                               child: Center(
//                                 child: Text(
//                                   "\$2,000",
//                                   style: AppTypography.text26.semiBold.copyWith(
//                                     color: AppColors.primaryBlue,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             Expanded(
//               child: Padding(
//                 padding: EdgeInsets.symmetric(
//                   horizontal: Sizer.width(30),
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     YBox(80),
//                     Text(
//                       "RECENT EARNINGS",
//                       style: AppTypography.text14.copyWith(
//                         fontWeight: FontWeight.w300,
//                         color: AppColors.gray79,
//                       ),
//                     ),
//                     YBox(20),
//                     Expanded(
//                       child: Builder(builder: (context) {
//                         if (referralVm.completedRefList.isEmpty) {
//                           return Column(
//                             children: [
//                               SizedBox(
//                                 height: Sizer.height(300),
//                                 child: Center(
//                                   child: EmptyState(
//                                     title: "No Invites yet",
//                                     subtitle:
//                                         "You haven’t sent out any invites yet, Click the button below to share your referral code with friends",
//                                   ),
//                                 ),
//                               ),
//                               YBox(40),
//                               CustomBtn.solid(
//                                 onTap: () async {
//                                   var configVM = context.read<ConfigVM>();
//                                   await Share.share(
//                                     referalShare(
//                                       refCode: authVm.user?.referralCode,
//                                       refLink:
//                                           'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
//                                       refBonusAmt: configVM.referralBonusAmount,
//                                       refamtForReferral:
//                                           configVM.amtForReferral,
//                                     ),
//                                     subject: "Korrency",
//                                   );

//                                   MixpanelService().track(
//                                       'Referral Code Shared',
//                                       properties: {
//                                         "referral_code":
//                                             authVm.user?.referralCode,
//                                         "referral_link":
//                                             'https://korrency.onelink.me/9BIc?referrer=${authVm.user?.referralCode}',
//                                         "referral_amount":
//                                             configVM.referralBonusAmount,
//                                         "time": DateTime.now().toIso8601String()
//                                       });
//                                 },
//                                 borderRadius:
//                                     BorderRadius.circular(Sizer.radius(20)),
//                                 text: "Share",
//                               ),
//                             ],
//                           );
//                         }
//                         return ListView.separated(
//                           shrinkWrap: true,
//                           padding: EdgeInsets.zero,
//                           itemBuilder: (ctx, i) {
//                             Referral pendingRef =
//                                 referralVm.completedRefList[i];
//                             return RefListTile(
//                               name: pendingRef.referredUserFullName ?? '',
//                               isClaimed: false,
//                               status: pendingRef.status ?? '',
//                               onTap: () {},
//                             );
//                           },
//                           separatorBuilder: (_, __) => const YBox(30),
//                           itemCount: referralVm.completedRefList.length,
//                         );
//                       }),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
