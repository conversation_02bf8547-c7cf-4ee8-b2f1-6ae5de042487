// import 'package:korrency/core/lib/lib.dart';
// import 'package:korrency/ui/components/components.dart';
// import 'package:lottie/lottie.dart';

// class ModalNotifcation extends StatefulWidget {
//   const ModalNotifcation({
//     Key? key,
//     this.type = ResType.fail,
//     required this.heading,
//   }) : super(key: key);

//   final ResType type;
//   final String heading;

//   @override
//   State<ModalNotifcation> createState() => _ModalNotifcationState();
// }

// class _ModalNotifcationState extends State<ModalNotifcation> {
//   @override
//   Widget build(BuildContext context) {
//     return ContainerWithTopBorderRadius(
//       height: Sizer.screenHeight * 0.44,
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           const YBox(10),
//           InkWell(
//             onTap: () {
//               Navigator.pop(context);
//             },
//             child: Container(
//               alignment: Alignment.centerRight,
//               child: Icon(
//                 Icons.close,
//                 size: Sizer.width(25),
//                 color: AppColors.gray500,
//               ),
//             ),
//           ),
//           const YBox(20),
//           Lottie.asset(
//             disPlayLottie(),
//             fit: BoxFit.fill,
//             // controller: LottieController(vsync: this),
//             height: Sizer.height(150),
//           ),
//           const YBox(16),
//           Text(
//             widget.heading,
//             style: AppTypography.text16.copyWith(
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   String disPlayLottie() {
//     switch (widget.type) {
//       case ResType.success:
//         return AppLottie.success;
//       case ResType.fail:
//         return AppLottie.error;
//       default:
//         return AppLottie.error;
//     }
//   }
// }
