class NewCustomerRateModel {
  final bool? newCustomerRateActive;
  final bool? userIsEligible;
  final RateData? rateData;

  NewCustomerRateModel({
    this.newCustomerRateActive,
    this.userIsEligible,
    this.rateData,
  });

  factory NewCustomerRateModel.fromJson(Map<String, dynamic> json) =>
      NewCustomerRateModel(
        newCustomerRateActive: json["new_customer_rate_active"],
        userIsEligible: json["user_is_eligible"],
        rateData: json["rate_data"] == null
            ? null
            : RateData.fromJson(json["rate_data"]),
      );
}

class RateData {
  final String? newCustomerRateTransactionAmount;
  final String? currencyCode;

  RateData({
    this.newCustomerRateTransactionAmount,
    this.currencyCode,
  });

  factory RateData.fromJson(Map<String, dynamic> json) => RateData(
        newCustomerRateTransactionAmount:
            json["new_customer_rate_transaction_amount"],
        currencyCode: json["currency_code"],
      );
}
