import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/beneficiary/bank_transfer_screen.dart';

class BeneficiaryPreferredMethodScreen extends StatelessWidget {
  const BeneficiaryPreferredMethodScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Preferred Method',
        ),
        body: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(30),
              Builder(builder: (context) {
                final payMethods = currency.paymentMethods
                    ?.where((method) => method.id != 'korrency_user')
                    .toList();
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, i) {
                    final paymentMethod = payMethods?[i];
                    return CustomListViews.view(
                      isSvg: true,
                      isNetworkSvg: true,
                      icon: paymentMethod?.icon ?? "",
                      title: paymentMethod?.name ?? "",
                      onTap: () {
                        printty('Single item $paymentMethod');
                        if (paymentMethod?.id == TransferMethod.bankTransfer) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.bankTransferScreen,
                            arguments: BeneficiaryPaymentArg(
                              currency: currency,
                              paymentMethod: paymentMethod!,
                            ),
                          );
                        } else if (paymentMethod?.id ==
                            TransferMethod.mobileMoney) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.mobileMoneyScreen,
                            arguments: BeneficiaryPaymentArg(
                              currency: currency,
                              paymentMethod: paymentMethod!,
                            ),
                          );
                        } else if (paymentMethod?.id == TransferMethod.iban) {
                          Navigator.pushNamed(
                            context,
                            RoutePath.ibanTransferScreen,
                            arguments: BeneficiaryPaymentArg(
                              currency: currency,
                              paymentMethod: paymentMethod!,
                            ),
                          );
                        }
                      },
                    );
                  },
                  separatorBuilder: (_, __) => const YBox(24),
                  itemCount: payMethods?.length ?? 0,
                );
              })
            ],
          ),
        ),
      );
    });
  }
}
