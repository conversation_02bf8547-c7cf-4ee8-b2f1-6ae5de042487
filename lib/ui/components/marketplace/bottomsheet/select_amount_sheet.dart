import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectAmountSheet extends StatefulWidget {
  const SelectAmountSheet({Key? key}) : super(key: key);

  @override
  State<SelectAmountSheet> createState() => _SelectAmountSheetState();
}

class _SelectAmountSheetState extends State<SelectAmountSheet> {
  final List<String> _amountList = [
    "500",
    "1000",
  ];
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      child: Consumer<CreateOfferVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(20),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (ctx, i) {
                final amount = _amountList[i];
                return InkWell(
                  onTap: () {
                    vm.setPartialAmt(amount);
                    Navigator.pop(context);
                  },
                  child: ContainerWithBluewishBg(
                    padding: EdgeInsets.symmetric(
                      vertical: Sizer.height(10),
                      horizontal: Sizer.width(16),
                    ),
                    child: WalletListTile(
                      title: "$amount CAD",
                      trailingIconSize: 16,
                      isSelected: vm.partialAmt == amount,
                    ),
                  ),
                );
              },
              separatorBuilder: (ctx, _) => const YBox(16),
              itemCount: _amountList.length,
            ),
            const YBox(50),
          ],
        );
      }),
    );
  }
}
