# Backend API Endpoint for FCM Token Updates

To properly handle FCM token updates on your backend, implement the following endpoint:

## Endpoint: `/auth/update-device-token`

### Method: POST

### Headers:
- Authorization: Bearer {access_token}
- Content-Type: application/json

### Request Body:
```json
{
  "device_token": "your_fcm_token_here"
}
```

### Response:
```json
{
  "success": true,
  "message": "Device token updated successfully"
}
```

### Implementation Notes:

1. This endpoint should update the FCM token for the current user in your database
2. Make sure to validate the token format before saving
3. If a user has multiple devices, maintain a list of tokens per user
4. When sending notifications, try all tokens for a user until one succeeds
5. If a token fails with an "unknown target" error, remove it from your database

### Error Handling:

When Firebase returns an "unknown target" error, it typically means one of the following:
- The token is no longer valid (device uninstalled the app)
- The token was generated for a different Firebase project
- The token was generated for a different app package name

Your notification sending code should handle these errors by:
1. Removing invalid tokens from your database
2. Logging the specific error for debugging
3. Continuing to try other tokens for the same user if available

This approach ensures that even if some tokens become invalid, users will still receive notifications on their other devices.
