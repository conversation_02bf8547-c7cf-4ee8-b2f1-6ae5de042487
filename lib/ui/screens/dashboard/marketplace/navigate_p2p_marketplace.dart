import 'package:korrency/core/core.dart';
import 'package:korrency/ui/screens/screens.dart';

class NavigateP2pMarketplace extends StatelessWidget {
  const NavigateP2pMarketplace({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<DashboardVM>(
      builder: (context, vm, _) {
        return vm.hasSeenP2pWelcomeScreen
            ? const MarketPlaceScreen()
            : const P2pWelcomeScreen();
      },
    );
  }
}
