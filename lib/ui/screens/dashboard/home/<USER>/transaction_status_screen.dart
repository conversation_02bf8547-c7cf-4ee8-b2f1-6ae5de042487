import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TransactionStatusScreen extends StatelessWidget {
  const TransactionStatusScreen({
    super.key,
    required this.arg,
  });

  final ConfirmationArg arg;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const YBox(20),
              const ArrowBack(),
              const YBox(30),
              Expanded(
                child: ConfirmationBody(arg: arg),
              ),
              const YBox(70),
              CustomBtn.solid(
                onTap: arg.onBtnTap,
                online: true,
                text: arg.buttonText,
              ),
              const YBox(16),
              if (arg.outlineBtn != null)
                CustomBtn.solid(
                  height: Sizer.height(56),
                  isOutline: true,
                  textColor: AppColors.primaryBlue,
                  onTap: arg.outlineBtn,
                  online: true,
                  text: arg.outlineBtnText ?? "Cancel",
                ),
              const YBox(70),
            ],
          ),
        ),
      ),
    );
  }
}
