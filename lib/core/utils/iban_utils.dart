class IbanParts {
  final String countryCode;
  final String checkDigits;
  final String bankCode;
  final String sortCode;
  final String accountNumber;

  IbanParts({
    required this.countryCode,
    required this.checkDigits,
    required this.bankCode,
    required this.sortCode,
    required this.accountNumber,
  });

  @override
  String toString() {
    return 'IbanParts(countryCode: $countryCode, checkDigits: $checkDigits, bankCode: $bankCode, sortCode: $sortCode, accountNumber: $accountNumber)';
  }
}

/// Extracts parts of a UK IBAN as shown in the screenshot:
/// - Country code: 2 letters (e.g. "GB")
/// - Check digits: 2 digits
/// - Bank code: 4 alphanumeric characters
/// - Sort code: 6 digits
/// - Account number: 8 digits
///
/// Accepts IBAN with or without spaces and returns [IbanParts].
/// Throws [FormatException] if the IBAN is not a valid UK IBAN.
IbanParts parseUkIban(String iban) {
  final sanitized = iban.replaceAll(RegExp(r"\s+"), '').toUpperCase();

  // UK IBAN length is 22 characters
  if (sanitized.length != 22) {
    throw const FormatException('Invalid IBAN length for UK (expected 22)');
  }

  final countryCode = sanitized.substring(0, 2);
  final checkDigits = sanitized.substring(2, 4);
  final bankCode = sanitized.substring(4, 8);
  final sortCode = sanitized.substring(8, 14);
  final accountNumber = sanitized.substring(14, 22);

  // Basic validations following the UK structure
  final isCountryValid = RegExp(r'^[A-Z]{2}$').hasMatch(countryCode);
  // Allow digits or masked letters (e.g., "XX") in examples
  final isCheckDigitsValid = RegExp(r'^[A-Z0-9]{2}$').hasMatch(checkDigits);
  final isBankCodeValid = RegExp(r'^[A-Z0-9]{4}$').hasMatch(bankCode);
  final isSortCodeValid = RegExp(r'^\d{6}$').hasMatch(sortCode);
  final isAccountValid = RegExp(r'^\d{8}$').hasMatch(accountNumber);

  if (!isCountryValid ||
      !isCheckDigitsValid ||
      !isBankCodeValid ||
      !isSortCodeValid ||
      !isAccountValid) {
    throw const FormatException('IBAN does not match expected UK format');
  }

  return IbanParts(
    countryCode: countryCode,
    checkDigits: checkDigits,
    bankCode: bankCode,
    sortCode: sortCode,
    accountNumber: accountNumber,
  );
}

String? getAccountNumber(String iban) {
  try {
    return parseUkIban(iban).accountNumber;
  } catch (_) {
    // Fallback: show raw IBAN if parsing fails
    return iban;
  }
}

String? getSortCodeNumber(String iban) {
  try {
    return parseUkIban(iban).sortCode;
  } catch (_) {
    // Fallback: show raw IBAN if parsing fails
    return iban;
  }
}
