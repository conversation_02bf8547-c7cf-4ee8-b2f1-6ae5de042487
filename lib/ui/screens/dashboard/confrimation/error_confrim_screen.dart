import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/buttons/buttons.dart';
import 'package:korrency/ui/components/dashboard/custom_header.dart';

class ErrorConfirmScreen extends StatelessWidget {
  const ErrorConfirmScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: Safe<PERSON><PERSON>(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
              .copyWith(top: Sizer.height(20)),
          child: Column(
            children: [
              Container(
                alignment: Alignment.bottomLeft,
                child: const ArrowBack(),
              ),
              const <PERSON><PERSON><PERSON>(52),
              image<PERSON><PERSON><PERSON>(
                AppImages.circleError,
                height: Sizer.height(213),
                width: Sizer.width(213),
              ),
              const Y<PERSON><PERSON>(82),
              Text(
                "Trusted Device Set Successfully",
                style: AppTypography.text20.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              CustomBtn.solid(
                onTap: () {
                  Navigator.pop(context);
                },
                online: true,
                text: "Home",
              ),
              const YBox(90),
            ],
          ),
        ),
      ),
    );
  }
}
