import 'package:korrency/core/constants/constant_const.dart';

enum TransferMethodType {
  korrency,
  interac,
  bankTransfer,
  mobileMoney,
  iban,
}

extension TransferMethodTypeExtension on TransferMethodType {
  String get text {
    switch (this) {
      case TransferMethodType.korrency:
        return "Korrency";
      case TransferMethodType.interac:
        return "Interac e-Transfer";
      case TransferMethodType.bankTransfer:
        return "Bank Transfer";
      case TransferMethodType.mobileMoney:
        return "Mobile Money";
      case TransferMethodType.iban:
        return "IBAN";
    }
  }
}

String? getTransferMethod(String? text) {
  if (text == null) return null;

  final methodMap = {
    TransferMethod.korrency: TransferMethodType.korrency,
    TransferMethod.interac: TransferMethodType.interac,
    TransferMethod.bankTransfer: TransferMethodType.bankTransfer,
    TransferMethod.mobileMoney: TransferMethodType.mobileMoney,
    TransferMethod.iban: TransferMethodType.iban,
  };

  return methodMap[text]?.text;
}
