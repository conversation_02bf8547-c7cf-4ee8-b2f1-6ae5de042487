## Suppress missing Javax StAX classes used by transitive dependencies
-dontwarn javax.xml.stream.**

## Suppress warnings and allow optimization for Apache Tika references
-dontwarn org.apache.tika.**

## Keep classes if referenced reflectively by plugins (safe, conservative)
-keep class org.apache.tika.** { *; }

## General safe keeps to avoid over-shrinking common Android reflection usage
-keep class androidx.** { *; }
-keep class com.google.gson.** { *; }
-keep class kotlin.** { *; }