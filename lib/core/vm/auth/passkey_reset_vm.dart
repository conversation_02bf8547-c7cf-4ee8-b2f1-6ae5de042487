import 'package:korrency/core/core.dart';

class PasskeyResetVM extends BaseVM {
  TextEditingController otpC = TextEditingController();
  TextEditingController emailC = TextEditingController();

  TextEditingController oldPasswordC = TextEditingController();
  TextEditingController passwordC = TextEditingController();
  TextEditingController passwordConfirmC = TextEditingController();

  ValidatorStatus _validatorStatus = ValidatorStatus();
  ValidatorStatus get validatorStatus => _validatorStatus;

  bool _isMatchingPassword = false;
  bool get isMatchingPassword => _isMatchingPassword;

  bool _isValidEmail = false;
  bool get isValidEmail => _isValidEmail;

  bool get passWordDontMatch {
    return passwordC.text.isNotEmpty &&
        passwordConfirmC.text.isNotEmpty &&
        passwordC.text != passwordConfirmC.text;
  }

  validatePassword(String password) {
    _validatorStatus = PasswordValidatorService().validate(password);
    reBuildUI();
  }

  passwordMatch(String password, String confirmPassword) {
    _isMatchingPassword =
        PasswordValidatorService().passwordMatch(password, confirmPassword) &&
            password.isNotEmpty;
    reBuildUI();
  }

  emailIsValid() {
    _isValidEmail = emailC.text.isNotEmpty &&
        emailC.text.contains("@") &&
        emailC.text.contains(".");
    reBuildUI();
  }

  optBtnsValid() {
    return otpC.text.trim().length == 6;
  }

  enableSavePasswordBtn() {
    return _isMatchingPassword && validatorStatus.isValid && !isBusy;
  }

  Future<ApiResponse> forgotPasswordOtpRequest() async {
    return await performApiCall(
      url: "/auth/password/request-otp",
      method: apiService.post,
      body: {
        "recipient": emailC.text.trim(),
        "verification_type": "email_verification",
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> resetPassword() async {
    return await performApiCall(
      url: "/auth/password/reset",
      method: apiService.post,
      body: {
        "recipient": emailC.text.trim(),
        "password": passwordC.text.trim(),
        "password_confirmation": passwordConfirmC.text.trim(),
        "code": otpC.text.trim()
      },
      onSuccess: (data) {
        clearData();
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> changePassword() async {
    return await performApiCall(
      url: "/auth/password/change",
      method: apiService.postWithAuth,
      body: {
        "current_password": oldPasswordC.text.trim(),
        "password": passwordC.text.trim(),
        "password_confirmation": passwordConfirmC.text.trim(),
      },
      onSuccess: (data) {
        clearData();
        return apiResponse;
      },
    );
  }

  clearData() {
    _isValidEmail = false;
    _isMatchingPassword = false;
    _validatorStatus = ValidatorStatus();

    otpC.clear();
    emailC.clear();
    oldPasswordC.clear();
    passwordC.clear();
    passwordConfirmC.clear();

    reBuildUI();
  }

  @override
  void dispose() {
    printty('PasskeyResetVM Disposed');

    otpC.dispose();
    emailC.dispose();
    oldPasswordC.dispose();
    passwordC.dispose();
    passwordConfirmC.dispose();

    super.dispose();
  }
}
