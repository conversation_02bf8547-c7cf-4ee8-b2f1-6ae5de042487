import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class EmailAndPasswordScreen extends StatefulWidget {
  const EmailAndPasswordScreen({super.key});

  @override
  State<EmailAndPasswordScreen> createState() => _EmailAndPasswordScreenState();
}

class _EmailAndPasswordScreenState extends State<EmailAndPasswordScreen> {
  final _emailFocusNode = FocusNode();
  final _userNameFocusNode = FocusNode();
  final _firstName = FocusNode();
  final _lastName = FocusNode();
  final _middleName = FocusNode();
  final _dob = FocusNode();

  DateTime? selectedDate = DateTime.now();

  // Track if user has started typing in each field
  bool _hasTypedFirstName = false;
  bool _hasTypedMiddleName = false;
  bool _hasTypedLastName = false;

  // Validation functions for name fields
  String? _validateSingleWordName(
      String value, String fieldName, bool hasTyped) {
    if (!hasTyped)
      return null; // Don't show validation until user starts typing

    if (value.isEmpty) {
      return "$fieldName is required";
    }
    if (value.trim().contains(' ')) {
      return "$fieldName should be a single word only";
    }
    return null;
  }

  String? _validateOptionalSingleWordName(
      String value, String fieldName, bool hasTyped) {
    if (!hasTyped) {
      return null; // Don't show validation until user starts typing
    }

    if (value.isNotEmpty && value.trim().contains(' ')) {
      return "$fieldName should be a single word only";
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OnBoardVM>().getDeepLinkValueFromStorage();
    });
  }

  @override
  void dispose() {
    _emailFocusNode.dispose();
    _userNameFocusNode.dispose();
    _firstName.dispose();
    _lastName.dispose();
    _middleName.dispose();
    _dob.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Setup your Account',
            onBackBtnTap: () {
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Tell us a bit about yourself to setup your account ",
                          style: AppTypography.text15
                              .copyWith(color: AppColors.gray93),
                        ),
                        const YBox(40),
                        CustomTextField(
                          labelText: "Username",
                          showLabelHeader: true,
                          focusNode: _userNameFocusNode,
                          controller: vm.userNameC,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          errorText:
                              vm.userNameC.text.isNotEmpty && !vm.userNameValid
                                  ? "Username is required"
                                  : null,
                          onChanged: (val) => vm.isUserNameValid(),
                          onSubmitted: (p0) {
                            _userNameFocusNode.unfocus();
                            _emailFocusNode.requestFocus();
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "First Name",
                          showLabelHeader: true,
                          focusNode: _firstName,
                          controller: vm.firstname,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          errorText: _validateSingleWordName(vm.firstname.text,
                              "First Name", _hasTypedFirstName),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[a-zA-Z]')),
                          ],
                          onChanged: (val) {
                            _hasTypedFirstName = true;
                            AppUtils.handleTextCapitalization(
                                vm.firstname, val);
                            setState(() {});
                          },
                          onSubmitted: (p0) {
                            _firstName.unfocus();
                            _middleName.requestFocus();
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Middle Name (Optional)",
                          showLabelHeader: true,
                          focusNode: _middleName,
                          controller: vm.middlename,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          errorText: _validateOptionalSingleWordName(
                              vm.middlename.text,
                              "Middle Name",
                              _hasTypedMiddleName),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[a-zA-Z]')),
                          ],
                          onChanged: (val) {
                            _hasTypedMiddleName = true;
                            AppUtils.handleTextCapitalization(
                                vm.middlename, val);
                            setState(() {});
                          },
                          onSubmitted: (p0) {
                            _middleName.unfocus();
                            _lastName.requestFocus();
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Last Name",
                          showLabelHeader: true,
                          focusNode: _lastName,
                          controller: vm.lastName,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.user,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          errorText: _validateSingleWordName(
                              vm.lastName.text, "Last Name", _hasTypedLastName),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[a-zA-Z]')),
                          ],
                          onChanged: (val) {
                            _hasTypedLastName = true;
                            AppUtils.handleTextCapitalization(vm.lastName, val);
                            setState(() {});
                          },
                          onSubmitted: (p0) {
                            _lastName.unfocus();
                            _emailFocusNode.requestFocus();
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Email",
                          showLabelHeader: true,
                          focusNode: _emailFocusNode,
                          controller: vm.emailC,
                          errorText:
                              vm.emailC.text.isNotEmpty && !vm.isValidEmail
                                  ? "Invalid Email"
                                  : null,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.sms,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          onChanged: (val) => vm.emailIsValid(),
                          onSubmitted: (p0) {
                            _emailFocusNode.unfocus();
                            _dob.requestFocus();
                          },
                        ),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Date of Birth",
                          showLabelHeader: true,
                          focusNode: _dob,
                          controller: vm.dateOfBirth,
                          borderRadius: Sizer.height(12),
                          isReadOnly: true,
                          prefixIcon: Padding(
                            padding: EdgeInsets.all(Sizer.radius(12)),
                            child: Icon(
                              Iconsax.calendar_1,
                              color: AppColors.gray500,
                              size: Sizer.height(20),
                            ),
                          ),
                          onChanged: (val) => vm.emailIsValid(),
                          onSubmitted: (p0) {
                            _dob.unfocus();
                          },
                          onTap: () {
                            showCupertinoDatePicker(
                              context,
                              onDateTimeChanged: (val) {
                                selectedDate = val;
                              },
                              onDone: () {
                                if (selectedDate != null) {
                                  // Calculate age
                                  DateTime currentDate = DateTime.now();
                                  int age =
                                      currentDate.year - selectedDate!.year;
                                  if (currentDate.month < selectedDate!.month ||
                                      (currentDate.month ==
                                              selectedDate!.month &&
                                          currentDate.day <
                                              selectedDate!.day)) {
                                    age--;
                                  }
                                  // Check if age is 18 or above
                                  if (age >= 18) {
                                    // Do something with the selected date
                                    printty('Selected date: $selectedDate');
                                    vm.formattedDobC.text =
                                        AppUtils.dayWithSuffixMonthAndYear(
                                            selectedDate!);
                                    vm.dateOfBirth.text = selectedDate!
                                        .toIso8601String()
                                        .split("T")
                                        .first;
                                    Navigator.pop(context);
                                    setState(() {});
                                  } else {
                                    FlushBarToast.fLSnackBar(
                                      message: 'Age must be 18 or above',
                                    );
                                  }
                                }
                              },
                            );
                          },
                        ),
                        const YBox(60),
                      ],
                    ),
                  ),
                ),
                CustomBtn.withChild(
                  onTap: verifyEmailOtp,
                  online: vm.signupBtnIsValid(),
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(isOnline: vm.signupBtnIsValid()),
                ),
                // CustomBtn.solid(
                //   onTap: () {
                //     Navigator.pushNamed(context, RoutePath.verifyEmailScreen);
                //   },
                //   height: 65,
                //   text: "Continue",
                // ),
                const YBox(30),
              ],
            ),
          ),
        ),
      );
    });
  }

  verifyEmailOtp() async {
    FocusScope.of(context).unfocus();
    context.read<OnBoardVM>().requestOtp(otpType: OtpType.email).then((value) {
      if (value.success) {
        _moveToVerifyEmailScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _moveToVerifyEmailScreen() {
    Navigator.pushReplacementNamed(context, RoutePath.verifyEmailScreen);
  }
}
