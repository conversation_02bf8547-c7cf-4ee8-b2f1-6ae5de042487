import 'package:korrency/core/core.dart';

class BalanceWidgetText extends StatelessWidget {
  const BalanceWidgetText({
    super.key,
    required this.balance,
  });

  final String balance;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: 'Balance: ',
        style: AppTypography.text12.copyWith(
          color: AppColors.gray79,
          fontFamily: "Outfit",
          fontWeight: FontWeight.w300,
        ),
        children: [
          TextSpan(
            text: AppUtils.formatAmountDoubleString(balance),
            style: AppTypography.text12.copyWith(
              color: AppColors.mainBlack,
              fontFamily: "Outfit",
              fontWeight: FontWeight.w300,
            ),
          ),
        ],
      ),
    );
  }
}
