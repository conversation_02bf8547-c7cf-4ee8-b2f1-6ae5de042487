# Searchable Dropdown Widget

A reusable Flutter widget that provides a searchable dropdown functionality with a clean, modern design that matches the app's styling patterns.

## Features

- **Search functionality**: Users can type to filter options
- **Dropdown display**: Shows filtered options in an overlay
- **Customizable styling**: Matches the app's design system
- **Generic type support**: Works with any data type
- **Keyboard navigation**: Proper focus handling
- **Error handling**: Supports error states and validation
- **Responsive design**: Uses the app's Sizer utility for consistent sizing

## Usage

### Basic Usage

```dart
SearchableDropdownWidget<String>(
  labelText: "Occupation",
  hintText: "Search occupation",
  showLabelHeader: true,
  items: occupationItems,
  selectedValue: selectedOccupation,
  borderRadius: Sizer.height(12),
  onChanged: (value) {
    setState(() {
      selectedOccupation = value;
    });
  },
)
```

### Creating Items

```dart
final List<SearchableDropdownItem<String>> occupationItems = [
  const SearchableDropdownItem(
    value: 'construction_manager',
    label: 'Construction Manager',
  ),
  const SearchableDropdownItem(
    value: 'marketing_manager',
    label: 'Marketing Manager',
  ),
  // Add more items...
];
```

### With Icons

```dart
final List<SearchableDropdownItem<String>> itemsWithIcons = [
  SearchableDropdownItem(
    value: 'manager',
    label: 'Manager',
    icon: Icon(Icons.person, size: 20),
  ),
];
```

## Properties

| Property | Type | Description | Default |
|----------|------|-------------|---------|
| `labelText` | `String?` | Label text displayed above the field | `null` |
| `hintText` | `String?` | Placeholder text | `null` |
| `items` | `List<SearchableDropdownItem<T>>` | List of dropdown items | Required |
| `selectedValue` | `T?` | Currently selected value | `null` |
| `onChanged` | `Function(T?)?` | Callback when selection changes | `null` |
| `errorText` | `String?` | Error message to display | `null` |
| `showLabelHeader` | `bool` | Whether to show the label header | `false` |
| `borderRadius` | `double` | Border radius for the field | `0` |
| `fillColor` | `Color?` | Background color of the field | `Colors.transparent` |
| `borderColor` | `Color?` | Border color | `AppColors.gray500` |
| `prefixIcon` | `Widget?` | Icon to show at the start of the field | `null` |
| `isOptional` | `bool` | Whether the field is optional | `false` |
| `optionalText` | `String?` | Custom optional text | `null` |
| `labelSize` | `double?` | Size of the label text | `14` |
| `labelColor` | `Color?` | Color of the label text | `AppColors.gray600` |

## SearchableDropdownItem Properties

| Property | Type | Description | Default |
|----------|------|-------------|---------|
| `value` | `T` | The actual value of the item | Required |
| `label` | `String` | Display text for the item | Required |
| `icon` | `Widget?` | Optional icon to display | `null` |

## Example Implementation

See `searchable_dropdown_example.dart` for a complete example showing how to use the widget with occupation and gender data.

## Styling

The widget automatically uses the app's design system:
- Colors from `AppColors`
- Typography from `AppTypography`
- Responsive sizing with `Sizer`
- Consistent with other form fields in the app

## Integration

The widget is already integrated into the app's component system:
- Exported in `textfields.dart`
- Available through `components.dart`
- Used in `complete_profile_screen.dart`

## Notes

- The dropdown overlay automatically positions itself below the input field
- Search is case-insensitive and matches partial strings
- The widget handles focus management automatically
- Supports both keyboard and touch interactions
- Follows the app's accessibility guidelines