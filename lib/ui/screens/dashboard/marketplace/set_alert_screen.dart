import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SetAlertScreen extends StatefulWidget {
  const SetAlertScreen({Key? key}) : super(key: key);

  @override
  State<SetAlertScreen> createState() => _SetAlertScreenState();
}

class _SetAlertScreenState extends State<SetAlertScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() {
    context.read<MarketRateAlertVM>().getAlerts();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MarketRateAlertVM>(builder: (context, vm, _) {
      return Scaffold(
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(30),
                const AuthTextSubTitle(
                  title: "Set Alerts",
                  subtitle:
                      "Receive alerts for offers matching your target rate on the marketplace",
                ),
                const YBox(10),
                Expanded(
                  child: Builder(builder: (context) {
                    if (vm.isBusy) {
                      return const SetAlertShimmer();
                    }
                    if (vm.rateAlerts.isEmpty && !vm.isBusy) {
                      Center(
                        child: Text(
                          "No transactions yet",
                          style: AppTypography.text16.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.gray500),
                        ),
                      );
                    }
                    return RefreshIndicator(
                      onRefresh: () async {
                        await _init();
                      },
                      child: ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(
                          top: Sizer.height(20),
                        ),
                        itemBuilder: (ctx, i) {
                          MarketRateAlert alert = vm.rateAlerts[i];
                          return SecurityTile(
                            padding: EdgeInsets.zero,
                            bgColor: AppColors.transparent,
                            title:
                                '${_checkOfferType(offerType: alert.offerType ?? '', rate: alert.rate.toString())} and',
                            subTitle:
                                'Offer amount is between ${AppUtils.formatAmountDoubleString(alert.minimumAmount ?? '')} CAD - ${AppUtils.formatAmountDoubleString(alert.maximumAmount ?? '')} CAD',
                            subTitleTwo:
                                'Created ${AppUtils.dateAndTime(date: alert.createdAt ?? DateTime.now())}',
                            subTitleColor: AppColors.gray500,
                            showTrailing: true,
                            leadingIcon: AppSvgs.sellOffer,
                            trailingWidget: svgHelper(AppSvgs.trash),
                            onTrailingPress: () {
                              BsWrapper.bottomSheet(
                                context: context,
                                widget: ConfirmationSheet(
                                  title: 'Delete Alert',
                                  message:
                                      'Are you sure you want to delete this alert?',
                                  secondBtnText: 'Delete',
                                  secendBtnTap: () {
                                    _deleteAlert(alert.id ?? 0);
                                  },
                                ),
                              );
                            },
                          );
                        },
                        separatorBuilder: (ctx, _) => const YBox(30),
                        itemCount: vm.rateAlerts.length,
                      ),
                    );
                  }),
                ),
                CustomBtn.solid(
                  height: 56,
                  onTap: () {
                    Navigator.of(context)
                        .pushNamed(RoutePath.createAlertScreen);
                  },
                  // online: vm.btnIsValid(),
                  text: "Add Alert",
                ),
                const YBox(40),
              ],
            ),
          ),
        ),
      );
    });
  }

  _checkOfferType({required String offerType, required String rate}) {
    if (offerType == OfferConst.buyOffer) {
      return 'Buyer’s rate is above ${AppUtils.formatAmountDoubleString(rate)} CAD';
    } else {
      return 'Seller’s rate is below ${AppUtils.formatAmountDoubleString(rate)} CAD';
    }
  }

  _deleteAlert(int alertId) {
    Navigator.pop(context);
    var rateAlertVM = context.read<MarketRateAlertVM>();
    rateAlertVM.deleteAlert(alertId).then((value) {
      if (value.success) {
        _showConfirmationScreen(msg: value.message);
      } else {
        _showConfirmationScreen(msg: value.message, isFailed: true);
      }
    });
  }

  _showConfirmationScreen({String? msg, bool isFailed = false}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed ? "Something went wrong" : "Alert created successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
          if (!isFailed) {
            NavigatorKeys.appNavigatorKey.currentContext!
                .read<MarketRateAlertVM>()
                .getAlerts();
          }
        },
      ),
    );
  }
}

class SetAlertShimmer extends StatelessWidget {
  const SetAlertShimmer({
    Key? key,
    this.itemCount,
  }) : super(key: key);

  final int? itemCount;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(
          // horizontal: Sizer.width(24),
          vertical: Sizer.height(30),
        ),
        itemBuilder: (ctx, i) {
          return SecurityTile(
            padding: EdgeInsets.zero,
            bgColor: AppColors.transparent,
            title: 'Seller’s rate is below 1,200 CAD',
            subTitle: 'Created March 21, 2024',
            showTrailing: true,
            leadingIcon: AppSvgs.sellOffer,
            trailingWidget: svgHelper(AppSvgs.trash),
            onTap: () {},
          );
        },
        separatorBuilder: (ctx, _) => const YBox(26),
        itemCount: itemCount ?? 10,
      ),
    );
  }
}
