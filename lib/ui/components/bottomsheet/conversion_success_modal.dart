import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConversionSuccessModal extends StatefulWidget {
  const ConversionSuccessModal({
    super.key,
  });

  @override
  State<ConversionSuccessModal> createState() => _ConversionSuccessModalState();
}

class _ConversionSuccessModalState extends State<ConversionSuccessModal> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {});
  }

  String get fromAmount {
    final vm = context.read<ConvertMoneyVM>();
    return "${AppUtils.formatAmountDoubleString(
      vm.fromC.text.trim().toString().replaceAll(',', ''),
    )} ${vm.fromConvertWallet?.currency?.code}";
  }

  String get toAmount {
    final vm = context.read<ConvertMoneyVM>();
    return "${vm.toC.text} ${vm.toConvertWallet?.currency?.code}";
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.only(
              left: Sizer.width(20),
              right: Sizer.width(20),
            ),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const YBox(28),
                SvgPicture.asset(
                  AppSvgs.convertSuccess,
                  height: Sizer.height(200),
                ),
                const YBox(16),
                Text(
                  'Your Conversion was Successful',
                  style: FontTypography.text22.semiBold,
                ),
                YBox(4),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray93,
                      fontFamily: AppFont.outfit.family,
                    ),
                    children: [
                      const TextSpan(
                        text: "You have successfully exchanged \n",
                      ),
                      TextSpan(
                        text: fromAmount,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.primaryBlue,
                          fontFamily: AppFont.outfit.family,
                        ),
                      ),
                      const TextSpan(
                        text: " to ",
                      ),
                      TextSpan(
                        text: toAmount,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.primaryBlue,
                          fontFamily: AppFont.outfit.family,
                        ),
                      ),
                    ],
                  ),
                ),
                // YBox(24),
                // Container(
                //   padding: EdgeInsets.symmetric(
                //     horizontal: Sizer.width(12),
                //     vertical: Sizer.height(16),
                //   ),
                //   decoration: BoxDecoration(
                //     border: Border.all(
                //       color: AppColors.grayAEC,
                //     ),
                //     borderRadius: BorderRadius.circular(Sizer.radius(12)),
                //   ),
                //   child: Row(
                //     children: [
                //       Text(
                //         "Reference",
                //         style: AppTypography.text14.copyWith(
                //           color: AppColors.gray79,
                //         ),
                //       ),
                //       const Spacer(),
                //       Text(
                //         "1234567890",
                //         style: AppTypography.text14.copyWith(
                //           color: AppColors.gray79,
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
                YBox(50),
                CustomBtn.solid(
                  onTap: () {
                    var convertMoneyVM = context.read<ConvertMoneyVM>();
                    convertMoneyVM.resetData();
                    Navigator.pushNamedAndRemoveUntil(
                      NavigatorKeys.appNavigatorKey.currentContext!,
                      RoutePath.dashboardNav,
                      (route) => false,
                    );
                  },
                  online: true,
                  borderRadius: BorderRadius.circular(Sizer.radius(14)),
                  textStyle: FontTypography.text15.medium.withCustomColor(
                    AppColors.white,
                  ),
                  text: "Done",
                ),
                YBox(30),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
