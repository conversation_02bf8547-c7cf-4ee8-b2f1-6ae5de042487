import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/custom_containers.dart';

class YourOfferCard extends StatelessWidget {
  const YourOfferCard({
    Key? key,
    required this.deal,
    this.onTap,
  }) : super(key: key);

  final OfferDealActivity deal;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ContainerWithBluewishBg(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.only(
                      right: Sizer.width(21),
                    ),
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text:
                                "${deal.acceptingUserUsername} ${deal.offer?.type?.toLowerCase() == "sell offer" ? 'bought' : 'sold'}",
                            style: const TextStyle(
                              color: AppColors.textGray,
                              fontSize: 14,
                              height: 1.4,
                            ),
                          ),
                          TextSpan(
                            text:
                                " ${deal.currency?.symbol}${AppUtils.formatAmountDoubleString(deal.amount ?? "0")} ${deal.currency?.code} ",
                            style: AppTypography.text14.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.w700,
                              height: 1.4,
                            ),
                          ),
                          TextSpan(
                            text: "on your ongoing ${deal.offer?.type ?? ""}",
                            style: const TextStyle(
                              color: AppColors.textGray,
                              fontSize: 14,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const YBox(20),
                  Text(
                    AppUtils.formatDateTime(deal.createdAt.toString()),
                    style: AppTypography.text12.copyWith(
                      color: AppColors.textGray,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              "View",
              style: AppTypography.text14.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryBlue,
              ),
            )
          ],
        ),
      ),
    );
  }
}
