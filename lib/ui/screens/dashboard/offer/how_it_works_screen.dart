import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class HowItWorksScreen extends StatefulWidget {
  const HowItWorksScreen({Key? key}) : super(key: key);

  @override
  State<HowItWorksScreen> createState() => _HowItWorksScreenState();
}

class _HowItWorksScreenState extends State<HowItWorksScreen> {
  OfferType selectedOfferType = OfferType.sell;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                const YBox(20),
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    'How it Works',
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black900,
                    ),
                  ),
                ),
                // const CustomHeader(
                //   // showBackBtn: true,
                //   showHeader: true,
                //   headerText: 'How it Works',
                // ),
                const YBox(36),
                imageHelper(
                  AppImages.howitworks,
                  height: Sizer.height(235),
                  width: Sizer.width(2856),
                ),
                const YBox(40),
                Row(
                  children: [
                    Expanded(
                      child: TabBtn(
                        text: 'Sell Offers',
                        isActive: selectedOfferType == OfferType.sell,
                        onTap: () {
                          setState(() {
                            selectedOfferType = OfferType.sell;
                          });
                        },
                      ),
                    ),
                    const XBox(16),
                    Expanded(
                      child: TabBtn(
                        text: 'Buy Offers',
                        isActive: selectedOfferType == OfferType.buy,
                        onTap: () {
                          setState(() {
                            selectedOfferType = OfferType.buy;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const YBox(24),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Create ${selectedOfferType == OfferType.sell ? 'Sell' : 'Buy'} Offers',
                        style: AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.black900,
                        ),
                      ),
                      const YBox(4),
                      RichText(
                        text: TextSpan(
                          text: 'If you',
                          style: AppTypography.text14.copyWith(
                            color: AppColors.black800,
                            fontFamily: 'Inter',
                          ),
                          children: [
                            TextSpan(
                              text: ' have',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.w700,
                                fontFamily: 'Inter',
                              ),
                            ),
                            TextSpan(
                              text: ' NGN and you',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.black800,
                                fontFamily: 'Inter',
                              ),
                            ),
                            TextSpan(
                              text: ' need',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.primaryBlue,
                                fontWeight: FontWeight.w700,
                                fontFamily: 'Inter',
                              ),
                            ),
                            TextSpan(
                              text: ' CAD',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.black800,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(16),
                CustomTimeline(
                  step: '1',
                  title:
                      'Set up your ${selectedOfferType == OfferType.sell ? 'Sell' : 'Buy'} Offer',
                  subTitle:
                      'Enter the amount you need to ${selectedOfferType == OfferType.sell ? 'sell' : 'buy'} and your desired exchange rate.',
                ),
                const YBox(8),
                const CustomTimeline(
                  step: '2',
                  title: 'Post your Offer ',
                  subTitle:
                      'Review your offer for accuracy and post. Successfully posted offers get listed on the marketplace for interested users to accept.',
                ),
                const YBox(8),
                const CustomTimeline(
                  step: '3',
                  isEnd: true,
                  title: 'Offer accepted',
                  subTitle:
                      'The exchange will automatically execute, and funds will be credited to your wallet',
                ),
                const YBox(80),
                Consumer<DashboardVM>(builder: (context, vm, _) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ValidationItemWidget(
                        label: "Dont show me this again",
                        isValid: vm.dotNotShowOfferHowitWorksScreen,
                        onTap: () {
                          vm.toggleOfferHowitWorksScreen(
                              !vm.dotNotShowOfferHowitWorksScreen);
                        },
                      ),
                      const YBox(40),
                      CustomBtn.solid(
                        onTap: () {
                          vm.setHasSeenOfferHowitWorksScreen(true);
                        },
                        online: true,
                        text: "Continue",
                      ),
                    ],
                  );
                }),

                const YBox(100),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CustomTimeline extends StatelessWidget {
  const CustomTimeline({
    Key? key,
    this.isEnd = false,
    required this.title,
    required this.subTitle,
    required this.step,
  }) : super(key: key);

  final bool isEnd;
  final String title;
  final String subTitle;
  final String step;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: Sizer.width(25),
              height: Sizer.height(25),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
              ),
              child: Center(
                child: Text(
                  step,
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
            if (!isEnd) const YBox(8),
            if (!isEnd)
              Container(
                width: Sizer.width(2),
                height: Sizer.height(60),
                color: AppColors.primaryBlue,
              ),
          ],
        ),
        const XBox(16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text14.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.black900,
                ),
              ),
              const YBox(4),
              Text(
                subTitle,
                style: AppTypography.text14.copyWith(
                  color: AppColors.black800,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class TabBtn extends StatelessWidget {
  const TabBtn({
    Key? key,
    required this.text,
    this.isActive = false,
    this.onTap,
  }) : super(key: key);

  final String text;
  final bool isActive;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(10),
        ),
        decoration: BoxDecoration(
          color: isActive ? AppColors.primaryBlue : AppColors.gray100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            text,
            style: AppTypography.text14.copyWith(
              fontWeight: isActive ? FontWeight.w700 : FontWeight.w500,
              color: isActive ? AppColors.white : AppColors.darkBlue,
            ),
          ),
        ),
      ),
    );
  }
}
