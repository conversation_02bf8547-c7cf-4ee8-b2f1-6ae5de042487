// ignore_for_file: use_build_context_synchronously

import 'package:chewie/chewie.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerScreen extends StatefulWidget {
  const VideoPlayerScreen({
    super.key,
    required this.args,
  });

  final VideoPlayerArg args;

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  bool _isLoading = true;

  late VideoPlayerController _videoPlayerController;

  late ChewieController _chewieController;

  @override
  void initState() {
    super.initState();

    _init();
  }

  Future<void> _init() async {
    try {
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(
          widget.args.url,
        ),
      );
      await _videoPlayerController.initialize();

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController,
        autoPlay: true,
        looping: true,
        // Make sure this option is enabled
        allowMuting: true,
        // Start with sound on
        autoInitialize: true,
      );

      _isLoading = false;
      setState(() {});
    } on Exception catch (e) {
      printty('Error initializing video player: $e');
    }
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        headerText: "Video Player",
        showHeaderTitle: true,
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator.adaptive())
            : Chewie(
                controller: _chewieController,
              ),
      ),
    );
  }
}
