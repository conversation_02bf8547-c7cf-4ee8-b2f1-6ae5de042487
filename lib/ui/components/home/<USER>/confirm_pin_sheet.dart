// import 'package:korrency/core/core.dart';
// import 'package:korrency/ui/components/components.dart';
// import 'package:pinput/pinput.dart';

// // NOTE: This file is no longer in use;

// class ConfirmPinSheet extends StatefulWidget {
//   const ConfirmPinSheet({Key? key}) : super(key: key);

//   @override
//   State<ConfirmPinSheet> createState() => _ConfirmPinSheetState();
// }

// class _ConfirmPinSheetState extends State<ConfirmPinSheet> {
//   final FocusNode pinFocusNode = FocusNode();
//   @override
//   void initState() {
//     super.initState();
//     pinFocusNode.requestFocus();
//     KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
//   }

//   @override
//   dispose() {
//     pinFocusNode.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(
//         bottom: MediaQuery.of(context).viewInsets.bottom,
//       ),
//       child: ContainerWithTopBorderRadius(
//         height: Sizer.screenHeight * 0.63,
//         child: Consumer<ConvertMoneyVM>(builder: (context, vm, _) {
//           return Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               const YBox(20),
//               const AuthTextSubTitle(
//                 title: "Confirm PIN",
//                 subtitle:
//                     "To authorize this transaction, kindly enter \nyour PIN",
//               ),
//               const YBox(40),
//               Container(
//                 alignment: Alignment.center,
//                 child: Pinput(
//                   defaultPinTheme: PinInputTheme.defaultPinTheme(),
//                   // focusedPinTheme: PinInputTheme.focusFillPinTheme(),
//                   followingPinTheme: PinInputTheme.followPinTheme(),
//                   length: 4,
//                   controller: vm.pinC,
//                   focusNode: pinFocusNode,
//                   showCursor: true,
//                   obscureText: true,
//                   obscuringWidget: Container(
//                     padding: EdgeInsets.only(top: Sizer.height(8)),
//                     child: Text('*', style: AppTypography.text36),
//                   ),
//                   onChanged: (_) => vm.reBuildUI(),
//                   onCompleted: (pin) {
//                     pinFocusNode.unfocus();
//                     _convert();
//                   },
//                 ),
//               ),
//               const Spacer(),
//               CustomBtn.solid(
//                 onTap: () {
//                   pinFocusNode.unfocus();
//                   _convert();
//                 },
//                 online: vm.pinC.text.length == 4,
//                 isLoading: vm.isBusy,
//                 text: "Continue",
//               ),
//               const YBox(60),
//             ],
//           );
//         }),
//       ),
//     );
//   }

//   _convert() {
//     var convertMoneyVM = context.read<ConvertMoneyVM>();
//     convertMoneyVM.convertMoney().then((value) {
//       if (value.success) {
//         convertMoneyVM.resetData();

//         _getWalletCredential();
//         FlushBarToast.fLSnackBar(
//           message: value.message.toString(),
//         );
//         Future.delayed(const Duration(seconds: 3), () {
//           Navigator.pop(context);
//           Navigator.pop(context);
//         });
//       } else {
//         FlushBarToast.fLSnackBar(
//           message: value.message.toString(),
//           backgroundColor: AppColors.red,
//         );
//       }
//     });
//   }

//   _getWalletCredential() {
//     context.read<WalletVM>().getWallets();
//     context.read<AuthUserVM>().getAuthUser();
//     context.read<CurrencyVM>().getCurrencies();
//     context.read<TransactionVM>().getTransactions();
//   }
// }
