import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CurrencyWalletSheet extends StatefulWidget {
  const CurrencyWalletSheet({
    Key? key,
    required this.onWalletSelected,
  }) : super(key: key);

  final Function(Wallet wallet) onWalletSelected;

  @override
  State<CurrencyWalletSheet> createState() => _CurrencyWalletSheetState();
}

class _CurrencyWalletSheetState extends State<CurrencyWalletSheet> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(
      builder: (context, vm, _) {
        return ContainerWithTopBorderRadius(
          padding: EdgeInsets.zero,
          height: Sizer.screenHeight * 0.40,
          child: ListView(
            shrinkWrap: true,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              if (vm.walletList.isNotEmpty)
                Text(
                  "Available Wallets",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              if (vm.walletList.isNotEmpty) const YBox(24),
              if (vm.walletList.isNotEmpty)
                ListView.separated(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (ctx, i) {
                      Wallet wallet = vm.walletList[i];
                      return ContainerWithBluewishBg(
                        child: WalletListTile(
                            onTap: () {
                              widget.onWalletSelected(wallet);
                              Navigator.pop(context);
                            },
                            title: wallet.currency?.name ?? "",
                            subTitle:
                                "${AppUtils.formatAmountDoubleString(wallet.balance ?? "")} ${wallet.currency?.code}",
                            useNetworkSvg: true,
                            currencyIcon: wallet.currency?.flag ?? "",
                            showTrailing: true,
                            trailingWidget: const SizedBox.shrink()),
                      );
                    },
                    separatorBuilder: (ctx, _) => const YBox(24),
                    itemCount: vm.walletList.length),
            ],
          ),
        );
      },
    );
  }
}
