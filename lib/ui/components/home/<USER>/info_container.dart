import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/custom_containers.dart';

class InfoContainer extends StatelessWidget {
  const InfoContainer({
    Key? key,
    required this.text,
    this.padding,
  }) : super(key: key);

  final String text;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: ContainerWithBluewishBg(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(10),
        ),
        bgColor: AppColors.blue00,
        child: Row(
          children: [
            svgHelper(AppSvgs.docUpload, height: 24, width: 24),
            const XBox(10),
            Expanded(
              child: Text(
                text,
                style: const TextStyle(
                  color: AppColors.primaryBlue,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
