import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectGenderModal extends StatefulWidget {
  const SelectGenderModal({super.key, this.selectedGender});

  final String? selectedGender;

  @override
  State<SelectGenderModal> createState() => _SelectGenderModalState();
}

class _SelectGenderModalState extends State<SelectGenderModal> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Consumer<KycVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(25),
            InkWell(
              onTap: () {
                Navigator.pop(context, "Male");
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Male",
                  isSelected: widget.selectedGender == "Male",
                ),
              ),
            ),
            const YBox(16),
            InkWell(
              onTap: () {
                Navigator.pop(context, "Female");
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Female",
                  isSelected: widget.selectedGender == "Female",
                ),
              ),
            ),
            const YBox(16),
            InkWell(
              onTap: () {
                Navigator.pop(context, "Rather not say");
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Rather not say",
                  isSelected: widget.selectedGender == "Rather not say",
                ),
              ),
            ),
            const YBox(100),
          ],
        );
      }),
    );
  }
}
