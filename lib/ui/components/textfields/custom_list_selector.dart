import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CustomListSelector<T> extends StatefulWidget {
  final List<T> items;
  final String Function(T) displayText;
  final T? selectedValue;
  final Function(T?)? onChanged;
  final String? searchHintText;

  const CustomListSelector({
    super.key,
    required this.items,
    required this.displayText,
    this.selectedValue,
    this.onChanged,
    this.searchHintText,
  });

  @override
  State<CustomListSelector<T>> createState() => _CustomListSelectorState<T>();
}

class _CustomListSelectorState<T> extends State<CustomListSelector<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<T> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items
            .where((item) => widget
                .displayText(item)
                .toLowerCase()
                .contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  void _selectItem(T item) {
    widget.onChanged?.call(item);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black12.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      margin: EdgeInsets.only(top: Sizer.height(8)),
      padding: EdgeInsets.only(
        top: Sizer.height(6),
        bottom: Sizer.height(30),
        left: Sizer.width(6),
        right: Sizer.width(6),
      ),
      constraints: BoxConstraints(
        maxHeight: Sizer.height(300),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search field
          CustomTextField(
            hintText: widget.searchHintText ?? 'Search',
            controller: _searchController,
            borderRadius: Sizer.height(50),
            onChanged: _onSearchChanged,
            onSubmitted: (p0) {},
            prefixIcon: Icon(
              Iconsax.search_normal_1,
              color: AppColors.gray500,
              size: Sizer.radius(16),
            ),
          ),

          YBox(12),
          // List items
          Flexible(
            child: _filteredItems.isEmpty
                ? Padding(
                    padding: EdgeInsets.all(Sizer.radius(16)),
                    child: Text(
                      'No options found',
                      style: TextStyle(
                        color: AppColors.gray500,
                        fontSize: Sizer.text(14),
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    separatorBuilder: (context, index) => YBox(8),
                    itemCount: _filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = _filteredItems[index];
                      final isSelected = widget.selectedValue == item;
                      return InkWell(
                        onTap: () => _selectItem(item),
                        borderRadius: BorderRadius.circular(Sizer.radius(8)),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(12),
                            vertical: Sizer.height(12),
                          ),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.blueFD
                                : Colors.transparent,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(8)),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  widget.displayText(item),
                                  style: TextStyle(
                                    color: AppColors.gray700,
                                    fontSize: Sizer.text(14),
                                    fontWeight: isSelected
                                        ? FontWeight.w500
                                        : FontWeight.w400,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  size: Sizer.radius(20),
                                  color: AppColors.baseGreen,
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
