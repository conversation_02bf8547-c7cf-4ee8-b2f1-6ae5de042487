// ignore_for_file: use_build_context_synchronously

import 'package:intl/intl.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/components/dropdown/wallet_dropdown_overlay.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class AccountStatementModal extends StatefulWidget {
  const AccountStatementModal({
    super.key,
    required this.wallet,
  });

  final Wallet wallet;

  @override
  State<AccountStatementModal> createState() => _AccountStatementModalState();
}

class _AccountStatementModalState extends State<AccountStatementModal> {
  final _startDateC = TextEditingController();
  final _endDateC = TextEditingController();
  final _currencyC = TextEditingController();
  final _walletFocusNode = FocusNode();
  final GlobalKey _walletFieldKey = GlobalKey();

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();
  Wallet? selectedWallet;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      selectedWallet = widget.wallet;
      _currencyC.text = selectedWallet?.currency?.name ?? "";
      setState(() {});
    });

    // Add focus listener for wallet dropdown
    _walletFocusNode.addListener(() {
      if (_walletFocusNode.hasFocus) {
        _showOverlay();
      } else {
        _hideOverlay();
      }
    });
  }

  @override
  void dispose() {
    _walletFocusNode.dispose();
    _hideOverlay();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authVm = context.watch<AuthUserVM>();
    return GestureDetector(
      onTap: () {
        // Close dropdown when tapping outside
        if (_overlayEntry != null) {
          _walletFocusNode.unfocus();
          _hideOverlay();
        }
      },
      child: Padding(
        padding: EdgeInsets.all(Sizer.radius(12)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: Sizer.screenWidth,
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(12)),
              decoration: BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  YBox(20),
                  Text(
                    "Account Statement",
                    style: AppTypography.text18.semiBold,
                  ),
                  YBox(4),
                  Text(
                    "Please fill the following with accurate \ninformation",
                    textAlign: TextAlign.center,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray93,
                    ),
                  ),
                  YBox(20),
                  Container(
                    key: _walletFieldKey,
                    child: CustomTextField(
                      labelText: "Wallet",
                      showLabelHeader: true,
                      isReadOnly: true,
                      controller: _currencyC,
                      focusNode: _walletFocusNode,
                      borderRadius: Sizer.height(12),
                      hintText: 'Select an option',
                      showSuffixIcon: true,
                      prefixIcon: Container(
                          padding: EdgeInsets.all(Sizer.radius(10)),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: SvgPicture.network(
                              selectedWallet?.currency?.flag ?? '',
                              width: Sizer.width(20),
                              height: Sizer.height(14),
                              fit: BoxFit.cover,
                            ),
                          )),
                      suffixIcon: Icon(
                        Icons.expand_more,
                        color: AppColors.gray500,
                        size: Sizer.height(26),
                      ),
                      onTap: () {
                        if (_overlayEntry != null) {
                          // If dropdown is open, close it
                          _walletFocusNode.unfocus();
                          _hideOverlay();
                        } else {
                          // If dropdown is closed, open it
                          _walletFocusNode.requestFocus();
                        }
                      },
                    ),
                  ),
                  const YBox(20),
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          isReadOnly: true,
                          labelText: "Start Date",
                          hintText: '17 Feb 2024',
                          showLabelHeader: true,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(10),
                            ),
                            child: Icon(
                              Iconsax.calendar_1,
                              color: AppColors.mainBlack,
                              size: Sizer.height(20),
                            ),
                          ),
                          controller: _startDateC,
                          onTap: () async {
                            await showCupertinoDatePicker(
                              context,
                              onDateTimeChanged: (val) {
                                // Check if val is greater now
                                if (val.isAfter(DateTime.now())) {
                                  startDate = DateTime.now();
                                  return;
                                }

                                startDate = val;
                              },
                              onDone: () {
                                _startDateC.text =
                                    DateFormat('dd/MM/yyyy').format(startDate);
                                setState(() {});
                                Navigator.pop(context);
                              },
                            );
                          },
                          onChanged: (val) {},
                        ),
                      ),
                      const XBox(20),
                      Expanded(
                        child: CustomTextField(
                          isReadOnly: true,
                          labelText: "End Date",
                          hintText: '17 Feb 2024',
                          showLabelHeader: true,
                          borderRadius: Sizer.height(12),
                          prefixIcon: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(10),
                            ),
                            child: Icon(
                              Iconsax.calendar_1,
                              color: AppColors.mainBlack,
                              size: Sizer.height(20),
                            ),
                          ),
                          controller: _endDateC,
                          onTap: () async {
                            await showCupertinoDatePicker(
                              context,
                              minimumDate: startDate,
                              onDateTimeChanged: (val) {
                                if (val.isAfter(DateTime.now())) {
                                  endDate = DateTime.now();
                                  return;
                                }
                                endDate = val;
                              },
                              onDone: () {
                                _endDateC.text =
                                    DateFormat('dd/MM/yyyy').format(endDate);
                                setState(() {});
                                Navigator.pop(context);
                              },
                            );
                          },
                          onChanged: (val) {},
                        ),
                      ),
                    ],
                  ),
                  const YBox(20),
                  CustomTextField(
                    labelText: "Email",
                    isReadOnly: true,
                    fillColor: AppColors.litGrey100,
                    showLabelHeader: true,
                    borderRadius: Sizer.height(12),
                    controller: TextEditingController(
                      text: authVm.user?.email,
                    ),
                    onChanged: (val) {},
                  ),
                  const YBox(120),
                  CustomBtn.solid(
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    text: "Continue",
                    isLoading: authVm.isBusy,
                    online: selectedWallet != null &&
                        _startDateC.text.trim().isNotEmpty &&
                        _endDateC.text.trim().isNotEmpty &&
                        _currencyC.text.trim().isNotEmpty,
                    onTap: () {
                      _downloadAccountStatement();
                    },
                  ),
                  const YBox(40),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _walletFieldKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx,
        top: position.dy + size.height + 4,
        width: size.width,
        child: Material(
          color: Colors.transparent,
          child: WalletDropdownOverlay(
            selectedWallet: selectedWallet,
            onWalletSelected: _onWalletSelected,
            maxHeight: 240,
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _onWalletSelected(Wallet wallet) {
    setState(() {
      selectedWallet = wallet;
      _currencyC.text = wallet.currency?.name ?? "";
    });
    _walletFocusNode.unfocus();
    _hideOverlay();
  }

  _downloadAccountStatement() {
    final authVm = context.read<AuthUserVM>();
    authVm
        .downloadAccountStatement(
      startDate: startDate.toIso8601String().split("T").first,
      endDate: endDate.toIso8601String().split("T").first,
      currencyId: selectedWallet?.currency?.id ?? 0,
    )
        .then((value) {
      if (value.success) {
        _pop();
        _pop();
        BsWrapper.bottomSheet(
          context: context,
          widget: ConfirmationModal(
            args: ConfirmationModalArgs(
              title: "Statement Sent!",
              description:
                  'Your Account statement has successfully \nbeen sent to your email address',
              btnText: 'Done',
              btnTap: () {
                _pop();
              },
            ),
          ),
        );
      } else {
        _showErrorConfirmationScreen(msg: value.message);
      }
    });
  }

  _showErrorConfirmationScreen({String? msg}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () {
          _pop();
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
