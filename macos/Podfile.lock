PODS:
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (10.22.0):
    - Firebase/Core
  - Firebase/Core (10.22.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.22.0)
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Crashlytics (10.22.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.22.0)
  - Firebase/Messaging (10.22.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.22.0)
  - Firebase/RemoteConfig (10.22.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 10.22.0)
  - firebase_analytics (10.8.9):
    - Firebase/Analytics (= 10.22.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.27.0):
    - Firebase/CoreOnly (~> 10.22.0)
    - FlutterMacOS
  - firebase_crashlytics (3.4.18):
    - Firebase/CoreOnly (~> 10.22.0)
    - Firebase/Crashlytics (~> 10.22.0)
    - firebase_core
    - FlutterMacOS
  - firebase_messaging (14.7.19):
    - Firebase/CoreOnly (~> 10.22.0)
    - Firebase/Messaging (~> 10.22.0)
    - firebase_core
    - FlutterMacOS
  - firebase_remote_config (4.3.17):
    - Firebase/CoreOnly (~> 10.22.0)
    - Firebase/RemoteConfig (~> 10.22.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseABTesting (10.25.0):
    - FirebaseCore (~> 10.0)
  - FirebaseAnalytics (10.22.0):
    - FirebaseAnalytics/AdIdSupport (= 10.22.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.25.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.25.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.22.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.25.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfig (10.22.0):
    - FirebaseABTesting (~> 10.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseSessions (10.25.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.25.0)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.11):
    - FMDB/standard (= 2.7.11)
  - FMDB/standard (2.7.11)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - GoogleAppMeasurement (10.22.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.22.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.22.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_crashlytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - firebase_remote_config (from `Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `Flutter/ephemeral/.symlinks/plugins/smart_auth/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseSessions
    - FirebaseSharedSwift
    - FMDB
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_crashlytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  firebase_remote_config:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_remote_config/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  smart_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/smart_auth/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_analytics: d3b627594fb80567824609e9c2622bd4f48aee81
  firebase_core: 94cef1d79e26747286a4808b2345c1ecb8501b11
  firebase_crashlytics: 1e514e5700aeb92c50fbf987c112deab8778f2c1
  firebase_messaging: b243cd1026362f3f5ec5e73ffb0c4431519f0b57
  firebase_remote_config: 10c8b1b0a76aabf1e6a3c2f57a73ec756c98d439
  FirebaseABTesting: e6e3c3e0e35813874f571d1b7bdae2aab319dd38
  FirebaseAnalytics: 8d0ff929c63b7f72260f332b86ccf569776b75d3
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreExtension: 8a47811d0b155501559ef05d089518152a0a1677
  FirebaseCoreInternal: 910a81992c33715fec9263ca7381d59ab3a750b7
  FirebaseCrashlytics: e568d68ce89117c80cddb04073ab9018725fbb8c
  FirebaseInstallations: 91950fe859846fff0fbd296180909dd273103b09
  FirebaseMessaging: 9f71037fd9db3376a4caa54e5a3949d1027b4b6e
  FirebaseRemoteConfig: e1b992a94d3674dddbcaf5d0d31a0312156ceb1c
  FirebaseSessions: c0939656253a1fa0e94ecc266ccf770cc8b33732
  FirebaseSharedSwift: 0274086954b1b2d5fd7e829eccc587044d72a4ba
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  geolocator_apple: 72a78ae3f3e4ec0db62117bd93e34523f5011d58
  GoogleAppMeasurement: ccefe3eac9b0aa27f96066809fb1a7fe4b462626
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  smart_auth: b38e3ab4bfe089eacb1e233aca1a2340f96c28e9
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: 5f437abeda8c85500ceb03f5c1938a8c5a705399

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.15.2
