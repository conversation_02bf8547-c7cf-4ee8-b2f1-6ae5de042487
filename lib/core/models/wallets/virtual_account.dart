class VirtualAccount {
  String? accountNumber;
  String? bankCode;
  String? currency;
  String? customerIdentifier;
  String? provider;
  String? bankName;
  String? accountName;

  VirtualAccount({
    this.accountNumber,
    this.accountName,
    this.bankCode,
    this.bankName,
    this.currency,
    this.customerIdentifier,
    this.provider,
  });

  factory VirtualAccount.fromJson(Map<String, dynamic> json) => VirtualAccount(
        accountNumber: json["account_number"],
        accountName: json["account_name"],
        bankCode: json["bank_code"],
        bankName: json["bank_name"],
        currency: json["currency"],
        customerIdentifier: json["customer_identifier"],
        provider: json["provider"],
      );

  Map<String, dynamic> toJson() => {
        "account_number": accountNumber,
        "bank_code": bankCode,
        "currency": currency,
        "customer_identifier": customerIdentifier,
        "provider": provider,
      };
}
