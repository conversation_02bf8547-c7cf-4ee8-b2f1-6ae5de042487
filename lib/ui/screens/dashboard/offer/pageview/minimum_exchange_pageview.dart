import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class MinimumExchangePageView extends StatelessWidget {
  const MinimumExchangePageView({
    Key? key,
    required this.offerType,
  }) : super(key: key);

  final OfferType offerType;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      child: Consumer<CreateOfferVM>(builder: (context, vm, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AuthTextSubTitle(
              title: "Minimum Exchange (Optional)",
              subtitle: "Set the smallest amount users can accept",
            ),
            const YBox(24),
            ContainerWithBluewishBg(
              bgColor: AppColors.userBg,
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Allow Partial Amounts',
                        style: AppTypography.text14.copyWith(
                          color: AppColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const YBox(4),
                      RichText(
                        text: TextSpan(
                          children: [
                            const TextSpan(
                              text: 'Enable for others to',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'inter',
                                fontWeight: FontWeight.w500,
                                color: AppColors.black600,
                              ),
                            ),
                            TextSpan(
                              text:
                                  ' ${offerType == OfferType.buy ? 'sell' : 'buy'}',
                              style: const TextStyle(
                                fontSize: 12,
                                fontFamily: 'inter',
                                fontWeight: FontWeight.w500,
                                color: AppColors.lightBlue,
                              ),
                            ),
                            const TextSpan(
                              text: ' fraction of your offer',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: 'inter',
                                fontWeight: FontWeight.w500,
                                color: AppColors.black600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  CustomSwitch(
                    value: vm.allowPartialAmt,
                    onChanged: (value) {
                      vm.setAllowPartialAmt(value);
                    },
                  ),
                ],
              ),
            ),
            const YBox(24),
            if (vm.allowPartialAmt)
              CustomTextField(
                labelText: "What is your minimum amount",
                showLabelHeader: true,
                borderRadius: Sizer.height(4),
                hintText: '${vm.partialAmt ?? '0.00'} CAD',
                isReadOnly: true,
                onTap: () {
                  BsWrapper.bottomSheet(
                      context: context, widget: const SelectAmountSheet());
                },
                showSuffixIcon: true,
                suffixIcon: Icon(
                  Icons.expand_more,
                  color: AppColors.gray500,
                  size: Sizer.height(26),
                ),
                onChanged: (val) {},
              ),
            const YBox(24),
            const Spacer(),
            CustomBtn.solid(
              onTap: () {
                Navigator.pushNamed(context, RoutePath.reviewOfferScreen,
                    arguments: OfferTypeArg(offerType));
              },
              online: true,
              text: "Continue",
            ),
            const YBox(30),
          ],
        );
      }),
    );
  }
}
