import 'package:korrency/core/core.dart';

class FreqCountryListTile extends StatelessWidget {
  const FreqCountryListTile({
    super.key,
    required this.countryFlag,
    required this.countryName,
    this.isSelected = false,
    this.onTap,
  });

  final String countryFlag;
  final String countryName;
  final bool isSelected;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(12),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.litGrey100 : AppColors.white,
          borderRadius: BorderRadius.circular(
            Sizer.radius(8),
          ),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(100),
              child: SizedBox(
                  width: Sizer.width(24),
                  height: Sizer.height(24),
                  child: SvgPicture.network(
                    countryFlag,
                    fit: BoxFit.cover,
                  )),
            ),
            const XBox(16),
            Expanded(
              child: Text(countryName,
                  style: AppTypography.text16.copyWith(
                    color: AppColors.textBlack900,
                    fontWeight: FontWeight.w500,
                  )),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: AppColors.textBlack900,
                size: Sizer.height(20),
              ),
          ],
        ),
      ),
    );
  }
}
