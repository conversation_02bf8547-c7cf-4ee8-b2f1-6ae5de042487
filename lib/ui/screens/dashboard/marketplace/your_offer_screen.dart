import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:skeletonizer/skeletonizer.dart';

class YourOfferScreen extends StatefulWidget {
  const YourOfferScreen({Key? key}) : super(key: key);

  @override
  State<YourOfferScreen> createState() => _YourOfferScreenState();
}

class _YourOfferScreenState extends State<YourOfferScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() {
    context.read<MyOfferVM>().getMyOffers();
    context.read<MyOfferVM>().getOfferActivity();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MyOfferVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: Safe<PERSON>rea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(
                      'Your Offers',
                      style: AppTypography.text24.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.headerBlack,
                      ),
                    ),
                  ],
                ),
                const YBox(20),
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        borderRadius: Sizer.height(4),
                        prefixIcon: Icon(
                          Iconsax.search_normal_1,
                          color: AppColors.black600,
                          size: Sizer.radius(20),
                        ),
                        hintText: 'Search...',
                        // controller: vm.emailController,
                        // errorText:
                        //     vm.emailController.text.isNotEmpty && !vm.isValidEmail
                        //         ? "Invalid Email"
                        //         : null,
                        onChanged: (val) {},
                      ),
                    ),
                    const XBox(14),
                    InkWell(
                      onTap: () {
                        Navigator.of(context)
                            .pushNamed(RoutePath.sortOffersScreen);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(12),
                          vertical: Sizer.width(11),
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.bgWhite,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: AppColors.opacityWhite,
                          ),
                        ),
                        child: svgHelper(AppSvgs.filterLines),
                      ),
                    )
                  ],
                ),
                const YBox(30),
                Row(
                  children: [
                    TextRow(
                      text: "Deals",
                      value: "${vm.offerDealActivity.length}",
                      hasValue: true,
                      isSelected: vm.yourOfferType == OfferType.deal,
                      onTap: () => vm.setYourOffersType(OfferType.deal),
                    ),
                    const XBox(16),
                    TextRow(
                      text: "Sell Offers",
                      isSelected: vm.yourOfferType == OfferType.sell,
                      onTap: () => vm.setYourOffersType(OfferType.sell),
                    ),
                    const XBox(16),
                    TextRow(
                      text: "Buy Offers",
                      isSelected: vm.yourOfferType == OfferType.buy,
                      onTap: () => vm.setYourOffersType(OfferType.buy),
                    )
                  ],
                ),
                const YBox(10),
                Builder(builder: (context) {
                  if (vm.isBusy) {
                    return Expanded(
                      child: Skeletonizer(
                        child: ListView.separated(
                          shrinkWrap: true,
                          itemBuilder: (ctx, i) {
                            return OfferMarketDetailsCard(
                              onTap: () {},
                              title1: "3,000 CAD available",
                              title2: "minimum",
                              rate: "500 minimum",
                              expiresAt: DateTime.now(),
                              createAt: DateTime.now(),
                              minimumAmount: "0",
                            );
                          },
                          separatorBuilder: (ctx, _) => const YBox(16),
                          itemCount: 10,
                        ),
                      ),
                    );
                  }
                  return Expanded(
                    child: Column(
                      children: [
                        if (vm.yourOfferType == OfferType.deal)
                          Expanded(
                            child: RefreshIndicator(
                              onRefresh: () async {
                                _init();
                              },
                              child: vm.offerDealActivity.isEmpty
                                  ? SizedBox(
                                      height: Sizer.height(
                                          259), // Adjust the height based on the offer type
                                      child: Center(
                                        child: Text(
                                          "No deals available",
                                          style: AppTypography.text16.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.gray500),
                                        ),
                                      ),
                                    )
                                  : ListView.separated(
                                      shrinkWrap: true,
                                      padding: EdgeInsets.only(
                                        top: Sizer.height(20),
                                        bottom: Sizer.height(100),
                                      ),
                                      itemBuilder: (context, i) {
                                        var deal = vm.offerDealActivity[i];
                                        return YourOfferCard(
                                          deal: deal,
                                          onTap: () {
                                            Navigator.of(context).pushNamed(
                                                RoutePath.myOfferDetailsScreen,
                                                arguments: OfferDetailArg(
                                                    deal.offer?.id ?? 0));
                                          },
                                        );
                                      },
                                      separatorBuilder: (ctx, _) =>
                                          const YBox(20),
                                      itemCount: vm.offerDealActivity.length,
                                    ),
                            ),
                          ),
                        if (vm.yourOfferType == OfferType.sell)
                          buildOfferList(
                              context, vm.mySellOffers, OfferType.sell,
                              (offer) {
                            Navigator.of(context).pushNamed(
                                RoutePath.myOfferDetailsScreen,
                                arguments: OfferDetailArg(offer.id ?? 0));
                          }),
                        if (vm.yourOfferType == OfferType.buy)
                          buildOfferList(context, vm.myBuyOffers, OfferType.buy,
                              (offer) {
                            Navigator.of(context).pushNamed(
                                RoutePath.myOfferDetailsScreen,
                                arguments: OfferDetailArg(offer.id ?? 0));
                          }),
                      ],
                    ),
                  );
                })
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget buildOfferList(BuildContext context, List<Offers> offers,
      OfferType offerType, ValueChanged<Offers> onTap) {
    return Builder(builder: (context) {
      return offers.isEmpty
          ? SizedBox(
              height: Sizer.height(
                  259), // Adjust the height based on the offer type
              child: Center(
                child: Text(
                  "No ${offerType == OfferType.sell ? 'sell' : 'buy'} offers available",
                  style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w600, color: AppColors.gray500),
                ),
              ),
            )
          : Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  _init();
                },
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(
                    top: Sizer.height(20),
                    bottom: Sizer.height(100),
                  ),
                  itemBuilder: (context, i) {
                    var offer = offers[i];
                    return OfferMarketDetailsCard(
                      onTap: () => onTap(offer),
                      title1:
                          "\$${AppUtils.formatAmountDoubleString(offer.tradingAmount ?? "0")}",
                      title2: offer.askingCurrency?.code ?? "",
                      rate: "${offer.rate}",
                      expiresAt: offer.expiresAt,
                      createAt: offer.createdAt,
                      showMinimumAmount: offer.allowsPartialTrade ?? false,
                      minimumAmount:
                          "\$${AppUtils.formatAmountDoubleString(offer.minimumPartialTradeAmount ?? "0")} minimum",
                      offerType: offerType,
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(20),
                  itemCount: offers.length,
                ),
              ),
            );
    });
  }
}
