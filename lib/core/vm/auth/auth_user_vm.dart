import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:korrency/core/core.dart';

class AuthUserVM extends BaseVM {
  AuthUserVM() {
    getUserFromStorage();
  }

  AuthUser? _user;
  AuthUser? get user => _user;
  List<DeactivateAccountMessage> _deactivateAccountMessages = [];
  List<DeactivateAccountMessage> get deactivateAccountMessages =>
      _deactivateAccountMessages;

  DeactivateAccountMessage? _selectedReason;
  DeactivateAccountMessage? get selectedReason => _selectedReason;

  /// This flag help check getAuthUser() method is called
  /// if it is called, it will be set to true
  bool _refresh = false;
  bool get refresh => _refresh;
  String get verifyMethod => _user?.twoFactorNotificationPreference == mail
      ? _user?.email ?? ""
      : _user?.phone ?? "";
  ReferralRule? get referralRule => _user?.referralRule;
  List<String> _avatars = [];
  List<String> get avatars => _avatars;
  bool get loadWhenBusy => _refresh && isBusy;
  bool _hasFundInAccount = false;
  bool get hasFundInAccount => _hasFundInAccount;
  bool _hasOpenOffers = false;
  bool get hasOpenOffers => _hasOpenOffers;

  bool get enableDeactiveBtn => !_hasFundInAccount && !_hasOpenOffers;

  setUser(AuthUser? user) {
    _user = user;
    reBuildUI();
  }

  String get userInteracMail => _user?.interacEmail ?? "";

  bool get userIsVerified => _user?.status?.toLowerCase() == KycStatus.verified;
  bool get statusProcessing =>
      _user?.status?.toLowerCase() == KycStatus.processing;
  bool get kycVerified => userIsVerified || statusProcessing;

  bool get isTrustedDevice => _user?.isTrustedDevice ?? false;
  bool get hasSecurityQuestions => _user?.hasSecurityQuestions ?? false;
  bool get secQuestCheck =>
      !hasSecurityQuestions && isTrustedDevice && ((_user?.kycStep ?? 0) > 3);

  bool get hasTransactionPin => _user?.hasTransactionPin ?? false;
  bool get transactionPinCheck => !hasTransactionPin && isTrustedDevice;

  bool get kycVerifiedCheck => !kycVerified && isTrustedDevice;
  bool get isPoliticallyExposed => _user?.isPoliticallyExposed ?? false;
  bool get awaitingReview => _user?.awaitingReview ?? false;
  bool get openOccupationModal => userIsVerified && _user?.occupation == null;

  // Country Currency
  Currency? get countryCurrency => _user?.countryCurrency;

  String? get nameInitals {
    if (user?.lastName == null || user?.userName == null) {
      return "${user?.userName?.substring(1, 2) ?? ""}${user?.userName?.substring(2, 3) ?? ""}";
    }
    return "${user?.firstName?.substring(0, 1) ?? ""}${user?.lastName?.substring(0, 1) ?? ""}";
  }

  setSelectedReason(DeactivateAccountMessage? reason) {
    _selectedReason = reason;
    reBuildUI();
  }

  bool _closeGetStarted = false;
  bool get closeGetStarted => _closeGetStarted;
  setCloseGetStarted(bool value) {
    _closeGetStarted = value;
    reBuildUI();
  }

  Future<ApiResponse> getAuthUser() async {
    return await performApiCall(
      url: "/auth/user",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        // printty(data, logLevel: "Raw Data");
        _user = authUserFromJson(json.encode(data["data"]));

        //Mixpanel identify user
        MixpanelService().identify(_user?.id ?? "");

        // Set user ID across all analytics platforms (async without await to avoid blocking)
        UnifiedAnalyticsManager.instance.setUserId(_user?.id ?? "").then((_) {
          // Set user properties for enhanced targeting
          return UnifiedAnalyticsManager.instance.setUserProperties(
            email: _user?.email,
            firstName: _user?.firstName,
            lastName: _user?.lastName,
            country: _user?.country,
            customProperties: {
              'kyc_step': _user?.kycStep?.toString() ?? '0',
              'registration_date': _user?.createdAt?.toString() ?? '',
            },
          );
        }).catchError((e) {
          printty('❌ Error setting analytics user data: $e');
        });

        _refresh = true;
        return apiResponse;
      },
    );
  }

  updateUser(Map<String, dynamic> user) async {
    printty(user, level: 'updateUser got called');
    await StorageService.storeUser(user);
    _user = AuthUser.fromJson(user);
    reBuildUI();
  }

  Future<ApiResponse> getAvatars() async {
    return await performApiCall(
      url: "/utilities/custom-avatars",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        printty(data, level: "Avatar Data");
        _avatars = fcbNotifcationResFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateProfileAvatar(String avatarUrl) async {
    return await performApiCall(
      url: "/auth/profile/avatar",
      method: apiService.postWithAuth,
      body: {
        "avatar_url": avatarUrl,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateUserNotification(Map<String, dynamic> body) async {
    return await performApiCall(
      url: "/auth/profile/update-notification-settings",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        // printty(data, logLevel: "Raw Data");
        _user = authUserFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateInteracMail(String email) async {
    return await performApiCall(
      url: "/virtual-accounts/update-interac-email",
      method: apiService.postWithAuth,
      body: {
        "interac_email": email,
      },
      onSuccess: (data) {
        printty(data, level: "Interac Email Updated");
        getAuthUser();
        return apiResponse;
      },
    );
  }

  getUserFromStorage() async {
    var authUser = await StorageService.getUser();
    if (authUser != null) {
      _user = authUser;
      reBuildUI();
    }
  }

  Future<ApiResponse> set2FA(
      {required TwoFactorAuthType twoFactorAuthType}) async {
    // if(twoFactorAuthType == TwoFactorAuthType.none) return;
    return await performApiCall(
      url: "/auth/profile/update-notification-preference",
      method: apiService.postWithAuth,
      body: {
        "preference":
            twoFactorAuthType == TwoFactorAuthType.sms ? "sms" : "mail"
      },
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        _user = authUserFromJson(json.encode(data["data"]));
        return ApiResponse(success: true, message: data["message"]);
      },
    );
  }

  Future<ApiResponse> accountCheck() async {
    return await performApiCall(
      url: "/auth/accounts/check",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        // printty(data, logLevel: "Raw Data");
        _hasFundInAccount = data["data"]["has_funds_in_account"];
        _hasOpenOffers = data["data"]["has_open_offers"];
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getDeactivedAccountMessage() async {
    return await performApiCall(
      url: "/auth/accounts/deactivate-messages",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _deactivateAccountMessages =
            deactivateAccountMessageFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> deactivateAccount(String password) async {
    Map<String, dynamic> body = {
      "password": password,
    };

    if (selectedReason != null) {
      body.addAll(selectedReason!.toJson());
    }

    return await performApiCall(
      url: "/auth/accounts/deactivate",
      method: apiService.postWithAuth,
      body: body,
      onSuccess: (data) {
        return ApiResponse(success: true, message: data["message"]);
      },
    );
  }

  // Upload profile picture
  Future<ApiResponse> uploadProfilePicture(File file) async {
    final payload = {
      "image": await MultipartFile.fromFile(file.path),
      "type": "file",
    };

    return await performApiCall(
      url: "/auth/profile/picture",
      method: apiService.postWithAuth,
      body: payload,
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        _user = authUserFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> downloadAccountStatement({
    required String startDate,
    required String endDate,
    required int currencyId,
  }) async {
    printty(
        "startDate: $startDate, endDate: $endDate, currencyId: $currencyId");
    return await performApiCall(
      url:
          "/transactions/statement?start_date=$startDate&end_date=$endDate&currency_id=$currencyId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  void resetData() {
    printty("Resetting AuthUser VM data ");

    _user = null;
    _refresh = false;
    _selectedReason = null;
    reBuildUI();
  }
}

class KycStatus {
  static const pending = "pending";
  static const processing = "processing";
  static const verified = "verified";
  static const failed = "failed";
}

class NotificationFields {
  static const email = "email";
  static const rate = "rate";
  static const push = "push";
}

List<String> fcbNotifcationResFromJson(String str) =>
    List<String>.from(json.decode(str).map((x) => x));
