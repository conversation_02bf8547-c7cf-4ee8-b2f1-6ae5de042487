import 'package:korrency/core/core.dart';

class QuickActionCard extends StatefulWidget {
  const QuickActionCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.imgaeBg,
    required this.svgIcon,
    this.iconPosition,
    this.iconSize,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String imgaeBg;
  final String svgIcon;
  final double? iconSize;
  final IconPosition? iconPosition;
  final Function()? onTap;

  @override
  State<QuickActionCard> createState() => _QuickActionCardState();
}

class _QuickActionCardState extends State<QuickActionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<double>(
      begin: 200.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.bounceOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Stack(
        children: [
          Container(
            height: Sizer.height(180),
            width: Sizer.width(160),
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
              vertical: Sizer.height(16),
            ),
            decoration: BoxDecoration(
              color: AppColors.blue9EB,
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: AppTypography.text16.semiBold.copyWith(
                    color: AppColors.primaryBlue,
                  ),
                ),
                YBox(2),
                Text(
                  widget.subTitle,
                  style: AppTypography.text10.copyWith(
                    color: AppColors.blue7AB,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(Sizer.radius(12)),
                bottomRight: Radius.circular(Sizer.radius(12)),
              ),
              child: Stack(
                children: [
                  SizedBox(
                    width: Sizer.width(160),
                    child: imageHelper(widget.imgaeBg),
                  ),
                  Positioned(
                    bottom: widget.iconPosition?.bottom,
                    right: widget.iconPosition?.right,
                    left: widget.iconPosition?.left,
                    top: widget.iconPosition?.top,
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _slideAnimation.value),
                          child: Opacity(
                            opacity: _fadeAnimation.value,
                            child: SvgPicture.asset(
                              widget.svgIcon,
                              height: Sizer.height(widget.iconSize ?? 80),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}

class IconPosition {
  final double? left;
  final double? right;
  final double? bottom;
  final double? top;

  IconPosition({
    this.left,
    this.right,
    this.bottom,
    this.top,
  });
}
