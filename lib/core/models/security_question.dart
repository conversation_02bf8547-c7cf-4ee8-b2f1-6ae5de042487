import 'dart:convert';

List<SecurityQuestion> securityQuestionFromJson(String str) =>
    List<SecurityQuestion>.from(
        json.decode(str).map((x) => SecurityQuestion.fromJson(x)));

String securityQuestionToJson(List<SecurityQuestion> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SecurityQuestion {
  int? id;
  String? question;

  SecurityQuestion({
    this.id,
    this.question,
  });

  factory SecurityQuestion.fromJson(Map<String, dynamic> json) =>
      SecurityQuestion(
        id: json["id"],
        question: json["question"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
      };
}

ChangeSecAnswers secAnswersFromJson(String str) =>
    ChangeSecAnswers.fromJson(json.decode(str));

String secAnswersToJson(ChangeSecAnswers data) => json.encode(data.toJson());

class ChangeSecAnswers {
  final int? attempts;
  final int? correctAnswers;
  final int? totalQuestions;

  ChangeSecAnswers({
    this.attempts,
    this.correctAnswers,
    this.totalQuestions,
  });

  factory ChangeSecAnswers.fromJson(Map<String, dynamic> json) =>
      ChangeSecAnswers(
        attempts: json["attempts"],
        correctAnswers: json["correct_answers"],
        totalQuestions: json["total_questions"],
      );

  Map<String, dynamic> toJson() => {
        "attempts": attempts,
        "correct_answers": correctAnswers,
        "total_questions": totalQuestions,
      };
}
