import 'package:korrency/core/core.dart';

class OnboardCred {
  final String title;
  final String desc;
  final String image;

  OnboardCred({
    required this.title,
    required this.desc,
    required this.image,
  });

  static List<OnboardCred> get onboardCreds => [
        OnboardCred(
          title: "Multi-currency Accounts",
          desc:
              "Manage multiple currencies seamlessly in one place for your exchanges and global transfers",
          image: AppImages.onboard0,
        ),
        OnboardCred(
          title: "Transfer Globally",
          desc:
              "Send and receive money internationally at competitive exchange rates, Guaranteed!",
          image: AppImages.onboard1,
        ),
        OnboardCred(
          title: "Tested and Trusted",
          desc:
              "Securely exchange money with your peers at your own rate for your complete peace of mind",
          image: AppImages.onboard2,
        ),
        OnboardCred(
          title: "24/7 Support",
          desc: "Always available to assist you, anytime, anywhere",
          image: AppImages.onboard3,
        ),
      ];
}
