import 'dart:async';

import 'core/core.dart';

/// SessionTimeoutService manages inactivity-based and app-focus-based timeouts.
///
/// Behavior:
/// - If the user is inactive for [inactivityDuration], triggers timeout
/// - If the app is minimized or goes to background, it does NOT trigger timeout; timer is paused
/// - On timeout: clears session data and navigates to WelcomeBackScreen
class SessionTimeoutService with WidgetsBindingObserver {
  static final SessionTimeoutService instance =
      SessionTimeoutService._internal();

  SessionTimeoutService._internal();

  Duration inactivityDuration = const Duration(minutes: 5);
  Timer? _inactivityTimer;
  bool _listening = false;
  DateTime? _lastActivityAt;
  DateTime? _expireAt;

  Future<void> _loadTimeoutFromStorage() async {
    final s =
        await StorageService.getStringItem(StorageKey.sessionTimeoutMinutes);
    if (s != null) {
      final m = int.tryParse(s);
      if (m != null && m > 0) {
        inactivityDuration = Duration(minutes: m);
      }
    }
  }

  /// Configure inactivity duration (default 5 minutes).
  void configure({Duration? inactivity}) {
    if (inactivity != null) inactivityDuration = inactivity;
  }

  /// Start listening for session timeout conditions.
  void start() {
    if (_listening) {
      _resetTimer();
      return;
    }
    _listening = true;
    WidgetsBinding.instance.addObserver(this);
    _loadTimeoutFromStorage().then((_) {
      _resetTimer();
    });
  }

  /// Stop listening and cancel timers.
  void stop() {
    _listening = false;
    _inactivityTimer?.cancel();
    _inactivityTimer = null;
    WidgetsBinding.instance.removeObserver(this);
    printty("SessionTimeoutService stopped");
  }

  /// Register user activity (pointer taps, scrolls, key presses, navigation).
  void registerActivity() {
    if (!_listening) return;
    _lastActivityAt = DateTime.now();
    _expireAt = _lastActivityAt!.add(inactivityDuration);
    _scheduleTimerForRemaining();
  }

  void _resetTimer() {
    _lastActivityAt = DateTime.now();
    _expireAt = _lastActivityAt!.add(inactivityDuration);
    _scheduleTimerForRemaining();
  }

  void _scheduleTimerForRemaining() {
    _inactivityTimer?.cancel();
    if (_expireAt == null) return;
    final now = DateTime.now();
    if (now.isAfter(_expireAt!)) {
      // Already expired; trigger immediately
      _onImmediateTimeout();
      return;
    }
    final remaining = _expireAt!.difference(now);
    _inactivityTimer = Timer(remaining, _onTimeout);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!_listening) return;
    // Count background time toward inactivity. We avoid relying on timers
    // while the app is backgrounded, and instead compute remaining on resume.
    switch (state) {
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        // Timers won't execute in background; just cancel for efficiency.
        _inactivityTimer?.cancel();
        _inactivityTimer = null;
        break;
      case AppLifecycleState.resumed:
        // If already past expiry, trigger timeout immediately; otherwise reschedule
        final now = DateTime.now();
        if (_expireAt != null && now.isAfter(_expireAt!)) {
          _onImmediateTimeout();
        } else {
          _scheduleTimerForRemaining();
        }
        break;
      default:
        break;
    }
  }

  Future<void> _onImmediateTimeout() async {
    // Immediate timeout: clear and redirect
    await _handleTimeout();
  }

  Future<void> _onTimeout() async {
    await _handleTimeout();
  }

  Future<void> _handleTimeout() async {
    stop(); // Prevent re-entrancy
    // await StorageService.logout();
    // Navigate to WelcomeBackScreen for re-authentication
    final ctx = NavigatorKeys.appNavigatorKey.currentContext;
    if (ctx != null) {
      Navigator.pushNamed(ctx, RoutePath.welcomeBackScreen, arguments: false);
    }
  }
}

/// A lightweight widget to capture pointer activity app-wide and feed it to the service.
class ActivityListener extends StatefulWidget {
  final Widget child;
  const ActivityListener({super.key, required this.child});

  @override
  State<ActivityListener> createState() => _ActivityListenerState();
}

class _ActivityListenerState extends State<ActivityListener> {
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) => SessionTimeoutService.instance.registerActivity(),
      onPointerMove: (_) => SessionTimeoutService.instance.registerActivity(),
      onPointerSignal: (_) => SessionTimeoutService.instance.registerActivity(),
      child: RawKeyboardListener(
        focusNode: _focusNode,
        onKey: (_) => SessionTimeoutService.instance.registerActivity(),
        child: widget.child,
      ),
    );
  }
}
