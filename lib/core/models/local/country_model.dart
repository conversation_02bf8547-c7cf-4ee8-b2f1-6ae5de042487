import 'package:korrency/core/core.dart';

class CountryModel {
  final String? flag;
  final String? code;
  final String? dialCode;
  final String? name;
  final String? countryValue;

  CountryModel({
    required this.flag,
    required this.code,
    required this.dialCode,
    required this.name,
    this.countryValue,
  });

  static final List<CountryModel> _countries = [
    CountryModel(
      flag: AppSvgs.canada,
      code: 'CAD',
      dialCode: '+1',
      name: 'Canada',
      countryValue: "country:ca",
    ),
    // CountryModel(
    //   flag: AppSvgs.nigeria,
    //   code: 'NGN',
    //   dialCode: '+234',
    //   name: 'Nigeria',
    //   countryValue: "country:ng",
    // ),
    // CountryModel(
    //   flag: AppSvgs.kenya,
    //   code: 'KES',
    //   dialCode: '+254',
    //   name: 'Kenya',
    //   countryValue: "country:ke",
    // ),
    // CountryModel(
    //   flag: AppSvgs.ghana,
    //   code: 'GHN',
    //   dialCode: '+233',
    //   name: 'Ghana',
    //   countryValue: "country:gh",
    // ),
    // CountryModel(
    //   flag: AppSvgs.australia,
    //   code: 'AUD',
    //   dialCode: '+61',
    //   name: 'Australia',
    //   countryValue: "country:au",
    // ),
    // CountryModel(
    //   flag: AppSvgs.uganda,
    //   code: 'UGX',
    //   dialCode: '+256',
    //   name: 'Uganda',
    //   countryValue: "country:ug",
    // ),
    CountryModel(
      flag: AppSvgs.uk,
      code: 'GBP',
      dialCode: '+44',
      name: 'United Kingdom',
      countryValue: "country:gb",
    ),
    // CountryModel(
    //   flag: AppSvgs.usa,
    //   code: 'USD',
    //   dialCode: '+1',
    //   name: 'United States',
    //   countryValue: "country:us",
    // ),
  ];

  /// Sorted list alphabetically by country name
  static List<CountryModel> get countries {
    final sortedList = List<CountryModel>.from(_countries);
    sortedList.sort((a, b) => a.name!.compareTo(b.name!));
    return sortedList;
  }

  static CountryModel get defaultCountry =>
      countries.firstWhere((element) => element.name == 'Canada',
          orElse: () => countries.first);

  static CountryModel? getCountryByName(String name) {
    return countries.firstWhere(
      (e) => e.name?.toLowerCase() == name.toLowerCase(),
      orElse: () => CountryModel(
        flag: AppSvgs.canada,
        code: 'CAD',
        dialCode: '+1',
        name: 'Canada',
        countryValue: "country:ca",
      ),
    );
  }

  static CountryModel? getCountryByCode(String code) {
    return countries.firstWhere(
      (e) => e.code == code,
      orElse: () => CountryModel(
        flag: AppSvgs.canada,
        code: 'CAD',
        dialCode: '+1',
        name: 'Canada',
        countryValue: "country:ca",
      ),
    );
  }
}
