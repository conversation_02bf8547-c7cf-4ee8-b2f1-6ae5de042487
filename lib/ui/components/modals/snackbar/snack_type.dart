import 'package:flutter/material.dart';
import 'package:korrency/ui/components/modals/snackbar/default_colors.dart';

class SnackType {
  /// message is `required` parameter
  final String message;

  /// color is optional, if provided null then `DefaultColors` will be used
  final Color? color;

  SnackType(this.message, [this.color]);

  static SnackType help = SnackType('help', DefaultColors.helpBlue);
  static SnackType failure = SnackType('failure', DefaultColors.failureRed);
  static SnackType success = SnackType('success', DefaultColors.successGreen);
  static SnackType warning = SnackType('warning', DefaultColors.warningYellow);
}
