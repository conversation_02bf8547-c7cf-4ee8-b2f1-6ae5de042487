import 'package:korrency/core/core.dart';

class ForgotPasswordBtn extends StatelessWidget {
  const ForgotPasswordBtn({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, RoutePath.forgotPasswordScreen);
      },
      child: Container(
        alignment: Alignment.center,
        child: Text(
          "Forgot Password?",
          style: AppTypography.text14.copyWith(
            color: AppColors.blueE5,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
