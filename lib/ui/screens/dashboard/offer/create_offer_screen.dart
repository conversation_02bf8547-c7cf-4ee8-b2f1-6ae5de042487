import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CreateOfferScreen extends StatelessWidget {
  const CreateOfferScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "Create an Offer",
                subtitle:
                    "Hey, let's get your offer listed on the Marketplace in few simple steps!",
              ),
              const YBox(24),
              OfferCard(
                svgIcon: AppSvgs.sellOffer,
                title: "Sell Offer",
                subTitleFirst: "Create a Sell Offer If you",
                subTitleSecondBold: " have",
                subTitleThird: " CAD and",
                subTitleFourthBold: " need",
                subTitleFifth: " NGN",
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.createBuyOfferScreen,
                      arguments: OfferTypeArg(OfferType.sell));
                },
              ),
              const YBox(24),
              OfferCard(
                svgIcon: AppSvgs.buy,
                title: "Buy Offer",
                subTitleFirst: "Create a Buy Offer If you",
                subTitleSecondBold: " need",
                subTitleThird: " CAD and",
                subTitleFourthBold: " have",
                subTitleFifth: " NGN",
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.createBuyOfferScreen,
                      arguments: OfferTypeArg(OfferType.buy));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
