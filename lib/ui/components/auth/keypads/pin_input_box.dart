import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:korrency/core/core.dart';

class PINInputBox extends StatefulWidget {
  final String codeInput;

  const PINInputBox({
    super.key,
    required this.codeInput,
  });

  @override
  State<PINInputBox> createState() => _PINInputBoxState();
}

class _PINInputBoxState extends State<PINInputBox> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 47.w,
      height: 47.w,
      padding: EdgeInsets.only(top: Sizer.height(8)),
      decoration: BoxDecoration(
        color: widget.codeInput.isEmpty ? AppColors.bgWhite : AppColors.gray300,
        borderRadius: BorderRadius.circular(
          Sizer.radius(4),
        ),
        border: Border.all(
          width: Sizer.width(0.73),
          color:
              widget.codeInput.isEmpty ? AppColors.gray500 : AppColors.gray300,
        ),
      ),
      child: widget.codeInput.isNotEmpty
          ? Center(
              child: Text(
                '*',
                style: AppTypography.text32,
              ),
            )
          : const SizedBox.shrink(),
    );
  }
}
