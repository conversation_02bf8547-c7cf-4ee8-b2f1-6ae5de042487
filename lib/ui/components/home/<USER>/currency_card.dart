import 'package:korrency/core/core.dart';

class CurrencyCard extends StatelessWidget {
  const CurrencyCard({
    super.key,
    required this.svgPath,
    required this.title,
    this.bgColor,
    this.width,
    this.onTap,
  });

  final String svgPath;
  final String title;
  final Color? bgColor;
  final double? width;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Skeleton.replace(
        //  width: 35, // width of replacement
        //         height: 31,
        replacement: const Bone.square(
          size: 50,
          uniRadius: 10,
        ),
        child: Container(
          width: width,
          margin: EdgeInsets.only(right: Sizer.width(4)),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(Sizer.radius(12)),
                decoration: BoxDecoration(
                  color: bgColor ?? AppColors.blue100,
                  borderRadius: BorderRadius.circular(
                    Sizer.width(4),
                  ),
                ),
                child: svg<PERSON><PERSON><PERSON>(
                  svgPath,
                  height: Sizer.height(24),
                  width: Sizer.width(24),
                ),
              ),
              const YBox(10),
              Text(
                title,
                style: AppTypography.text12.copyWith(
                  color: AppColors.gray500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
