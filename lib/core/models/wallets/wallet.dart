import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:korrency/core/models/wallets/wallets.dart';

List<Wallet> walletFromJson(String str) =>
    List<Wallet>.from(json.decode(str).map((x) => Wallet.fromJson(x)));

String walletToJson(List<Wallet> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Wallet {
  String? id;
  String? balance;
  bool? isLocked;
  Currency? currency;
  List<VirtualAccount>? virtualAccounts;

  Wallet({
    this.id,
    this.balance,
    this.isLocked,
    this.currency,
    this.virtualAccounts,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) => Wallet(
        id: json["id"],
        balance: json["balance"],
        isLocked: json["is_locked"],
        currency: json["currency"] == null
            ? null
            : Currency.fromJson(json["currency"]),
        virtualAccounts: json["virtual_accounts"] == null
            ? []
            : List<VirtualAccount>.from(json["virtual_accounts"]!
                .map((x) => VirtualAccount.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "balance": balance,
        "is_locked": isLocked,
        "currency": currency?.toJson(),
        "virtual_accounts": virtualAccounts == null
            ? []
            : List<dynamic>.from(virtualAccounts!.map((x) => x.toJson())),
      };

  @override
  String toString() {
    return 'Wallet(id: $id, balance: $balance, isLocked: $isLocked, currency: $currency, virtualAccounts: $virtualAccounts)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Wallet &&
        other.id == id &&
        other.balance == balance &&
        other.isLocked == isLocked &&
        other.currency == currency &&
        listEquals(other.virtualAccounts, virtualAccounts);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        balance.hashCode ^
        isLocked.hashCode ^
        currency.hashCode ^
        virtualAccounts.hashCode;
  }
}
