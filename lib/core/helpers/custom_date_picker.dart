import 'package:flutter/material.dart';

class CustomDatePicker extends StatefulWidget {
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final Function(DateTime)? onDateSelected;

  const CustomDatePicker({
    super.key,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.onDateSelected,
  });

  @override
  State<CustomDatePicker> createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  late DateTime currentDate;
  late DateTime displayedMonth;
  DateTime? selectedDate;
  bool showMonthYearPicker = false;

  @override
  void initState() {
    super.initState();
    currentDate = widget.initialDate ?? DateTime.now();
    displayedMonth = DateTime(currentDate.year, currentDate.month);
    selectedDate = widget.initialDate;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 350,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildCalendarContent(),
          _buildButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                showMonthYearPicker = !showMonthYearPicker;
              });
            },
            child: Row(
              children: [
                Text(
                  _getMonthName(displayedMonth.month),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  showMonthYearPicker
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                showMonthYearPicker = !showMonthYearPicker;
              });
            },
            child: Row(
              children: [
                Text(
                  displayedMonth.year.toString(),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  showMonthYearPicker
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarContent() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child:
          showMonthYearPicker ? _buildMonthYearPicker() : _buildCalendarGrid(),
    );
  }

  Widget _buildMonthYearPicker() {
    return Container(
      height: 300,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          // Month selector
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: 12,
                itemBuilder: (context, index) {
                  int month = index + 1;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        displayedMonth = DateTime(displayedMonth.year, month);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: displayedMonth.month == month
                            ? const Color(0xFF3B82F6).withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        _getMonthName(month),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: displayedMonth.month == month
                              ? const Color(0xFF3B82F6)
                              : Colors.black54,
                          fontWeight: displayedMonth.month == month
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 20),
          // Year selector
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                reverse: true, // Show recent years first
                itemCount: 20, // Show 20 years range
                itemBuilder: (context, index) {
                  int year = DateTime.now().year - 10 + index;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        displayedMonth = DateTime(year, displayedMonth.month);
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: displayedMonth.year == year
                            ? const Color(0xFF3B82F6).withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        year.toString(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: displayedMonth.year == year
                              ? const Color(0xFF3B82F6)
                              : Colors.black54,
                          fontWeight: displayedMonth.year == year
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          _buildWeekdayHeaders(),
          const SizedBox(height: 10),
          _buildCalendarDays(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];
    return Row(
      children: weekdays.map((day) {
        bool isWeekend = day == 'Sa' || day == 'Su';
        return Expanded(
          child: Text(
            day,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isWeekend ? Colors.black38 : Colors.black54,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCalendarDays() {
    List<Widget> weeks = [];
    DateTime firstDayOfMonth =
        DateTime(displayedMonth.year, displayedMonth.month, 1);
    DateTime lastDayOfMonth =
        DateTime(displayedMonth.year, displayedMonth.month + 1, 0);

    // Get the first Monday to show
    DateTime firstDayToShow = firstDayOfMonth.subtract(
      Duration(days: (firstDayOfMonth.weekday - 1) % 7),
    );

    DateTime current = firstDayToShow;

    for (int week = 0; week < 6; week++) {
      List<Widget> days = [];

      for (int day = 0; day < 7; day++) {
        bool isCurrentMonth = current.month == displayedMonth.month;
        bool isSelected = selectedDate != null &&
            current.year == selectedDate!.year &&
            current.month == selectedDate!.month &&
            current.day == selectedDate!.day;
        bool isWeekend = current.weekday == 6 || current.weekday == 7;

        days.add(
          Expanded(
            child: GestureDetector(
              onTap: isCurrentMonth
                  ? () {
                      setState(() {
                        selectedDate = current;
                      });
                    }
                  : null,
              child: Container(
                height: 40,
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color:
                      isSelected ? const Color(0xFF1E40AF) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    current.day.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected
                          ? Colors.white
                          : isCurrentMonth
                              ? (isWeekend ? Colors.black38 : Colors.black87)
                              : Colors.black26,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );

        current = current.add(const Duration(days: 1));
      }

      weeks.add(Row(children: days));

      // Break if we've shown all days of the month and some days of next month
      if (current.month != displayedMonth.month && week >= 4) break;
    }

    return Column(children: weeks);
  }

  Widget _buildButtons() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: Color(0xFFE5E7EB)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: selectedDate != null
                  ? () {
                      widget.onDateSelected?.call(selectedDate!);
                      Navigator.of(context).pop(selectedDate);
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1E40AF),
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Select',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }
}

// Usage example:
class DatePickerDemo extends StatefulWidget {
  const DatePickerDemo({super.key});

  @override
  State<DatePickerDemo> createState() => _DatePickerDemoState();
}

class _DatePickerDemoState extends State<DatePickerDemo> {
  DateTime? selectedDate;

  void _showDatePicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: CustomDatePicker(
            initialDate: selectedDate ?? DateTime.now(),
            onDateSelected: (date) {
              setState(() {
                selectedDate = date;
              });
            },
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Custom Date Picker Demo')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              selectedDate != null
                  ? 'Selected: ${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                  : 'No date selected',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _showDatePicker,
              child: const Text('Show Date Picker'),
            ),
          ],
        ),
      ),
    );
  }
}
