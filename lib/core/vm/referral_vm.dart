import 'dart:convert';

import 'package:korrency/core/core.dart';

class ReferralVM extends BaseVM {
  List<Referral> _allRefList = [];
  List<Referral> get allRefList => _allRefList;
  List<Referral> get completedRefList => _allRefList.where((ref) {
        return ref.status?.toLowerCase() == "completed";
      }).toList();
  List<Referral> get pendingRefList => _allRefList
      .where((ref) => ref.status?.toLowerCase() != "completed")
      .toList();
  Earnings? _refEarnings;
  Earnings? get refEarnings => _refEarnings;

  ReferralWallet? _refWallet;
  ReferralWallet? get refWallet => _refWallet;

  Future<ApiResponse> getReferrals() async {
    return await performApiCall(
      url: "/referrals",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _allRefList = referralFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getReferralWallet() async {
    return await performApiCall(
      url: "/referrals/wallet",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _refWallet = ReferralWallet.fromJson(data["data"]);
        return ApiResponse(success: true, data: _refWallet);
      },
    );
  }

  Future<ApiResponse> getReferralEarnings() async {
    return await performApiCall(
      url: "/referrals/earnings",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _refEarnings = Earnings.fromJson(data["data"]);
        return ApiResponse(success: true, data: data);
      },
    );
  }

  Future<ApiResponse> claimRefBonus() async {
    return await performApiCall(
      url: "/referrals/claim",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  // For those who missed onboarding referrals
  Future<ApiResponse> getAddReferralCode(String refCode) async {
    return await performApiCall(
      url: "/referrals/add-referral-code",
      method: apiService.postWithAuth,
      body: {"referral_code": refCode},
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }
}
