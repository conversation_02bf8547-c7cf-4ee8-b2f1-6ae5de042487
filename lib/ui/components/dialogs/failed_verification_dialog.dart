// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class FailedVerificationDialog extends StatefulWidget {
  const FailedVerificationDialog({super.key});

  @override
  State<FailedVerificationDialog> createState() =>
      _FailedVerificationDialogState();
}

class _FailedVerificationDialogState extends State<FailedVerificationDialog> {
  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(builder: (ctx, loginVm, _) {
      return Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(Sizer.width(10)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppColors.white.withValues(alpha: 0.15),
                      border: Border.all(
                        color: AppColors.white.withValues(alpha: 0.70),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.close,
                      size: Sizer.width(16),
                      color: AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
            YBox(12),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const YBox(28),
                  SvgPicture.asset(
                    AppSvgs.failedVerify,
                    height: Sizer.height(140),
                  ),
                  YBox(20),
                  Text(
                    "Rejected",
                    textAlign: TextAlign.center,
                    style: FontTypography.text22.semiBold,
                  ),
                  YBox(4),
                  Text(
                    "Your ID documents were rejected due to the following reasons:",
                    // textAlign: TextAlign.center,
                    style: FontTypography.text16.withCustomColor(
                      AppColors.gray93,
                    ),
                  ),
                  const YBox(12),
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: Sizer.height(2)),
                        height: Sizer.height(7),
                        width: Sizer.width(7),
                        decoration: BoxDecoration(
                          color: AppColors.blueD8,
                          borderRadius: BorderRadius.circular(Sizer.radius(7)),
                        ),
                      ),
                      XBox(8),
                      Text(
                        "Image uploaded was blurry and unreadable",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.blueD8,
                        ),
                      )
                    ],
                  ),
                  YBox(6),
                  Row(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: Sizer.height(2)),
                        height: Sizer.height(7),
                        width: Sizer.width(7),
                        decoration: BoxDecoration(
                          color: AppColors.blueD8,
                          borderRadius: BorderRadius.circular(Sizer.radius(7)),
                        ),
                      ),
                      XBox(8),
                      Text(
                        "Date indicates expired document ",
                        style: AppTypography.text14.copyWith(
                          color: AppColors.blueD8,
                        ),
                      )
                    ],
                  ),
                  const YBox(32),
                  CustomBtn.solid(
                    onTap: () {
                      Navigator.pop(context);
                      BsWrapper.bottomSheet(
                        context: context,
                        widget: VerifyIdentifyModal(),
                      );
                    },
                    borderRadius: BorderRadius.circular(Sizer.radius(20)),
                    text: "Try Again",
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
