// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';

class DeepLinkHandler {
  static DeepLinkHandler? _instance;
  static DeepLinkHandler get instance => _instance ??= DeepLinkHandler._();

  DeepLinkHandler._();

  // Track if app was opened via deep link
  bool _appOpenedViaDeepLink = false;
  String? _pendingDeepLinkPath;

  /// Set flag when app is opened via deep link
  void setAppOpenedViaDeepLink(String? path) {
    _appOpenedViaDeepLink = true;
    _pendingDeepLinkPath = path;
    printty('📋 App opened via deep link: $path');
  }

  /// Check if app was opened via deep link
  bool get wasOpenedViaDeepLink => _appOpenedViaDeepLink;

  /// Get pending deep link path
  String? get pendingDeepLinkPath => _pendingDeepLinkPath;

  /// Clear deep link flags
  void clearDeepLinkFlags() {
    _appOpenedViaDeepLink = false;
    _pendingDeepLinkPath = null;
  }

  /// Process raw deep link data and determine routing action
  DeepLinkData processDeepLink(Map<String, dynamic> clickEvent) {
    final ctx = NavigatorKeys.appNavigatorKey.currentContext;
    try {
      printty('🔍 Processing deep link click event: $clickEvent');

      // Determine deep link type and target route
      DeepLinkTypeData typeData = _determineDeepLinkType(clickEvent);
      String? targetRoute =
          _determineTargetRoute(typeData.deeplinkType, clickEvent);

      final deepLinkData = DeepLinkData(
        type: typeData.deeplinkType,
        parameters: clickEvent,
        referralCode: typeData.code,
        targetRoute: targetRoute,
        code: typeData.code,
      );

      // Set deeplink data
      ctx?.read<DeepLinkVm>().setDeepLinkData(deepLinkData);

      printty('✅ Processed deep link: $deepLinkData');
      return deepLinkData;
    } catch (e) {
      printty('❌ Error processing deep link: $e');
      return DeepLinkData(
        type: DeepLinkType.gbpWallet,
        parameters: clickEvent,
      );
    }
  }

  /// Execute the routing action based on processed deep link data
  Future<void> executeDeepLink(DeepLinkData deepLinkData,
      {Wallet? wallet}) async {
    try {
      final context = NavigatorKeys.appNavigatorKey.currentContext;
      if (context == null) return;

      // Handle different types of deep links
      switch (deepLinkData.type) {
        case DeepLinkType.referral:
          return await _handleReferralDeepLink(context, deepLinkData);
        case DeepLinkType.general:
          return await _handleGeneralDeepLink(context, deepLinkData);
        case DeepLinkType.transaction:
          return await _handleTransactionDeepLink(context, deepLinkData);
        case DeepLinkType.profile:
          return await _handleProfileDeepLink(context, deepLinkData);
        case DeepLinkType.beneficiaries:
          return await _handleBeneficiariesDeepLink(context, deepLinkData);
        case DeepLinkType.privacySecurity:
          return await _handlePrivacySecurityDeepLink(context, deepLinkData);
        case DeepLinkType.helpSupport:
          return await _handleHelpSupportDeepLink(context, deepLinkData);
        case DeepLinkType.accountLimit:
          return await _handleAccountLimitDeepLink(
              context, deepLinkData, wallet);
        case DeepLinkType.exchangeRate:
          return await _handleExchangeRateDeepLink(context, deepLinkData);
        case DeepLinkType.convertMoney:
          return await _handleConvertMoneyDeepLink(context, deepLinkData);
        case DeepLinkType.ngnWallet:
          return await _handleNgnWalletDeepLink(context, deepLinkData, wallet);
        case DeepLinkType.cadWallet:
          return await _handleCadWalletDeepLink(context, deepLinkData, wallet);
        case DeepLinkType.gbpWallet:
          return await _handleGbpWalletDeepLink(context, deepLinkData, wallet);
        case DeepLinkType.euroWallet:
          return await _handleOtherWalletDeepLink(
              context, deepLinkData, wallet);
        // default:
        //   return await _handleGeneralDeepLink(context, deepLinkData);
      }
    } catch (e) {
      printty('❌ Error executing deep link: $e');
    }
  }

  /// Determine the type of deep link
  DeepLinkTypeData _determineDeepLinkType(Map<String, dynamic> clickEvent) {
    // Normalize the path once
    final String? rawPath = clickEvent['path']?.toString();
    if (rawPath == null || rawPath.isEmpty) {
      return const DeepLinkTypeData(deeplinkType: DeepLinkType.general);
    }

    // Split and sanitize segments
    final parts = rawPath
        .toLowerCase()
        .split('/')
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    if (parts.length < 2) {
      return const DeepLinkTypeData(deeplinkType: DeepLinkType.general);
    }

    // "/nmB9/ref/202020"
    final String second = parts[1];
    final String? third = parts.length > 2 ? parts[2] : null;

    // Map of recognized second-segment values to their types
    const typeMap = <String, DeepLinkType>{
      'ref': DeepLinkType.referral,
      'referral': DeepLinkType.referral,
      'transaction': DeepLinkType.transaction,
      'transactions': DeepLinkType.transaction,
      'profile': DeepLinkType.profile,
      'user': DeepLinkType.profile,
      'exchange_rate': DeepLinkType.exchangeRate,
      'exchange': DeepLinkType.exchangeRate,
      'cad': DeepLinkType.cadWallet,
      'gbp': DeepLinkType.gbpWallet,
      'eur': DeepLinkType.euroWallet,
      'ngn': DeepLinkType.ngnWallet,
      'convert_money': DeepLinkType.convertMoney,
      'beneficiaries': DeepLinkType.beneficiaries,
      'privacy_security': DeepLinkType.privacySecurity,
      'help_support': DeepLinkType.helpSupport,
      'account_limit': DeepLinkType.accountLimit,
    };

    final type = typeMap[second] ?? DeepLinkType.general;

    // Use third segment if available, otherwise fallback to second (Cus of wallet links)
    return DeepLinkTypeData(deeplinkType: type, code: third ?? second);
  }

  /// Determine target route based on deep link type
  String? _determineTargetRoute(
      DeepLinkType type, Map<String, dynamic> clickEvent) {
    switch (type) {
      case DeepLinkType.referral:
        return RoutePath.createAcctScreen;
      case DeepLinkType.transaction:
        return RoutePath.transactionScreen;
      case DeepLinkType.profile:
        return RoutePath.profileScreen;
      case DeepLinkType.general:
      default:
        return RoutePath.dashboardNav;
    }
  }

  /// Handle referral deep links
  Future<void> _handleReferralDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      // Store referral code for signup process
      if (data.referralCode != null) {
        await StorageService.storeString(
            StorageKey.deepLinkValue, data.referralCode!);

        // Set in OnBoardVM if available
        try {
          context.read<OnBoardVM>().setDeepLinkReferralCode(data.referralCode!);
          printty('✅ Referral code set in OnBoardVM');
        } catch (e) {
          printty(
              '⚠️ OnBoardVM not available, referral stored for later use: $e');
        }
      }

      // Log analytics event
      AppsFlyerService.instance.logEvent('referral_deep_link_opened', {
        'referral_code': data.referralCode,
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      printty('❌ Error handling referral deep link: $e');
    }
  }

  /// Handle general deep links
  Future<void> _handleGeneralDeepLink(
      BuildContext context, DeepLinkData data) async {
    try {
      // Log analytics event
      AppsFlyerService.instance.logEvent('general_deep_link_opened', {
        'campaign': data.parameters['campaign'],
        'media_source': data.parameters['media_source'],
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      printty('❌ Error handling general deep link: $e');
    }
  }

  /// Handle transaction-related deep links
  Future<void> _handleTransactionDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('transaction_deep_link_opened', {
      'screen': 'transaction_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.dashboardNav, arguments: 1);
  }

  /// Handle profile-related deep links
  Future<void> _handleProfileDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('profile_deep_link_opened', {
      'screen': 'profile_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.dashboardNav, arguments: 4);
    Navigator.pushNamed(context, RoutePath.profileScreen);
  }

  /// Handle Help support deep links
  Future<void> _handleHelpSupportDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('help_support_deep_link_opened', {
      'screen': 'help_support_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.dashboardNav, arguments: 4);
    Navigator.pushNamed(context, RoutePath.helpSupportScreen);
  }

  /// Handle Privacy Security deep links
  Future<void> _handlePrivacySecurityDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('privacy_security_deep_link_opened', {
      'screen': 'privacy_security_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.dashboardNav, arguments: 4);
    Navigator.pushNamed(context, RoutePath.securityScreen);
  }

  /// Handle Beneficiaries deep links
  Future<void> _handleBeneficiariesDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('beneficiaries_deep_link_opened', {
      'screen': 'beneficiaries_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.dashboardNav, arguments: 4);
    Navigator.pushNamed(context, RoutePath.beneficiaryScreen);
  }

  /// Handle Exchange Rate deep links
  Future<void> _handleExchangeRateDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('exchange_rate_deep_link_opened', {
      'screen': 'exchange_rate_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });

    Navigator.pushNamed(context, RoutePath.exchangeRateScreen);
  }

  /// Handle Account Limit deep links
  Future<void> _handleAccountLimitDeepLink(
      BuildContext context, DeepLinkData data, Wallet? wallet) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('account_limit_deep_link_opened', {
      'screen': 'account_limit_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    Navigator.pushNamed(
      context,
      RoutePath.accountLimitScreen,
      arguments: wallet?.currency,
    );
  }

  /// Handle convert money deep links
  Future<void> _handleConvertMoneyDeepLink(
      BuildContext context, DeepLinkData data) async {
    // Log analytics event
    AppsFlyerService.instance.logEvent('convert_money_deep_link_opened', {
      'screen': 'convert_money_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    Navigator.pushNamed(context, RoutePath.convertCurrencyScreen);
  }

  /// Handle ngn wallet deep links
  Future<void> _handleNgnWalletDeepLink(
      BuildContext context, DeepLinkData data, Wallet? wallet) async {
    printty("Wallet $wallet");
    // Log analytics event
    AppsFlyerService.instance.logEvent('ngn_wallet_deep_link_opened', {
      'screen': 'ngn_wallet_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    if (wallet != null) {
      Navigator.pushNamed(
        context,
        RoutePath.ngnWalletDetailsScreen,
        arguments: wallet,
      );
    }
  }

  /// Handle cad wallet deep links
  Future<void> _handleCadWalletDeepLink(
      BuildContext context, DeepLinkData data, Wallet? wallet) async {
    printty("Wallet $wallet");
    // Log analytics event
    AppsFlyerService.instance.logEvent('cad_wallet_deep_link_opened', {
      'screen': 'cad_wallet_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    if (wallet != null) {
      Navigator.pushNamed(
        context,
        RoutePath.cadWalletDetailsScreen,
        arguments: wallet,
      );
    }
  }

  /// Handle gbp wallet deep links
  Future<void> _handleGbpWalletDeepLink(
      BuildContext context, DeepLinkData data, Wallet? wallet) async {
    printty("Wallet $wallet");
    // Log analytics event
    AppsFlyerService.instance.logEvent('gbp_wallet_deep_link_opened', {
      'screen': 'gbp_wallet_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    if (wallet != null) {
      Navigator.pushNamed(
        context,
        RoutePath.gbpWalletDetailsScreen,
        arguments: wallet,
      );
    }
  }

  /// Handle other wallet deep links
  Future<void> _handleOtherWalletDeepLink(
      BuildContext context, DeepLinkData data, Wallet? wallet) async {
    printty("Wallet $wallet");
    // Log analytics event
    AppsFlyerService.instance.logEvent('other_wallet_deep_link_opened', {
      'screen': 'other_wallet_screen',
      'timestamp': DateTime.now().toIso8601String(),
    });
    if (wallet != null) {
      Navigator.pushNamed(
        context,
        RoutePath.otherWalletDetailsScreen,
        arguments: wallet,
      );
    }
  }
}
