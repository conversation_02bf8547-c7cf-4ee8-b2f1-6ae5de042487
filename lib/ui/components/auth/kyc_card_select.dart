import 'package:korrency/core/core.dart';

import 'auth_text_subtitle.dart';

class KycCardSelect extends StatelessWidget {
  const KycCardSelect({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    this.isSelected = false,
    this.btnIsInactive = false,
    this.onTap,
  });

  final String title;
  final String subtitle;
  final IconData icon;
  final bool isSelected;
  final bool btnIsInactive;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: btnIsInactive ? null : onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(10),
          vertical: Sizer.height(18),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.opacityGreen100 : AppColors.blue100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
            ),
            const XBox(10),
            AuthTextSubTitle(
              title: title,
              titleColor: AppColors.blue900,
              titleFontSize: 16,
              subtitle: subtitle,
              subtitleColor: AppColors.black600,
              subtitleFontSize: 14,
            ),
            if (isSelected) const Spacer(),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                size: 20,
                color: AppColors.baseGreen,
              ),
          ],
        ),
      ),
    );
  }
}
