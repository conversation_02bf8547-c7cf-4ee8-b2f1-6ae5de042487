import 'dart:async';

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AnimatedExchangeRateWidget extends StatefulWidget {
  const AnimatedExchangeRateWidget({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  State<AnimatedExchangeRateWidget> createState() =>
      _AnimatedExchangeRateWidgetState();
}

class _AnimatedExchangeRateWidgetState extends State<AnimatedExchangeRateWidget>
    with TickerProviderStateMixin {
  late AnimationController _borderController;
  late Animation<Color?> _borderColorAnimation;

  int _currentIndex = 0;
  late Timer _timer;

  @override
  void initState() {
    super.initState();

    _borderController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _borderColorAnimation = ColorTween(
      begin: AppColors.blueEF7,
      end: AppColors.primaryBlue,
    ).animate(CurvedAnimation(
      parent: _borderController,
      curve: Curves.easeInOut,
    ));

    // Start the border animation
    _borderController.repeat(reverse: true);

    // Start the timer for cycling through exchange rates
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      _cycleToNext();
    });
  }

  void _cycleToNext() {
    final rates = context.read<CurrencyVM>().ratesByCurrencyId?.rates;
    if (rates == null || rates.isEmpty) {
      _currentIndex = 0;
      return;
    }

    setState(() {
      _currentIndex = (_currentIndex + 1) % rates.length;
      // Additional safety check
      if (_currentIndex >= rates.length) {
        _currentIndex = 0;
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _borderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currencyVm = context.watch<CurrencyVM>();
    List<KRate> rates = currencyVm.ratesByCurrencyId?.rates ?? [];

    // Ensure _currentIndex is within bounds
    if (rates.isNotEmpty && _currentIndex >= rates.length) {
      _currentIndex = 0;
    }

    // If no rates available, show empty container
    if (rates.isEmpty) {
      return Container();
    }

    return InkWell(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _borderColorAnimation,
        builder: (context, child) {
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 800),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            child: HomeExchangeRateWidget(
              key: ValueKey(_currentIndex),
              firstFlag: currencyVm.ratesByCurrencyId?.flag ?? "",
              secondFlag: rates[_currentIndex].flag ?? "",
              exchangeRate:
                  "1 ${currencyVm.ratesByCurrencyId?.code} = ${AppUtils.formatAmountDoubleString(rates[_currentIndex].rate ?? "0")} ${rates[_currentIndex].to}",
              borderColor: _borderColorAnimation.value,
            ),
          );
        },
      ),
    );
  }
}
