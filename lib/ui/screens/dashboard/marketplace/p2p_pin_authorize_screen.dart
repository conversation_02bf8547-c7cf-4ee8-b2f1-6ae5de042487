import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/confirmation.dart';

class P2pPinAuthorizeScreen extends StatefulWidget {
  const P2pPinAuthorizeScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<P2pPinAuthorizeScreen> createState() => _P2pPinAuthorizeScreenState();
}

class _P2pPinAuthorizeScreenState extends State<P2pPinAuthorizeScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<MarketPlaceOfferVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const YBox(20),
                  const ArrowBack(),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Authorize Transaction",
                    subtitle:
                        "Enter your 4 digit PIN to authorize this transaction",
                  ),
                  const YBox(30),
                  Expanded(
                    child: TransactionPIN(
                      onSubmit: (pin) {
                        vm.setPin(pin);
                      },
                    ),
                  ),
                  const YBox(50),
                  CustomBtn.solid(
                    onTap: () {
                      _acceptOffer();
                    },
                    online: vm.pin?.isNotEmpty ?? false,
                    text: "Authorize",
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _acceptOffer() {
    context.read<MarketPlaceOfferVM>().acceptOffer().then((value) {
      if (value.success) {
        _moveToSuccessScreen();
      } else {
        _moveToFailedScreen(msg: value.message);
      }
    });
  }

  _moveToSuccessScreen() {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: "Exchange Successful",
        subTitle: "Funds have been transferred to your wallet",
        secondBtnText: "Home",
        btnText: "Browse Offers",
        btnTap: () => _dashboadNav(4),
        secondBtnTap: () => _dashboadNav(0),
      ),
    );
  }

  _dashboadNav(int index) {
    context.read<MarketPlaceOfferVM>().clearData();
    Navigator.pushNamedAndRemoveUntil(
      context,
      RoutePath.dashboardNav,
      arguments: index,
      (route) => false,
    );
  }

  _moveToFailedScreen({String? msg}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () => _dashboadNav(3),
        title: "Operation Failed",
        subTitle: msg ??
            "Please check and confirm the offer details if it has changed.",
      ),
    );
  }
}
