import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class VerifyInteracEmailScreen extends StatefulWidget {
  const VerifyInteracEmailScreen({
    super.key,
    required this.email,
  });

  final String email;

  @override
  State<VerifyInteracEmailScreen> createState() =>
      _VerifyInteracEmailScreenState();
}

class _VerifyInteracEmailScreenState extends State<VerifyInteracEmailScreen> {
  final pinC = TextEditingController();
  final focusNode = FocusNode();

  @override
  void dispose() {
    pinC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Add Interac email',
            onBackBtnTap: () {
              Navigator.pop(context);
            },
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Please enter the 6-digit code sent to ",
                  style: AppTypography.text16.copyWith(color: AppColors.gray93),
                ),
                Text(
                  widget.email,
                  style: AppTypography.text16
                      .copyWith(color: AppColors.primaryBlue90),
                ),
                const YBox(50),
                const YBox(30),
                Pinput(
                  defaultPinTheme: PinInputTheme.changeDefaultPinTheme(),
                  followingPinTheme: PinInputTheme.changePinTheme(),
                  focusedPinTheme: PinInputTheme.changeFocusPinTheme(),
                  submittedPinTheme: PinInputTheme.changePinTheme(),
                  length: 6,
                  controller: pinC,
                  focusNode: focusNode,
                  showCursor: true,
                  onChanged: (value) => vm.reBuildUI(),
                  onCompleted: (pin) {
                    _verifyInteracEmail();
                  },
                ),
                const YBox(24),
                ResendCode(
                  onResendCode: () async {
                    final res = await vm.requestInteracEmail(widget.email);
                    handleApiResponse(response: res);
                  },
                ),
                const Spacer(),
                CustomBtn.withChild(
                  onTap: () {
                    _verifyInteracEmail();
                  },
                  online: pinC.text.trim().length == 6,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  child: ContinueText(isOnline: pinC.text.trim().length == 6),
                ),
                const YBox(40),
              ],
            ),
          ),
        ),
      );
    });
  }

  _verifyInteracEmail() async {
    final res = await context
        .read<InteracVM>()
        .verifyInteracEmail(widget.email, pinC.text.trim());

    handleApiResponse(
        response: res,
        onSuccess: () {
          BsWrapper.showCustomDialog(
            context,
            canDismiss: false,
            child: ConfirmationDialog(
              args: ConfirmationDialogArgs(
                title: "Interac Email added \nsuccessfully!",
                content:
                    "Your Interac email was verified and added successfully, Please proceed by clicking the button below",
                btnText: "Done",
                onTap: () {
                  Navigator.pushNamedAndRemoveUntil(
                      context, RoutePath.dashboardNav, (_) => false);
                },
              ),
            ),
          );
        });
    //     .then((value) {
    //   if (value.success) {
    //     if (mounted) {
    //       Navigator.pushNamed(context, RoutePath.interacEmailConfirmationScreen,
    //           arguments: StatementConfirmScreenArgs(
    //             title: "Interac email added",
    //             message: value.message ?? "Interac email verified successfully",
    //           ));
    //     }
    //   } else {
    //     FlushBarToast.fLSnackBar(
    //       message: value.message.toString(),
    //     );
    //   }
    // });
  }
}

//  "Interac email added",
