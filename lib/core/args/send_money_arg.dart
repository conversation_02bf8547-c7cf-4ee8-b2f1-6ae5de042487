import 'package:korrency/core/core.dart';

class SendMoneyArg {
  SendMoneyArg({
    required this.fromCurrencyId,
    required this.toCurrencyId,
    this.fromCode,
    this.toCode,
    this.fromWallet,
    this.fromAmount,
    this.beneficiary,
    this.purpose,
  });

  final int fromCurrencyId;
  final int toCurrencyId;

  // For Exchange rate screen
  final String? fromCode;
  final String? toCode;

  /// Optional, just for the wallet details
  final Wallet? fromWallet;
  final String? fromAmount;
  final Beneficiary? beneficiary;
  final SendMoneyPurpose? purpose;
}
