import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AddWalletPickerModal extends StatefulWidget {
  const AddWalletPickerModal({super.key});

  @override
  State<AddWalletPickerModal> createState() => _AddWalletPickerModalState();
}

class _AddWalletPickerModalState extends State<AddWalletPickerModal> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int? selectedCurrencyId;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: SizedBox(
        height: Sizer.screenHeight * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(Sizer.width(10)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppColors.white.withValues(alpha: 0.15),
                      border: Border.all(
                        color: AppColors.white.withValues(alpha: 0.70),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          "Close",
                          style: AppTypography.text14.medium.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                        XBox(4),
                        Icon(
                          Icons.close,
                          size: Sizer.width(16),
                          color: AppColors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const YBox(12),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    YBox(20),
                    Text(
                      "Add Wallet",
                      style: AppTypography.text20.semiBold,
                    ),
                    YBox(20),
                    CustomTextField(
                      controller: _searchController,
                      prefixIcon: Padding(
                        padding: EdgeInsets.all(Sizer.width(12)),
                        child: SvgPicture.asset(AppSvgs.search),
                      ),
                      hintText: "Search currency or country",
                      keyboardType: KeyboardType.regular,
                      inputFormatters: [],
                      borderRadius: Sizer.height(40),
                      onChanged: (val) {
                        // Search is handled by the listener
                      },
                      onSubmitted: (val) {
                        // Optional: Handle search submission
                      },
                    ),
                    YBox(10),
                    Expanded(
                      child: ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.only(
                          top: Sizer.height(10),
                          bottom: Sizer.height(20),
                        ),
                        itemCount: _getFilteredCurrencies().length,
                        separatorBuilder: (_, __) => YBox(12),
                        itemBuilder: (ctx, i) {
                          final currencyWallet = _getFilteredCurrencies()[i];
                          return AddWalletPickerItem(
                            flag: currencyWallet.flag ?? '',
                            country: currencyWallet.name ?? '',
                            sunTitle: currencyWallet.country ?? '',
                            isLoading: selectedCurrencyId == currencyWallet.id
                                ? context.watch<WalletVM>().busy(createState)
                                : false,
                            onTap: () async {
                              selectedCurrencyId = currencyWallet.id;
                              setState(() {});
                              printty("currencyWallet: ${currencyWallet.flag}");
                              if (!context.read<AuthUserVM>().userIsVerified) {
                                showWarningToast(
                                    'Complete your KYC to add wallet');
                                return;
                              }

                              final res = await context
                                  .read<WalletVM>()
                                  .createVirtualAccount(
                                      currencyId: currencyWallet.id ?? 0);

                              handleApiResponse(
                                  response: res,
                                  onSuccess: () {
                                    context
                                        .read<WalletVM>()
                                        .getWallets(busyState: getState);
                                    Navigator.pop(context);
                                  });
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Currency> _getAvailableCurrencies() {
    final walletList = context.read<WalletVM>().walletList;
    final creatableCurrencies = context.read<CurrencyVM>().creatableCurrencies;

    // Filter out currencies that already have wallets
    final availableCurrencies = creatableCurrencies.where((currency) {
      return !walletList
          .any((wallet) => wallet.currency?.code == currency.code);
    }).toList();

    debugPrint('Available currencies: ${availableCurrencies.length}');

    return availableCurrencies;
  }

  List<Currency> _getFilteredCurrencies() {
    final availableCurrencies = _getAvailableCurrencies();

    if (_searchQuery.isEmpty) {
      return availableCurrencies;
    }

    return availableCurrencies.where((currency) {
      final name = currency.name?.toLowerCase() ?? '';
      final country = currency.country?.toLowerCase() ?? '';
      final code = currency.code?.toLowerCase() ?? '';

      return name.contains(_searchQuery) ||
          country.contains(_searchQuery) ||
          code.contains(_searchQuery);
    }).toList();
  }

  // _getWalletCredential() {
  //   context.read<AuthUserVM>().getAuthUser();
  //   context.read<WalletVM>().getWallets();
  //   context.read<CurrencyVM>().getCurrencies();
  //   context.read<TransactionVM>().getTransactions();
  // }
}

class AddWalletPickerItem extends StatelessWidget {
  const AddWalletPickerItem({
    super.key,
    required this.flag,
    required this.country,
    required this.sunTitle,
    this.isLoading = false,
    this.onTap,
  });

  final String flag;
  final String country;
  final String sunTitle;
  final bool isLoading;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(Sizer.radius(10)),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(30),
              child: SizedBox(
                width: Sizer.width(36),
                height: Sizer.height(36),
                child: SvgPicture.network(
                  flag,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            XBox(12),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    country,
                    style: AppTypography.text16.copyWith(
                      color: AppColors.gray51,
                    ),
                  ),
                  Text(
                    sunTitle,
                    style: AppTypography.text12.copyWith(
                      color: AppColors.gray79,
                    ),
                  ),
                ],
              ),
            ),
            isLoading
                ? CupertinoActivityIndicator()
                : InkWell(
                    onTap: onTap,
                    child: SvgPicture.asset(AppSvgs.addCircle),
                  )
          ],
        ),
      ),
    );
  }
}
