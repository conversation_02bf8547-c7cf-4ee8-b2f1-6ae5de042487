import 'package:korrency/core/core.dart';

typedef AsyncVoidCallback = Future<void> Function();

class ExportFormatModal extends StatefulWidget {
  const ExportFormatModal({
    super.key,
    this.isDownload = false,
    this.asImage,
    this.asPdf,
  });

  final bool isDownload;
  final AsyncVoidCallback? asImage;
  final AsyncVoidCallback? asPdf;

  @override
  State<ExportFormatModal> createState() => _ExportFormatModalState();
}

class _ExportFormatModalState extends State<ExportFormatModal> {
  bool _isProcessing = false;

  Future<void> _handleTap(AsyncVoidCallback? action) async {
    if (action == null || _isProcessing) return;
    setState(() {
      _isProcessing = true;
    });
    try {
      await action();
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(44)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            YBox(24),
            Text(
              'We would love to hear from you',
              textAlign: TextAlign.center,
              style: FontTypography.text16.withCustomColor(
                AppColors.gray51,
              ),
            ),
            const YBox(30),
            Row(
              children: [
                Expanded(
                  child: ExportWidget(
                    svgPath: AppSvgs.galleryExport,
                    desc:
                        '${widget.isDownload ? 'Download' : 'Export'} as Image',
                    onTap: () => _handleTap(widget.asImage),
                    isDisabled: _isProcessing || widget.asImage == null,
                  ),
                ),
                const XBox(38),
                Expanded(
                  child: ExportWidget(
                    svgPath: AppSvgs.pdfExport,
                    desc: '${widget.isDownload ? 'Download' : 'Export'} as PDF',
                    onTap: () => _handleTap(widget.asPdf),
                    isDisabled: _isProcessing || widget.asPdf == null,
                    // onTap: () {
                    //   BsWrapper.bottomSheet(
                    //     context: context,
                    //     widget: DownloadSuccessModal(),
                    //   );
                    // },
                  ),
                )
              ],
            ),
            const YBox(50),
          ],
        ),
      ),
    );
  }
}

class ExportWidget extends StatelessWidget {
  const ExportWidget({
    super.key,
    required this.svgPath,
    required this.desc,
    this.onTap,
    this.isDisabled = false,
  });

  final String svgPath;
  final String desc;
  final VoidCallback? onTap;
  final bool isDisabled;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      enabled: !isDisabled,
      label: desc,
      child: InkWell(
        onTap: isDisabled ? null : onTap,
        mouseCursor: isDisabled
            ? SystemMouseCursors.forbidden
            : SystemMouseCursors.click,
        child: Column(
          children: [
            Container(
              height: Sizer.height(120),
              decoration: BoxDecoration(
                color: AppColors.blue8FF,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Opacity(
                  opacity: isDisabled ? 0.6 : 1.0,
                  child: svgHelper(
                    svgPath,
                    height: Sizer.height(60),
                    width: Sizer.width(60),
                  ),
                ),
              ),
            ),
            const YBox(12),
            Text(
              desc,
              style: FontTypography.text12.withCustomColor(
                AppColors.primaryBlue,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
