import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyBeneficiaryListScreen extends StatefulWidget {
  const SendMoneyBeneficiaryListScreen({
    super.key,
    required this.transferMethodArg,
  });

  /// NOTE: if namecheck is true
  /// Add validation for account name
  /// if  false, do no validation
  final TransferMethodArg transferMethodArg;

  @override
  State<SendMoneyBeneficiaryListScreen> createState() =>
      _SendMoneyBeneficiaryListScreenState();
}

class _SendMoneyBeneficiaryListScreenState
    extends State<SendMoneyBeneficiaryListScreen> {
  final _searchC = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });

    // Add listener to search controller
    _searchC.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    final beneVm = context.read<BeneficiaryVM>();
    beneVm.searchBeneficiariesByCurrencyId(_searchC.text);
  }

  _init() {
    final sendMoneyVm = context.read<SendMoneyVM>();
    final beneVm = context.read<BeneficiaryVM>();
    beneVm.getBeneficiaries(
      currencyId: sendMoneyVm.recipientCurrency?.id,
      transferMethod: widget.transferMethodArg.paymentMethod.id,
    );
  }

  @override
  void dispose() {
    _searchC.removeListener(_onSearchChanged);
    _searchC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final beneVm = context.watch<BeneficiaryVM>();

    return BusyOverlay(
      show: beneVm.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.bgWhite,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: widget.transferMethodArg.title,
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          child: Column(
            children: [
              const YBox(20),
              CustomBtn.withChild(
                height: 48,
                onTap: () {
                  _switchMethod();
                },
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "New Beneficiary",
                      style: AppTypography.text16.medium.copyWith(
                        color: AppColors.white,
                      ),
                    ),
                    XBox(8),
                    SvgPicture.asset(
                      AppSvgs.addCircle,
                      colorFilter:
                          ColorFilter.mode(AppColors.white, BlendMode.srcIn),
                      height: Sizer.height(16),
                    )
                  ],
                ),
              ),
              const YBox(16),
              CustomTextField(
                controller: _searchC,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(Sizer.radius(12)),
                  child: SvgPicture.asset(AppSvgs.search),
                ),
                hintText: "Search Beneficiaries",
                keyboardType: KeyboardType.regular,
                inputFormatters: [],
                borderRadius: Sizer.height(20),
              ),
              YBox(6),
              Expanded(
                child: beneVm.beneficiariesByCurrencyId.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: AppColors.gray500,
                            ),
                            const YBox(16),
                            Text(
                              _searchC.text.isEmpty
                                  ? "No beneficiaries found"
                                  : "No beneficiaries match your search",
                              style: AppTypography.text16.copyWith(
                                color: AppColors.gray500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.separated(
                        padding: EdgeInsets.only(
                          top: Sizer.width(16),
                          bottom: Sizer.width(50),
                        ),
                        itemBuilder: (ctx, i) {
                          var beneficiary = beneVm.beneficiariesByCurrencyId[i];
                          return SendMoneyBenrficiaryListTile(
                            name: beneficiary.accountName ?? "",
                            title: beneficiary.accountName ?? "",
                            subtitle:
                                " ${beneficiary.accountIdentifier ?? "N/A"}",
                            iconPath: beneficiary.iconUrl ?? "",
                            onTap: () {
                              _switchMethod(beneficiary);
                            },
                          );
                        },
                        separatorBuilder: (ctx, _) => const YBox(8),
                        itemCount: beneVm.beneficiariesByCurrencyId.length,
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _switchMethod([Beneficiary? beneficiary]) {
    final method = widget.transferMethodArg.paymentMethod;
    if (method.id?.toLowerCase() == 'bank_transfer') {
      // vm.autoFillAcctNumberName(beneficiary);
      Navigator.pushNamed(
        context,
        RoutePath.sendToNgnScreen,
        arguments: TransferMethodArg(
          paymentMethod: method,
          beneficiary: beneficiary,
        ),
      );
    } else if (method.id?.toLowerCase() == 'interac') {
      // vm.autoFillInteracEmailName(beneficiary);
      Navigator.pushNamed(
        context,
        RoutePath.sendToCadScreen,
        arguments: TransferMethodArg(
          paymentMethod: method,
          beneficiary: beneficiary,
        ),
      );
    } else if (method.id?.toLowerCase() == 'mobile_money') {
      Navigator.pushNamed(
        context,
        RoutePath.sendMoneyMobileMoneyScreen,
        arguments: TransferMethodArg(
          paymentMethod: method,
          beneficiary: beneficiary,
        ),
      );
    } else if (method.id?.toLowerCase() == 'iban') {
      Navigator.pushNamed(
        context,
        RoutePath.sendToIbanScreen,
        arguments: TransferMethodArg(
          paymentMethod: method,
          beneficiary: beneficiary,
        ),
      );
    } else {
      Navigator.pushNamed(
        context,
        RoutePath.sendToKorrencyUser,
        arguments: method,
      );
    }
  }
}
