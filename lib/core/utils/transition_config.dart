// import 'package:korrency/core/core.dart';

// class TransitionConfig {
//   final Widget Function(RouteSettings settings) pageBuilder;
//   final TransitionType transition;
//   final Duration duration;
//   final Curve curve;
//   final RouteTransitionsBuilder? customTransition;

//   TransitionConfig({
//     required this.pageBuilder,
//     this.transition = TransitionType.fade,
//     this.duration = const Duration(milliseconds: 300),
//     this.curve = Curves.easeInOut,
//     this.customTransition,
//   });

//   // Helper constructor for routes without arguments
//   TransitionConfig.static({
//     required Widget page,
//     TransitionType transition = TransitionType.fade,
//     Duration duration = const Duration(milliseconds: 300),
//     Curve curve = Curves.easeInOut,
//     RouteTransitionsBuilder? customTransition,
//   }) : this(
//           pageBuilder: (_) => page,
//           transition: transition,
//           duration: duration,
//           curve: curve,
//           customTransition: customTransition,
//         );
// }
