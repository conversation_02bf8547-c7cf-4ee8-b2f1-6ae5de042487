import 'dart:convert';

OnfidoData onfidoDataFromJson(String str) =>
    OnfidoData.fromJson(json.decode(str));

String onfidoDataToJson(OnfidoData data) => json.encode(data.toJson());

class OnfidoData {
  String? applicantId;
  String? sdkToken;
  String? workflowRunId;

  OnfidoData({
    this.applicantId,
    this.sdkToken,
    this.workflowRunId,
  });

  factory OnfidoData.fromJson(Map<String, dynamic> json) => OnfidoData(
        applicantId: json["applicant_id"],
        sdkToken: json["sdk_token"],
        workflowRunId: json["workflow_run_id"],
      );

  Map<String, dynamic> toJson() => {
        "applicant_id": applicantId,
        "sdk_token": sdkToken,
        "workflow_run_id": workflowRunId,
      };
}
