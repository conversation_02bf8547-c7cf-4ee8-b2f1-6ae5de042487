// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:korrency/core/core.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';

class TransactionReceiptSheet extends StatefulWidget {
  const TransactionReceiptSheet({
    super.key,
    required this.imageScreenshot,
    required this.directoryPath,
  });

  final File imageScreenshot;
  final String directoryPath;

  @override
  State<TransactionReceiptSheet> createState() =>
      _TransactionReceiptSheetState();
}

class _TransactionReceiptSheetState extends State<TransactionReceiptSheet> {
  bool makeChange = false;
  late File asset;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        makeChange = true;
        asset = widget.imageScreenshot;
      });
    });
  }

  @override
  void dispose() {
    printty('Disposed called');
    super.dispose();
    asset.delete();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      child: Dismissible(
        key: const Key('dismised key'),
        direction: DismissDirection.down,
        onDismissed: (_) => Navigator.pop(context),
        child: AlertDialog(
          scrollable: false,
          contentPadding: EdgeInsets.zero,
          buttonPadding: EdgeInsets.zero,
          insetPadding:
              EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.09),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(15), topLeft: Radius.circular(15))),
          backgroundColor: Colors.white,
          titlePadding: EdgeInsets.zero,
          actionsPadding: EdgeInsets.zero,
          content: ClipRRect(
            borderRadius: const BorderRadius.only(
                topRight: Radius.circular(15), topLeft: Radius.circular(15)),
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.64,
                      child: Stack(children: [
                        Container(
                            padding: EdgeInsets.only(
                                top: Sizer.height(25),
                                bottom: Sizer.height(16),
                                left: Sizer.width(25),
                                right: Sizer.width(25)),
                            height: MediaQuery.of(context).size.height * 0.24,
                            width: MediaQuery.of(context).size.width,
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Color.fromARGB(255, 20, 5, 93),
                                  Color.fromARGB(255, 84, 84, 235),
                                ],
                                begin: Alignment.bottomLeft,
                                end: Alignment.topRight,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                        onTap: () {
                                          return Navigator.pop(context);
                                        },
                                        child: Icon(
                                          Icons.clear,
                                          color: Colors.white,
                                          size: Sizer.width(26),
                                        ))),
                                Material(
                                  color: Colors.transparent,
                                  child: Text("Receipt",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: Sizer.width(18),
                                      )),
                                ),
                                Visibility(
                                  visible: false,
                                  maintainSize: true,
                                  maintainAnimation: true,
                                  maintainState: true,
                                  child: Icon(
                                    Icons.clear,
                                    color: Colors.white,
                                    size: Sizer.width(26),
                                  ),
                                ),
                              ],
                            )),
                        Container(
                          margin: EdgeInsets.only(
                              top: MediaQuery.of(context).size.height * 0.09),
                          child: Container(
                            height: MediaQuery.of(context).size.height * 0.64,
                            width: MediaQuery.of(context).size.width,
                            alignment: Alignment.center,
                            child: makeChange
                                ? Container(
                                    width: MediaQuery.of(context).size.width,
                                    constraints: BoxConstraints(
                                      maxHeight:
                                          MediaQuery.of(context).size.height *
                                              0.6,
                                    ),
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width,
                                      child: Center(
                                        child: Container(
                                          margin: const EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 10),
                                          decoration: BoxDecoration(
                                            color: AppColors.white,
                                            borderRadius:
                                                BorderRadius.circular(25),
                                            boxShadow: const [
                                              BoxShadow(
                                                color: Colors.black26,
                                                //                                spreadRadius: 5,
                                                blurRadius: 4,
                                                offset: Offset(0,
                                                    3), // changes position of shadow
                                              ),
                                            ],
                                          ),
                                          child: Image.file(
                                            asset,
                                            fit: BoxFit.fitHeight,
                                            height: MediaQuery.of(context)
                                                    .size
                                                    .height *
                                                0.6,
                                          ),
                                        ),
                                      ),
                                    ))
                                : Container(
                                    height: MediaQuery.of(context).size.height *
                                        0.64,
                                    width: MediaQuery.of(context).size.width,
                                    alignment: Alignment.center,
                                    child: SizedBox(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.1,
                                      width:
                                          MediaQuery.of(context).size.height *
                                              0.1,
                                      child: const CircularProgressIndicator
                                          .adaptive(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                AppColors.primaryBlue),
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                      ]),
                    ),
                    SizedBox(height: Sizer.height(50)),
                    Visibility(
                      visible: true,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          InkWell(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              shareFile();
                            },
                            child: Column(
                              children: [
                                const CircleAvatar(
                                    radius: 30,
                                    backgroundColor: AppColors.blue100,
                                    child: Icon(
                                      Icons.image_outlined,
                                      size: 25,
                                      color: AppColors.primaryBlue,
                                    )),
                                SizedBox(height: Sizer.height(16)),
                                const Text("Share as image")
                              ],
                            ),
                          ),
                          InkWell(
                            splashColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              final pdfFile = await imageToPdfFile(asset.path);
                              sharePdf(context, file: pdfFile);
                            },
                            child: Column(
                              children: [
                                const CircleAvatar(
                                    radius: 30,
                                    backgroundColor: AppColors.blue100,
                                    child: Icon(
                                      Icons.file_copy_outlined,
                                      size: 25,
                                      color: AppColors.primaryBlue,
                                    )),
                                SizedBox(height: Sizer.height(16)),
                                const Text("Share as pdf")
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<File> imageToPdfFile(String imagePath) async {
    final outputDir = await getTemporaryDirectory();
    final file = File("${outputDir.path}/transaction_receipt.pdf");

    final pdf = pw.Document();

    final image = pw.MemoryImage(
      await File(imagePath).readAsBytes(),
    );

    pdf.addPage(
      pw.Page(
        build: (pw.Context context) => pw.Center(
          child: pw.Image(image),
        ),
      ),
    );

    return file.writeAsBytes(await pdf.save(), flush: true);
  }

  void shareFile() {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    Share.shareXFiles([XFile(asset.path)],
        text: "Korrency_${DateTime.now().toIso8601String()}",
        subject: "Korrency_Receipt_${DateTime.now().toIso8601String()}",
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }

  void sharePdf(BuildContext context, {required File file}) async {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    Share.shareXFiles([XFile(file.path)],
        text: "Korrency_${DateTime.now().toIso8601String()}",
        subject: "Korrency_Receipt_${DateTime.now().toIso8601String()}",
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size);
  }
}
