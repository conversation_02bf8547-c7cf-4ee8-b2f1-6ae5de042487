import 'package:korrency/core/core.dart';

class SearchableDropdownWidget<T> extends StatefulWidget {
  final String? labelText;
  final String? hintText;
  final List<SearchableDropdownItem<T>> items;
  final T? selectedValue;
  final Function(T?)? onChanged;
  final String? errorText;
  final bool showLabelHeader;
  final double borderRadius;
  final Color? fillColor;
  final Color? borderColor;
  final Widget? prefixIcon;
  final bool isOptional;
  final String? optionalText;
  final double? labelSize;
  final Color? labelColor;

  const SearchableDropdownWidget({
    super.key,
    this.labelText,
    this.hintText,
    required this.items,
    this.selectedValue,
    this.onChanged,
    this.errorText,
    this.showLabelHeader = false,
    this.borderRadius = 0,
    this.fillColor = Colors.transparent,
    this.borderColor,
    this.prefixIcon,
    this.isOptional = false,
    this.optionalText,
    this.labelSize,
    this.labelColor,
  });

  @override
  State<SearchableDropdownWidget<T>> createState() =>
      _SearchableDropdownWidgetState<T>();
}

class _SearchableDropdownWidgetState<T>
    extends State<SearchableDropdownWidget<T>> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  List<SearchableDropdownItem<T>> _filteredItems = [];
  bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _focusNode.addListener(_onFocusChanged);
    
    // Set initial text if there's a selected value
    if (widget.selectedValue != null) {
      final selectedItem = widget.items.firstWhere(
        (item) => item.value == widget.selectedValue,
        orElse: () => SearchableDropdownItem<T>(
          value: widget.selectedValue as T,
          label: '',
        ),
      );
      _searchController.text = selectedItem.label;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _showDropdown();
    } else {
      _hideDropdown();
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items
            .where((item) =>
                item.label.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
    _updateOverlay();
  }

  void _showDropdown() {
    if (_overlayEntry != null) return;
    
    setState(() {
      _isDropdownOpen = true;
    });

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideDropdown() {
    setState(() {
      _isDropdownOpen = false;
    });
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;
    Offset offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height + 4,
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 4.0),
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: Sizer.height(200),
              ),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: Border.all(
                  color: widget.borderColor ?? AppColors.gray500,
                  width: 1,
                ),
              ),
              child: _filteredItems.isEmpty
                  ? Padding(
                      padding: EdgeInsets.all(Sizer.radius(16)),
                      child: Text(
                        'No options found',
                        style: TextStyle(
                          color: AppColors.gray500,
                          fontSize: Sizer.text(14),
                        ),
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return InkWell(
                          onTap: () => _selectItem(item),
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(16),
                              vertical: Sizer.height(12),
                            ),
                            decoration: BoxDecoration(
                              border: index < _filteredItems.length - 1
                                  ? Border(
                                      bottom: BorderSide(
                                        color: AppColors.gray200,
                                        width: 0.5,
                                      ),
                                    )
                                  : null,
                            ),
                            child: Row(
                              children: [
                                if (item.icon != null) ...[
                                  item.icon!,
                                  XBox(12),
                                ],
                                Expanded(
                                  child: Text(
                                    item.label,
                                    style: TextStyle(
                                      color: AppColors.gray700,
                                      fontSize: Sizer.text(14),
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ),
        ),
      ),
    );
  }

  void _selectItem(SearchableDropdownItem<T> item) {
    setState(() {
      _searchController.text = item.label;
    });
    widget.onChanged?.call(item.value);
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showLabelHeader)
            Column(
              children: [
                Row(
                  children: [
                    Text(
                      widget.labelText!,
                      style: TextStyle(
                        color: widget.labelColor ?? AppColors.gray600,
                        fontSize: Sizer.text(widget.labelSize ?? 14),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Text(
                      widget.optionalText != null
                          ? widget.optionalText!
                          : widget.isOptional
                              ? " (Optional)"
                              : "",
                      style: TextStyle(
                        color: AppColors.gray600,
                        fontWeight: FontWeight.w500,
                        fontSize: widget.labelSize ?? 15.sp,
                      ),
                    ),
                  ],
                ),
                YBox(4),
              ],
            ),
          Container(
            height: Sizer.height(49),
            alignment: Alignment.center,
            child: TextField(
              controller: _searchController,
              focusNode: _focusNode,
              onChanged: _onSearchChanged,
              style: TextStyle(
                color: AppColors.gray700,
                fontSize: Sizer.text(16),
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                errorText: widget.errorText,
                errorStyle: TextStyle(
                  color: AppColors.red,
                  fontSize: 0.01.sp,
                  height: 0.2,
                ),
                contentPadding: EdgeInsets.only(
                  top: Sizer.height(20),
                  bottom: 0,
                  left: Sizer.width(16),
                  right: Sizer.width(10),
                ),
                hintText: widget.hintText,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(14),
                  fontWeight: FontWeight.w400,
                  color: AppColors.gray500,
                ),
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: Sizer.width(10)),
                  child: Icon(
                    _isDropdownOpen ? Iconsax.arrow_up_1 : Iconsax.arrow_down_1,
                    size: 18,
                    color: AppColors.black,
                  ),
                ),
                suffixIconConstraints: BoxConstraints(
                  minWidth: Sizer.width(30),
                  minHeight: Sizer.height(30),
                ),
                prefixIcon: widget.prefixIcon,
                prefixIconConstraints: BoxConstraints(
                  minWidth: Sizer.width(30),
                  minHeight: Sizer.height(30),
                ),
                fillColor: widget.fillColor,
                filled: true,
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    width: 1,
                    color: widget.borderColor ?? AppColors.gray500,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                disabledBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: AppColors.gray500,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                border: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: AppColors.gray500,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    width: 1,
                    color: AppColors.red.withOpacity(0.8),
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    width: 1,
                    color: AppColors.red.withOpacity(0.8),
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: const BorderSide(
                    width: 1,
                    color: AppColors.gray500,
                  ),
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            ),
          ),
          if (widget.errorText != null)
            Text(
              widget.errorText!,
              style: TextStyle(
                color: AppColors.red.withOpacity(0.8),
                fontSize: Sizer.text(12),
              ),
            ),
        ],
      ),
    );
  }
}

class SearchableDropdownItem<T> {
  final T value;
  final String label;
  final Widget? icon;

  const SearchableDropdownItem({
    required this.value,
    required this.label,
    this.icon,
  });
}