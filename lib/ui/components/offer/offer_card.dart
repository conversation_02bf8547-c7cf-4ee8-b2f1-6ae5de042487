import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';

class OfferCard extends StatelessWidget {
  const OfferCard({
    Key? key,
    required this.svgIcon,
    required this.title,
    required this.subTitleFirst,
    this.subTitleSecondBold,
    this.subTitleThird,
    this.subTitleFourthBold,
    this.subTitleFifth,
    this.withBg = false,
    this.onTap,
  }) : super(key: key);

  final String svgIcon;
  final String title;
  final String subTitleFirst;
  final String? subTitleSecondBold;
  final String? subTitleThird;
  final String? subTitleFourthBold;
  final String? subTitleFifth;
  final bool withBg;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(14),
          vertical: Sizer.height(20),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: AppColors.dottedColor,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(Sizer.width(12)),
              decoration: BoxDecoration(
                color: withBg ? AppColors.blue100 : AppColors.transparent,
                borderRadius: BorderRadius.circular(4),
              ),
              child: svgHelper(
                svgIcon,
              ),
            ),
            const XBox(14),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.blue600,
                    ),
                  ),
                  const YBox(4),
                  RichText(
                      text: TextSpan(
                    children: [
                      TextSpan(
                        text: subTitleFirst,
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGray,
                        ),
                      ),
                      TextSpan(
                        text: subTitleSecondBold,
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGray,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      TextSpan(
                        text: subTitleThird,
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGray,
                        ),
                      ),
                      TextSpan(
                        text: subTitleFourthBold,
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGray,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      TextSpan(
                        text: subTitleFifth,
                        style: AppTypography.text14.copyWith(
                          color: AppColors.textGray,
                        ),
                      ),
                    ],
                  )),
                ],
              ),
            ),
            const XBox(10),
            Icon(
              Iconsax.arrow_right_3,
              color: AppColors.iconBlack800,
              size: Sizer.radius(24),
            ),
          ],
        ),
      ),
    );
  }
}
