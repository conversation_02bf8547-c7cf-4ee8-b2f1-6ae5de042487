import 'dart:convert';

List<Referral> referralFromJson(String str) =>
    List<Referral>.from(json.decode(str).map((x) => Referral.fromJson(x)));

String referralToJson(List<Referral> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Referral {
  int? id;
  String? referredUserId;
  dynamic referredUserName;
  String? referredUserFullName;
  String? referredUserAvatarUrl;
  String? bonus;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;

  Referral({
    this.id,
    this.referredUserId,
    this.referredUserName,
    this.referredUserFullName,
    this.referredUserAvatarUrl,
    this.bonus,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory Referral.fromJson(Map<String, dynamic> json) => Referral(
        id: json["id"],
        referredUserId: json["referred_user_id"],
        referredUserName: json["referred_user_name"],
        referredUserFullName: json["referred_user_full_name"],
        referredUserAvatarUrl: json["referred_user_avatar_url"],
        bonus: json["bonus"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "referred_user_id": referredUserId,
        "referred_user_name": referredUserName,
        "referred_user_full_name": referredUserFullName,
        "referred_user_avatar_url": referredUserAvatarUrl,
        "bonus": bonus,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

ReferralWallet referralWalletFromJson(String str) =>
    ReferralWallet.fromJson(json.decode(str));

String referralWalletToJson(ReferralWallet data) => json.encode(data.toJson());

class ReferralWallet {
  int? balance;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;

  ReferralWallet({
    this.balance,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory ReferralWallet.fromJson(Map<String, dynamic> json) => ReferralWallet(
        balance: json["balance"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "balance": balance,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

Earnings earningsFromJson(String str) => Earnings.fromJson(json.decode(str));

String earningsToJson(Earnings data) => json.encode(data.toJson());

class Earnings {
  int? bonusesEarned;
  int? claimedBonuses;
  bool? showDisabledClaimBonusesButton;
  bool? showEnabledClaimBonusesButton;
  bool? showClaimedButton;

  Earnings({
    this.bonusesEarned,
    this.claimedBonuses,
    this.showDisabledClaimBonusesButton,
    this.showEnabledClaimBonusesButton,
    this.showClaimedButton,
  });

  factory Earnings.fromJson(Map<String, dynamic> json) => Earnings(
        bonusesEarned: json["bonuses_earned"],
        claimedBonuses: json["claimed_bonuses"],
        showDisabledClaimBonusesButton:
            json["show_disabled_claim_bonuses_button"],
        showEnabledClaimBonusesButton:
            json["show_enabled_claim_bonuses_button"],
        showClaimedButton: json["show_claimed_button"],
      );

  Map<String, dynamic> toJson() => {
        "bonuses_earned": bonusesEarned,
        "claimed_bonuses": claimedBonuses,
      };
}
