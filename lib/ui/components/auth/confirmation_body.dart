import 'package:korrency/core/core.dart';

class ConfirmationArg {
  ConfirmationArg({
    required this.title,
    required this.buttonText,
    this.imagePath,
    this.subtitle,
    this.imageHeight,
    // this.imageWidth,
    this.onBtnTap,
    this.outlineBtnText,
    this.outlineBtn,
  });

  final String title;
  final String buttonText;
  final String? imagePath;
  final Widget? subtitle;
  final double? imageHeight;
  // final double? imageWidth;
  final VoidCallback? onBtnTap;
  final String? outlineBtnText;
  final VoidCallback? outlineBtn;
}

class ConfirmationBody extends StatelessWidget {
  const ConfirmationBody({
    super.key,
    required this.arg,
  });

  final ConfirmationArg arg;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        imageHelper(
          arg.imagePath ?? AppImages.success,
          height: Sizer.height(arg.imageHeight ?? 160),
          // width: Sizer.width(arg.imageWidth ?? 168),
        ),
        const YBox(50),
        Text(
          arg.title,
          style: AppTypography.text20b,
        ),
        const YBox(4),
        if (arg.subtitle != null) arg.subtitle!,
      ],
    );
  }
}
