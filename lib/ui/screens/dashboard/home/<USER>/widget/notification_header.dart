import 'package:korrency/core/core.dart';

class NotificationHeader extends StatelessWidget {
  const NotificationHeader({
    super.key,
    this.length,
  });

  final String? length;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Notifications',
          style: AppTypography.text16.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.black900,
          ),
        ),
        if (length != null) const XBox(4),
        if (length != null)
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(8),
              vertical: Sizer.height(4),
            ),
            decoration: BoxDecoration(
              color: AppColors.blue900,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Text(
              length!,
              style: AppTypography.text12.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
          ),
      ],
    );
  }
}
