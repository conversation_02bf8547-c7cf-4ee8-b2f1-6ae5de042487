import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyReviewScreen extends StatefulWidget {
  const SendMoneyReviewScreen({super.key, required this.arg});

  final SendMoneyReviewsArg arg;

  @override
  State<SendMoneyReviewScreen> createState() => _SendMoneyReviewScreenState();
}

class _SendMoneyReviewScreenState extends State<SendMoneyReviewScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(
      builder: (context, vm, _) {
        printty('SendMoneyReviewScreen Vm fromC ${widget.arg.iconPath}');
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: "Review Details",
            ),
            body: ListView(
              padding: EdgeInsets.only(
                top: Sizer.height(24),
                left: Sizer.width(24),
                right: Sizer.width(24),
              ),
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                    vertical: Sizer.height(20),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.grayFE,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.grayAEC,
                    ),
                  ),
                  child: Column(
                    children: [
                      ReviewRowWidget(
                        iconPath: vm.fromConvertWallet?.currency?.flag ?? '',
                        code: vm.fromConvertWallet?.currency?.code ?? '',
                        title: 'You Send',
                        amount: vm.reviewAmtYouSend,
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: Sizer.height(12)),
                        child: HDivider(color: AppColors.grayAEC),
                      ),
                      ReviewRowWidget(
                        iconPath: vm.recipientCurrency?.flag ?? '',
                        code: vm.recipientCurrency?.code ?? '',
                        title: 'Recipient gets',
                        amount: AppUtils.formatAmountDoubleString(
                          vm.recipientC.text.trim().replaceAll(",", ""),
                        ),
                      ),
                    ],
                  ),
                ),
                const YBox(24),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.radius(12),
                    vertical: Sizer.height(16),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.grayAEC,
                    ),
                  ),
                  child: SendMoneyBenrficiaryListTile(
                    padding: EdgeInsets.zero,
                    name: widget.arg.name,
                    title: widget.arg.title,
                    subtitle: widget.arg.subTitle,
                    iconPath: widget.arg.iconPath,
                  ),
                ),
                const YBox(24),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.radius(12),
                    vertical: Sizer.height(16),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.grayAEC,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildColText(
                        title: 'Our Fee',
                        subTitle: vm.transferFee,
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: Sizer.height(16)),
                        child: _buildColText(
                          title: 'Exchange Rate',
                          subTitle: vm.rateFormat,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: Sizer.height(16)),
                        child: _buildColText(
                          title: 'Delivery Method',
                          subTitle: getTransferMethod(widget
                                  .arg.transferAgainParams?.transferMethod) ??
                              vm.transferMethod?.text ??
                              '',
                        ),
                      ),

                      Padding(
                        padding: EdgeInsets.only(top: Sizer.height(16)),
                        child: _buildColText(
                          title: 'Reason for sending money',
                          subTitle: context
                                  .watch<TransactionVM>()
                                  .selectedReason
                                  ?.name ??
                              '',
                        ),
                      ),
                      // CustomListViews.currencyListText(
                      //   margin: EdgeInsets.zero,
                      //   leftText: 'Total Amount',
                      //   rightText: vm.total,
                      //   rightTextColor: AppColors.iconGreen,
                      //   rightFontWeight: FontWeight.w600,
                      // ),
                    ],
                  ),
                ),
                const YBox(160),
              ],
            ),
            bottomSheet: Container(
              padding: EdgeInsets.only(
                left: Sizer.width(24),
                right: Sizer.width(24),
                top: Sizer.height(10),
                bottom: Sizer.height(30),
              ),
              color: AppColors.white,
              child: CustomBtn.withChild(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                child: ContinueText(isOnline: true),
                onTap: () async {
                  final res = await BsWrapper.showCustomDialog(
                    context,
                    child: TransactionPinDialog(
                      params: widget.arg.transferAgainParams,
                    ),
                  );
                  printty("res $res");

                  if (res is Map && res['success'] == true) {
                    BsWrapper.bottomSheet(
                        canDismiss: false,
                        context: mounted
                            ? context
                            : NavigatorKeys.appNavigatorKey.currentContext!,
                        widget: TransferSuccessModal(
                          interacSecAnswer: res['securityAnswer'],
                          recipientName: widget.arg.name,
                        ));
                  }

                  // Navigator.pushNamed(
                  //   context,
                  //   RoutePath.sendMoneyAuthorizeScreen,
                  // );
                },
                online: true,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildColText({
    required String title,
    required String subTitle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray79,
          ),
        ),
        Text(
          subTitle,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: AppTypography.text16.copyWith(
            color: AppColors.primaryBlue,
          ),
        )
      ],
    );
  }

  // _gotoTransactionInProcess() {
  //   Navigator.pushReplacementNamed(
  //     context,
  //     RoutePath.transactionStatusScreen,
  //     arguments: ConfirmationArg(
  //       title: "Transaction in Progress",
  //       imagePath: AppImages.transaction,
  //       imageHeight: 270,
  //       imageWidth: 270,
  //       subtitle: const ConfirmationSubtitleText(
  //         startText: "The recipient will",
  //         endText: " receive the money in a few minutes",
  //       ),
  //       buttonText: 'Send more money',
  //       onBtnTap: () {
  //         _pop();
  //         _pop();
  //         _pop();
  //         _pop();
  //         Navigator.pushNamed(
  //           NavigatorKeys.appNavigatorKey.currentContext!,
  //           RoutePath.sendMoneyScreen,
  //         );
  //       },
  //       outlineBtnText: "Home",
  //       outlineBtn: () {
  //         Navigator.pushNamedAndRemoveUntil(
  //           NavigatorKeys.appNavigatorKey.currentContext!,
  //           RoutePath.dashboardNav,
  //           (route) => false,
  //         );
  //       },
  //     ),
  //   );
  // }
}
