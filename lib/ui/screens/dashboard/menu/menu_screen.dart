import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:easy_localization/easy_localization.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;
  String _tr(String key, {List<String>? args}) {
    final v = key.tr(args: args);
    if (v == key) {
      printty('Missing translation: $key', level: 'i18n');
    }
    return v;
  }
  @override
  void initState() {
    super.initState();
    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
  }

  @override
  void dispose() {
    _customController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: Sizer.screenHeight,
        width: Sizer.screenWidth,
        color: AppColors.white,
        child: BusyOverlay(
          show: context.watch<LoginVM>().isBusy,
          child: Consumer2<AuthUserVM, LanguageVM>(builder: (context, vm, langVm, _) {
            return SafeArea(
              bottom: false,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      YBox(20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          UserAvatar(
                            avatarUrl: vm.user?.avatarUrl ?? "",
                            onTap: () {
                              printty(
                                  "UserAvatar tapped ${vm.user?.avatarUrl}");
                            },
                          ),
                          // InkWell(
                          //   onTap: () {
                          //     context.read<FreshChatVM>()
                          //       ..initializeFreshchat()
                          //       ..configureFreshchatUser(
                          //           context.read<AuthUserVM>().user);
                          //     Future.delayed(const Duration(seconds: 1), () {
                          //       Freshchat.showConversations();
                          //     });
                          //   },
                          //   child: Row(
                          //     children: [
                          //       SvgPicture.asset(
                          //         AppSvgs.headphone,
                          //         height: Sizer.height(20),
                          //         width: Sizer.width(20),
                          //       ),
                          //       const XBox(8),
                          //       Text(
                          //         "Support",
                          //         style: AppTypography.text14
                          //             .copyWith(color: AppColors.white),
                          //       )
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                      const YBox(6),
                      Text(
                        vm.user?.fullName ?? "",
                        style: AppTypography.text18.copyWith(
                          fontFamily: AppFont.outfit.family,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                      Text(
                        vm.user?.userName ?? "",
                        style: AppTypography.text16.copyWith(
                          fontFamily: AppFont.outfit.family,
                          color: AppColors.gray79,
                        ),
                      ),
                    ],
                  ),
                  YBox(10),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.inviteAndEarnScreen,
                        );
                      },
                      // child: imageHelper(AppImages.profileBadge),
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                          vertical: Sizer.height(16),
                        ),
                        height: Sizer.height(80),
                        width: Sizer.screenWidth,
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(AppImages.inviteBadge),
                            // fit: BoxFit.cover,
                          ),
                        ),
                        child: Text(
                          _tr('menu.invite_banner', args: [
                            vm.referralRule?.currency.symbol ?? '',
                            vm.referralRule?.referredBonus ?? '0'
                          ]),
                          style: AppTypography.text16.copyWith(
                            fontFamily: AppFont.inter.family,
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      width: Sizer.screenWidth,
                      color: AppColors.white,
                      child: FadeTransition(
                        opacity: _customAnimation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0, 0.2),
                            end: const Offset(0, 0),
                          ).animate(_customAnimation),
                          child: ListView(
                            padding: EdgeInsets.only(
                              top: Sizer.height(30),
                              bottom: Sizer.height(24),
                              right: Sizer.width(24),
                              left: Sizer.width(24),
                            ),
                            children: [
                              MenuListTile(
                                title: _tr('menu.account_settings.title'),
                                subTitle: _tr('menu.account_settings.subtitle'),
                                icon: AppSvgs.gearRepo,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.accountSettingScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.profile_settings.title'),
                                subTitle: _tr('menu.profile_settings.subtitle'),
                                icon: AppSvgs.userProfile,
                                onPressed: () {
                                  Navigator.pushNamed(
                                      context, RoutePath.profileScreen);
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.privacy_security.title'),
                                subTitle: _tr('menu.privacy_security.subtitle'),
                                icon: AppSvgs.shield,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.securityScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.preferences.title'),
                                subTitle: _tr('menu.preferences.subtitle'),
                                icon: AppSvgs.preference,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.preferenceScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.beneficiaries.title'),
                                subTitle: _tr('menu.beneficiaries.subtitle'),
                                icon: AppSvgs.beneficiary,
                                onPressed: () {
                                  if (!vm.userIsVerified) {
                                    showWarningToast(
                                        _tr('menu.beneficiaries.kyc_warning'));
                                    return;
                                  }
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.beneficiaryScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.about.title'),
                                subTitle: _tr('menu.about.subtitle'),
                                icon: AppSvgs.about,
                                onPressed: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.aboutUsScreen,
                                  );
                                },
                              ),
                              const YBox(50),
                              MenuListTile(
                                title: _tr('menu.help_support.title'),
                                subTitle: _tr('menu.help_support.subtitle'),
                                icon: AppSvgs.helpSupport,
                                onPressed: () {
                                  MixpanelService().track("Support Accessed");
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.helpSupportScreen,
                                  );
                                },
                              ),
                              const YBox(24),
                              MenuListTile(
                                title: _tr('menu.logout.title'),
                                icon: AppSvgs.logout,
                                onPressed: () {
                                  BsWrapper.bottomSheet(
                                    context: context,
                                    widget: ConfirmationSheet(
                                       title: vm.user?.firstName ??
                                           vm.user?.userName,
                                      message: _tr('menu.logout.confirm_message'),
                                      secondBtnText: _tr('menu.logout.confirm_cta'),
                                      secendBtnTap: () => _handleLogout(),
                                    ),
                                  );
                                },
                              ),
                              // MenuListTileOLD(
                              //   title: 'Referrals',
                              //   iconData: Iconsax.gift,
                              //   onPressed: () {
                              //     // FlushBarToast.fLSnackBar(
                              //     //   message: 'Refer a friend is coming soon',
                              //     //   snackBarType: SnackBarType.success,
                              //     // );
                              //     BsWrapper.bottomSheet(
                              //       context: context,
                              //       widget: const InviteOthersAndEarnSheet(),
                              //     );
                              //   },
                              // ),
                              // const YBox(20),
                              // MenuListTileOLD(
                              //   title: 'About Korrency',
                              //   useImageIcon: true,
                              //   onPressed: () {
                              //     Navigator.pushNamed(
                              //       context,
                              //       RoutePath.aboutUsScreen,
                              //     );
                              //   },
                              // ),

                              const YBox(30),
                              InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, RoutePath.updateAvailableScreen);
                                },
                                child: Align(
                                  alignment: Alignment.center,
                                  child: Text(
                                    _tr('menu.version', args: [
                                      context.read<ConfigVM>().myAppCurrentVersion ?? '1.0.0'
                                    ]),
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.grayE0,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                              const YBox(100),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }

  void _handleLogout() async {
    final count = int.tryParse(
            await StorageService.getString(StorageKey.logoutCount) ?? "0") ??
        0;
    StorageService.storeString(StorageKey.logoutCount, '${count + 1}');
    navigate();
  }

  void navigate() {
    Navigator.pop(context);
    context.read<LoginVM>().logout();
  }
}
