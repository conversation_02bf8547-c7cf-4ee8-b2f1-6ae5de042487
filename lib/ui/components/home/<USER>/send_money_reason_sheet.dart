import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyReasonSheet extends StatefulWidget {
  const SendMoneyReasonSheet({
    super.key,
    this.selectedReason,
  });

  final String? selectedReason;

  @override
  State<SendMoneyReasonSheet> createState() => _SendMoneyReasonSheetState();
}

class _SendMoneyReasonSheetState extends State<SendMoneyReasonSheet> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TransactionVM>().getTransactionPurposes();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Container(
        height: Sizer.screenHeight * 0.8,
        margin: EdgeInsets.all(Sizer.width(12)),
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(Sizer.height(12)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Select Reason for sending money",
                  style: AppTypography.text18.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            Expanded(
              child: Builder(builder: (context) {
                if (vm.isBusy) {
                  return SizedBox(
                    height: Sizer.height(400),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                if (vm.transactionReasons.isEmpty) {
                  return SizedBox(
                    height: Sizer.height(400),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Iconsax.box,
                            size: Sizer.radius(40),
                          ),
                          const YBox(10),
                          Text(
                            "No Reason Found",
                            style: AppTypography.text16.copyWith(
                              color: AppColors.textBlack600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }
                return ListView(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(
                    top: Sizer.height(10),
                    bottom: Sizer.height(50),
                  ),
                  children: [
                    const YBox(30),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (ctx, i) {
                        final reason = vm.transactionReasons[i];
                        return TextSelectorTile(
                          text: reason.name ?? "",
                          isSelected: widget.selectedReason == reason.name,
                          onTap: () {
                            vm.setSelectedReason(reason);
                            Navigator.pop(context, reason);
                          },
                        );
                      },
                      separatorBuilder: (ctx, _) => const YBox(8),
                      itemCount: vm.transactionReasons.length,
                    ),
                  ],
                );
              }),
            ),
          ],
        ),
      );
    });
  }
}
