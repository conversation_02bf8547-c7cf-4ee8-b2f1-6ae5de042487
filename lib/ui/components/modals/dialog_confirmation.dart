import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DialogConfirmation extends StatelessWidget {
  const DialogConfirmation({
    Key? key,
    required this.msg,
    this.leftBtnText,
    this.rightBtnText,
    this.onConfirm,
    this.onCancel,
  }) : super(key: key);

  final String msg;
  final String? leftBtnText;
  final String? rightBtnText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Text(
            msg,
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(30),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: CustomBtn.solid(
                  height: 36,
                  onTap: onCancel ??
                      () {
                        Navigator.pop(context);
                      },
                  text: leftBtnText ?? "Ok",
                ),
              ),
              const XBox(20),
              Expanded(
                child: CustomBtn.solid(
                  height: 36,
                  onlineColor: AppColors.red,
                  onTap: onConfirm,
                  text: rightBtnText ?? "Ok",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
