// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class AccountStatementScreen extends StatefulWidget {
  const AccountStatementScreen({super.key});

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen> {
  final _startDateC = TextEditingController();
  final _endDateC = TextEditingController();
  final _currencyC = TextEditingController();

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();
  Wallet? selectedWallet;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   selectedWallet = context.read<WalletVM>().activeWallet;
    //   _currencyC.text = selectedWallet?.currency?.name ?? "";
    //   setState(() {});
    // });
  }

  @override
  void dispose() {
    _startDateC.dispose();
    _endDateC.dispose();
    _currencyC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    selectedWallet = context.read<WalletVM>().activeWallet;
    _currencyC.text = selectedWallet?.currency?.name ?? "";
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Account Statement',
          ),
          body: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              Text(
                "Please fill the following with accurate \ninformation",
                style: AppTypography.text16.copyWith(color: AppColors.gray93),
              ),
              const YBox(30),
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      isReadOnly: true,
                      height: Sizer.height(50),
                      labelText: "Start Date",
                      hintText: '17 Feb 2024',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(12),
                      prefixIcon: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(10),
                        ),
                        child: Icon(
                          Iconsax.calendar_1,
                          color: AppColors.mainBlack,
                          size: Sizer.height(20),
                        ),
                      ),
                      controller: _startDateC,
                      onTap: () {
                        showCupertinoDatePicker(
                          context,
                          onDateTimeChanged: (val) {
                            // Check if val is greater now
                            if (val.isAfter(DateTime.now())) {
                              startDate = DateTime.now();
                              return;
                            }

                            startDate = val;
                          },
                          onDone: () {
                            _startDateC.text =
                                AppUtils.dayWithSuffixMonthAndYear(startDate);
                            vm.reBuildUI();
                            Navigator.pop(context);
                          },
                        );
                      },
                      onChanged: (val) {},
                    ),
                  ),
                  const XBox(20),
                  Expanded(
                    child: CustomTextField(
                      isReadOnly: true,
                      height: Sizer.height(50),
                      labelText: "End Date",
                      hintText: '17 Feb 2024',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(12),
                      prefixIcon: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(10),
                        ),
                        child: Icon(
                          Iconsax.calendar_1,
                          color: AppColors.mainBlack,
                          size: Sizer.height(20),
                        ),
                      ),
                      controller: _endDateC,
                      onTap: () {
                        showCupertinoDatePicker(
                          context,
                          minimumDate: startDate,
                          onDateTimeChanged: (val) {
                            if (val.isAfter(DateTime.now())) {
                              endDate = DateTime.now();
                              return;
                            }
                            endDate = val;
                          },
                          onDone: () {
                            _endDateC.text =
                                AppUtils.dayWithSuffixMonthAndYear(endDate);
                            vm.reBuildUI();
                            Navigator.pop(context);
                          },
                        );
                      },
                      onChanged: (val) {},
                    ),
                  ),
                ],
              ),
              const YBox(20),
              CustomTextField(
                labelText: "Select Wallet",
                showLabelHeader: true,
                isReadOnly: true,
                controller: _currencyC,
                borderRadius: Sizer.height(12),
                hintText: 'Select Currency',
                showSuffixIcon: true,
                prefixIcon: Container(
                    padding: EdgeInsets.all(Sizer.radius(10)),
                    child: SvgPicture.network(
                      selectedWallet?.currency?.flag ?? '',
                      width: Sizer.width(20),
                      height: Sizer.height(14),
                    )),
                suffixIcon: Icon(
                  Icons.expand_more,
                  color: AppColors.gray500,
                  size: Sizer.height(26),
                ),
                onTap: () async {
                  final res = await BsWrapper.bottomSheet(
                    context: context,
                    widget: SelectWalletModal(),
                  );

                  if (res is Wallet) {
                    selectedWallet = res;
                    _currencyC.text = res.currency?.name ?? '';
                    vm.reBuildUI();
                  }
                },
              ),
              const YBox(20),
              CustomTextField(
                labelText: "Email",
                isReadOnly: true,
                fillColor: AppColors.litGrey100,
                showLabelHeader: true,
                hideBorder: true,
                borderRadius: Sizer.height(12),
                hintText: vm.user?.email,
                onChanged: (val) {},
              ),
            ],
          ),
          bottomSheet: Container(
            color: AppColors.bgWhite,
            padding: EdgeInsets.only(
              left: Sizer.width(24),
              right: Sizer.width(24),
              bottom: Sizer.height(40),
            ),
            child: CustomBtn.solid(
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              onTap: () {
                _downloadAccountStatement(vm);
              },
              online: selectedWallet != null &&
                  _startDateC.text.trim().isNotEmpty &&
                  _endDateC.text.trim().isNotEmpty &&
                  _currencyC.text.trim().isNotEmpty,
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }

  _downloadAccountStatement(AuthUserVM vm) {
    vm
        .downloadAccountStatement(
      startDate: startDate.toIso8601String().split("T").first,
      endDate: endDate.toIso8601String().split("T").first,
      currencyId: selectedWallet?.currency?.id ?? 0,
    )
        .then((value) {
      if (value.success) {
        BsWrapper.bottomSheet(
          context: context,
          widget: ConfirmationModal(
            args: ConfirmationModalArgs(
              title: "Your statement is on the way",
              description:
                  'Your account statement has been sent to \nyour email',
              btnText: 'Done',
              btnTap: () {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  RoutePath.dashboardNav,
                  (route) => false,
                );
              },
            ),
          ),
        );
      } else {
        _showErrorConfirmationScreen(msg: value.message);
      }
    });
  }

  _showErrorConfirmationScreen({String? msg}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () {
          _pop();
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
