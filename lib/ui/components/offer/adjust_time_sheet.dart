import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:numberpicker/numberpicker.dart';

class AdjustTimeSheet extends StatefulWidget {
  const AdjustTimeSheet({Key? key}) : super(key: key);

  @override
  State<AdjustTimeSheet> createState() => _AdjustTimeSheetState();
}

class _AdjustTimeSheetState extends State<AdjustTimeSheet> {
  int _currentHour = 1;
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.60,
      child: Consumer<CreateOfferVM>(builder: (context, vm, _) {
        printty(vm.createdOffer, level: "Created Offer");
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            const AuthTextSubTitle(
              title: "Adjust Time",
              subtitle:
                  "You can edit how long your offer stays on the marketplace",
            ),
            // const YBox(40),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: NumberPicker(
                      value: _currentHour,
                      minValue: 1,
                      maxValue: 24,
                      zeroPad: true,
                      itemWidth: Sizer.width(100),
                      itemHeight: Sizer.height(60),
                      infiniteLoop: true,
                      selectedTextStyle: _selectedTextStyle(),
                      textStyle: _unSelectedTextStyle(),
                      decoration: _boxDecoration(),
                      onChanged: (value) =>
                          setState(() => _currentHour = value),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.height(20),
                    ),
                    height: Sizer.height(60),
                    decoration: _boxDecoration(),
                    child: Center(
                      child: Text(
                        ":",
                        style: AppTypography.text24.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                      child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.height(20),
                    ),
                    height: Sizer.height(60),
                    decoration: _boxDecoration(),
                    child: Center(
                      child: Text(
                        "00",
                        style: AppTypography.text24.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                  )),
                ],
              ),
            ),
            const YBox(40),
            CustomBtn.solid(
              onTap: () {
                printty(_currentHour, level: "Current Hour");
                vm
                    .adjustTimeOffer(
                  time: _currentHour,
                  id: vm.createdOffer?.id ?? 0,
                )
                    .then((value) {
                  if (value.success) {
                    Navigator.pop(context);
                    FlushBarToast.fLSnackBar(
                      message: value.message.toString(),
                      snackBarType: SnackBarType.success,
                    );
                  } else {
                    FlushBarToast.fLSnackBar(
                      message: value.message.toString(),
                    );
                  }
                });
              },
              isLoading: vm.isBusy,
              online: true,
              text: "Save and Continue",
            ),
            const YBox(60),
          ],
        );
      }),
    );
  }

  TextStyle _unSelectedTextStyle() {
    return TextStyle(
      color: AppColors.dottedColor,
      fontSize: Sizer.text(16),
      fontWeight: FontWeight.w700,
    );
  }

  TextStyle _selectedTextStyle() {
    return TextStyle(
      color: AppColors.primaryBlue,
      fontSize: Sizer.text(24),
      fontWeight: FontWeight.w700,
    );
  }

  BoxDecoration _boxDecoration() {
    return const BoxDecoration(
      border: Border(
        top: BorderSide(
          color: AppColors.dottedColor,
          width: 1,
        ),
        bottom: BorderSide(
          color: AppColors.dottedColor,
          width: 1,
        ),
      ),
    );
  }
}
