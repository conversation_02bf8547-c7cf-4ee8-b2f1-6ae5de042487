import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AllWalletsSheet extends StatefulWidget {
  const AllWalletsSheet({super.key});

  @override
  State<AllWalletsSheet> createState() => _AllWalletsSheetState();
}

class _AllWalletsSheetState extends State<AllWalletsSheet> {
  @override
  void initState() {
    super.initState();
    MixpanelService().trackScreen("Wallet Page Viewed");
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(
      builder: (context, vm, _) {
        return ContainerWithTopBorderRadius(
          padding: EdgeInsets.zero,
          height: Sizer.screenHeight * 0.60,
          child: ListView(
            shrinkWrap: true,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              if (vm.walletList.isNotEmpty)
                Text(
                  "All Wallets",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              if (vm.walletList.isNotEmpty) const YBox(24),
              if (vm.walletList.isNotEmpty)
                ListView.separated(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (ctx, i) {
                      Wallet wallet = vm.walletList[i];
                      return ContainerWithBluewishBg(
                        child: WalletListTile(
                          onTap: () {
                            vm.setActiveWallet(wallet);
                            Navigator.pop(context);
                          },
                          title: wallet.currency?.name ?? "",
                          subTitle:
                              "${AppUtils.formatAmountDoubleString(wallet.balance ?? "")} ${wallet.currency?.code}",
                          isSelected: vm.activeWallet == wallet,
                          useNetworkSvg: true,
                          currencyIcon: wallet.currency?.flag ?? "",
                          icon: Icons.check_circle,
                        ),
                      );
                    },
                    separatorBuilder: (ctx, _) => const YBox(24),
                    itemCount: vm.walletList.length),
              if (vm.walletList.isNotEmpty) const YBox(30),
              if (_getAvailableCurrencies().isNotEmpty)
                Text(
                  "Available Currencies",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              // _getAvailableCurrencies().isEmpty
              // ? SizedBox(
              //     height: Sizer.height(200),
              //     child: Center(
              //       child: Text(
              //         "No currency available",
              //         style: AppTypography.text16.copyWith(
              //             fontWeight: FontWeight.w600,
              //             color: AppColors.gray500),
              //       ),
              //     ),
              //   )
              // :
              if (_getAvailableCurrencies().isNotEmpty)
                ListView.separated(
                  padding: EdgeInsets.symmetric(
                    vertical: Sizer.width(24),
                  ),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, i) {
                    var currency = _getAvailableCurrencies()[i];
                    return DottedWalletListTile(
                      title: currency.name ?? '',
                      subTitle: currency.code ?? '',
                      currencyIcon: currency.flag ?? '',
                      onTap: () {
                        if (!context.read<AuthUserVM>().userIsVerified) {
                          showWarningToast(
                              'Complete your KYC to add beneficiaries');
                          return;
                        }

                        Navigator.pop(context);
                        BsWrapper.bottomSheet(
                          context: context,
                          widget:
                              VirtualAccountConfirmation(currency: currency),
                        );
                        return;
                      },
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(24),
                  itemCount: _getAvailableCurrencies().length,
                ),
            ],
          ),
        );
      },
    );
  }

  List<Currency> _getAvailableCurrencies() {
    List<Currency> availableCurrency = [];
    List<Wallet> walletList = context.read<WalletVM>().walletList;
    for (var currency in context.read<CurrencyVM>().creatableCurrencies) {
      // compare currency code with wallet currency code and return list
      if (walletList
          .where((element) => element.currency?.code == currency.code)
          .isEmpty) {
        availableCurrency.add(currency);
      }
    }

    printty(availableCurrency.length.toString());

    return availableCurrency;
  }
}
