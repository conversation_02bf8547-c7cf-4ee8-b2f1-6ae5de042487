// ignore_for_file: deprecated_member_use

import 'package:korrency/core/core.dart';

class BottomNavColumn extends StatelessWidget {
  const BottomNavColumn({
    super.key,
    required this.icon,
    required this.labelText,
    this.isSelected = false,
    required this.onPressed,
  });

  final String icon;
  final String labelText;

  final bool isSelected;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: onPressed,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            icon,
            height: Sizer.height(24),
            width: Sizer.width(24),
          ),
          SizedBox(height: Sizer.height(6)),
          Text(
            labelText,
            style: AppTypography.text10.copyWith(
              color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
            ),
          ),
        ],
      ),
    );
  }
}
