import 'package:korrency/core/core.dart';

class TextSelectorTile extends StatelessWidget {
  const TextSelectorTile({
    super.key,
    required this.text,
    this.isSelected = false,
    this.showCheckBox = false,
    this.onTap,
  });

  final String text;
  final bool isSelected;
  final bool showCheckBox;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(12),
          vertical: Sizer.height(16),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : AppColors.transparent,
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                text,
                style: AppTypography.text16.copyWith(
                  color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                ),
              ),
            ),
            if (isSelected && showCheckBox)
              SvgPicture.asset(
                AppSvgs.checkBox,
                height: Sizer.height(14),
              ),
          ],
        ),
      ),
    );
  }
}
