import 'package:korrency/core/core.dart';

class BuildRateCard extends StatelessWidget {
  const BuildRateCard({
    super.key,
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(12),
        vertical: Sizer.height(6),
      ),
      decoration: BoxDecoration(
        color: AppColors.grayF5,
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
        border: Border.all(
          color: AppColors.grayEFA,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            text,
            style: AppTypography.text12.copyWith(
              color: AppColors.mainBlack,
              fontWeight: FontWeight.w300,
            ),
          ),
          // if (vm.setRateBannerToGreen) const XBox(10),
          // if (vm.setRateBannerToGreen)
          //   Icon(
          //     Iconsax.information5,
          //     size: Sizer.radius(16),
          //     color: AppColors.white,
          //   ),
        ],
      ),
    );
  }
}
