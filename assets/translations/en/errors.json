{"errors": {"timeout": "Connection time out. Check your internet connection.\nContact support if error persists", "general": "An error occurred. Please try again.\nContact support if error persists", "network": "Network error occurred. Please check your connection", "server": "Server error. Please try again later", "validation_failed": "Validation failed", "invalid_email": "<PERSON><PERSON><PERSON>", "invalid_phone": "Invalid phone number", "required_field": "This field is required", "password_mismatch": "Passwords do not match", "weak_password": "Password is too weak", "auth": {"login_failed": "<PERSON><PERSON> failed. Please check your credentials", "session_expired": "Your session has expired. Please log in again", "account_locked": "Account temporarily locked. Please try again later", "invalid_credentials": "Invalid email or password"}, "kyc": {"incomplete": "Complete your KYC to {action}", "pending": "Your KYC is pending review", "failed": "KYC verification failed", "document_required": "Document verification required"}, "transaction": {"insufficient_funds": "Insufficient funds in your wallet", "limit_exceeded": "Transaction limit exceeded", "failed": "Transaction failed. Please try again", "not_found": "Transaction not found", "cancelled": "Transaction was cancelled"}, "wallet": {"not_found": "Wallet not found", "insufficient_balance": "Insufficient wallet balance", "currency_not_supported": "<PERSON><PERSON><PERSON><PERSON> not supported"}, "beneficiary": {"not_found": "Beneficiary not found", "already_exists": "Beneficiary already exists", "limit_reached": "Maximum number of beneficiaries reached"}, "verification": {"max_attempts": "Too many incorrect attempts", "code_expired": "Verification code has expired", "invalid_code": "Invalid verification code", "failed": "Verification failed"}, "file": {"upload_failed": "File upload failed", "invalid_format": "Invalid file format", "too_large": "File size too large", "corrupted": "File appears to be corrupted"}, "rate_limit": "Too many requests. Please wait and try again", "maintenance": "Service temporarily unavailable due to maintenance", "feature_unavailable": "This feature is currently unavailable"}}