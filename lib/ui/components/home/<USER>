import 'package:korrency/core/core.dart';

class HomeExchangeRateWidget extends StatelessWidget {
  const HomeExchangeRateWidget({
    super.key,
    required this.firstFlag,
    required this.secondFlag,
    required this.exchangeRate,
    this.borderColor,
  });

  final String firstFlag;
  final String secondFlag;
  final String exchangeRate;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      padding: const EdgeInsets.all(2), // Padding for gradient border
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFFFFC636), // #FFC636
            Color(0xFF004AFF), // #004AFF
            Color(0xFFBACDF5), // #BACDF5
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(Sizer.radius(8)),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(20),
          vertical: Sizer.height(12),
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.yellow3EC,
              AppColors.white,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(Sizer.radius(6)), // Slightly smaller radius for inner container
         ),
         child: Row(
           children: [
             Expanded(
               child: Column(
                 crossAxisAlignment: CrossAxisAlignment.start,
                 children: [
                   Text(
                     "Exchange rate",
                     style: AppTypography.text12.copyWith(
                       color: AppColors.gray93,
                     ),
                   ),
                   YBox(2),
                   Text(
                     exchangeRate,
                     style: AppTypography.text16.semiBold.copyWith(
                       color: AppColors.gray93,
                     ),
                   ),
                 ],
               ),
             ),
             Padding(
               padding: EdgeInsets.only(
                 right: Sizer.width(16),
               ),
               child: StackedCurrencyFlag(
                 firstFlag: firstFlag,
                 secondFlag: secondFlag,
               ),
             )
           ],
         ),
       ),
     );
   }
}

class StackedCurrencyFlag extends StatelessWidget {
  const StackedCurrencyFlag({
    super.key,
    this.firstFlag,
    this.secondFlag,
    this.width,
    this.height,
  });

  final String? firstFlag;
  final String? secondFlag;
  final double? width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.white,
              width: 3,
            ),
            borderRadius: BorderRadius.circular(Sizer.radius(30)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(Sizer.radius(30)),
            child: SizedBox(
              width: Sizer.width(width ?? 32),
              height: Sizer.height(height ?? 32),
              child: firstFlag != null
                  ? SvgPicture.network(
                      firstFlag ?? "",
                      fit: BoxFit.cover,
                    )
                  : SvgPicture.asset(
                      AppSvgs.nigeria,
                    ),
            ),
          ),
        ),
        Positioned(
          right: -18,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: AppColors.white,
                width: 3,
              ),
              borderRadius: BorderRadius.circular(Sizer.radius(30)),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(Sizer.radius(30)),
              child: SizedBox(
                width: Sizer.width(width ?? 32),
                height: Sizer.height(height ?? 32),
                child: secondFlag != null
                    ? SvgPicture.network(
                        secondFlag ?? "",
                        fit: BoxFit.cover,
                      )
                    : SvgPicture.asset(
                        AppSvgs.canada,
                      ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
