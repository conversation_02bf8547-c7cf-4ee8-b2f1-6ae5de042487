import 'package:korrency/core/core.dart';

class CurrencyItemSelector extends StatelessWidget {
  const CurrencyItemSelector({
    super.key,
    this.isSelected = false,
    required this.country,
    required this.onTap,
  });

  final bool isSelected;
  final CountryModel country;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(20),
          horizontal: Sizer.width(12),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : AppColors.transparent,
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(Sizer.radius(30)),
              child: SizedBox(
                width: Sizer.width(28),
                height: Sizer.width(28),
                child: SvgPicture.network(
                  country.flag ?? '',
                  fit: BoxFit.cover,
                ),
              ),
            ),
            XBox(12),
            Expanded(
              child: Text(
                country.name ?? '',
                style: AppTypography.text16.copyWith(
                  color: isSelected ? AppColors.mainBlack : AppColors.gray79,
                ),
              ),
            ),
            Text(
              country.dialCode ?? '',
              style: AppTypography.text16.medium.copyWith(
                fontFamily: AppFont.inter.family,
                color: AppColors.gray51,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
