import 'dart:convert';

List<SendMoneyPurpose> sendMoneyReasonFromJson(String str) =>
    List<SendMoneyPurpose>.from(
        json.decode(str).map((x) => SendMoneyPurpose.fromJson(x)));

String sendMoneyReasonToJson(List<SendMoneyPurpose> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SendMoneyPurpose {
  final int? id;
  final String? name;
  final DateTime? createdAt;

  SendMoneyPurpose({
    this.id,
    this.name,
    this.createdAt,
  });

  factory SendMoneyPurpose.fromJson(Map<String, dynamic> json) =>
      SendMoneyPurpose(
        id: json["id"],
        name: json["name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "created_at": createdAt?.toIso8601String(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SendMoneyPurpose &&
        other.id == id &&
        other.name == name &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ createdAt.hashCode;
}
