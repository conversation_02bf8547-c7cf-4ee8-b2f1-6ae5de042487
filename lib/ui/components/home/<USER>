import 'package:korrency/core/core.dart';

class ViewAllRowWidget extends StatelessWidget {
  const ViewAllRowWidget({
    super.key,
    required this.leftTitle,
    this.onViewAll,
  });

  final String leftTitle;
  final VoidCallback? onViewAll;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            leftTitle,
            style: AppTypography.text16.semiBold,
          ),
        ),
        if (onViewAll != null)
          InkWell(
            onTap: onViewAll,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "View all",
                  style: AppTypography.text14.medium.copyWith(
                    color: AppColors.grayE91,
                  ),
                ),
                const XBox(8),
                SvgPicture.asset(
                  AppSvgs.arrowRight,
                  colorFilter: ColorFilter.mode(
                    AppColors.grayE91,
                    BlendMode.srcIn,
                  ),
                )
              ],
            ),
          ),
      ],
    );
  }
}
