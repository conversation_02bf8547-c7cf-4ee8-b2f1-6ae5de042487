import 'package:korrency/core/core.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewArg {
  final String? appBarText;
  final String webURL;

  WebViewArg({this.appBarText, required this.webURL});
}

class CreateFreshdeskTicketWebview extends StatefulWidget {
  const CreateFreshdeskTicketWebview({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final WebViewArg arg;

  @override
  State<CreateFreshdeskTicketWebview> createState() =>
      _CreateFreshdeskTicketWebviewState();
}

class _CreateFreshdeskTicketWebviewState
    extends State<CreateFreshdeskTicketWebview> {
  bool isLoading = true;

  late WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _initializeControllerFuture();
  }

  Future<void> _initializeControllerFuture() async {
    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(
            const PlatformWebViewControllerCreationParams());

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (_) async {
          isLoading = true;
        },
        onPageFinished: (finish) {
          setState(() {
            isLoading = false;
          });
        },
      ))
      ..loadRequest(Uri.parse(widget.arg.webURL));

    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          // title: Text(widget.arg.appBarText),
          // centerTitle: true,
          ),
      body: Stack(
        children: [
          WebViewWidget(
            controller: _controller,
          ),
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : const Stack(),
        ],
      ),
    );
  }
}
