import 'package:korrency/core/core.dart';

class DashboardVM extends BaseVM {
  DashboardVM() {
    getHasSeenP2pWelcomeScreenFromStorage();
    getHasSeenOfferHowitWorksScreenFromStorage();
  }

  int _currentIndex = 0;
  int get currentIndex => _currentIndex;
  bool _hasSeenP2pWelcomeScreen = false;
  bool get hasSeenP2pWelcomeScreen => _hasSeenP2pWelcomeScreen;
  bool _hasSeenOfferHowitWorksScreen = false;
  bool get hasSeenOfferHowitWorksScreen => _hasSeenOfferHowitWorksScreen;
  bool _doNotShowP2pWelcomeScreen = false;
  bool get doNotShowP2pWelcomeScreen => _doNotShowP2pWelcomeScreen;
  bool _dotNotShowOfferHowitWorksScreen = false;
  bool get dotNotShowOfferHowitWorksScreen => _dotNotShowOfferHowitWorksScreen;

  setHasSeenP2pWelcomeScreen(bool value) {
    _hasSeenP2pWelcomeScreen = value;

    if (_doNotShowP2pWelcomeScreen && value) {
      StorageService.storeBoolItem(StorageKey.hasSeenP2pWelcomeScreen, value);
    }
    printty(_hasSeenP2pWelcomeScreen, level: "P2P Screen");

    reBuildUI();
  }

  setHasSeenOfferHowitWorksScreen(bool value) {
    _hasSeenOfferHowitWorksScreen = value;

    if (_dotNotShowOfferHowitWorksScreen && value) {
      StorageService.storeBoolItem(
          StorageKey.hasSeenOfferHowitWorksScreen, value);
    }
    printty(_hasSeenOfferHowitWorksScreen, level: "Offer Screen");

    reBuildUI();
  }

  Future<void> getHasSeenP2pWelcomeScreenFromStorage() async {
    _hasSeenP2pWelcomeScreen =
        await StorageService.getBoolItem(StorageKey.hasSeenP2pWelcomeScreen) ??
            false;
    reBuildUI();
  }

  Future<void> getHasSeenOfferHowitWorksScreenFromStorage() async {
    _hasSeenOfferHowitWorksScreen = await StorageService.getBoolItem(
            StorageKey.hasSeenOfferHowitWorksScreen) ??
        false;
    reBuildUI();
  }

  toggleP2pWelcomeScreen(bool value) {
    _doNotShowP2pWelcomeScreen = value;

    reBuildUI();
  }

  toggleOfferHowitWorksScreen(bool value) {
    _dotNotShowOfferHowitWorksScreen = value;

    reBuildUI();
  }

  void changeScreenIndex(int index) {
    _currentIndex = index;
    reBuildUI();
  }

  resetData() {
    printty("Resetting Dashboard VM data ");
    _currentIndex = 0;
    reBuildUI();
  }
}
