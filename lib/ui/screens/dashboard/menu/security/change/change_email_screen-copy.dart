// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

/// ChangeEmailScreen implements a comprehensive security validation system
/// that requires users to correctly answer two security questions before
/// allowing them to proceed to change their email address.
///
/// Security Flow:
/// 1. Load user's security questions (minimum 2 required)
/// 2. Present questions one by one in a PageView
/// 3. Validate each answer against the backend
/// 4. Only proceed to newEmailScreen when both questions are answered correctly
/// 5. Redirect to error screen if validation fails
class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  State<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  // Core data structures for security questions
  List<SecurityQuestion> secQuestions = [];
  late PageController _pageController;
  int _currentPageIndex = 0;

  // Answer management
  List<TextEditingController> _answerControllers = [];
  List<bool> _answersValid = [];

  // Security validation tracking
  /// Tracks which questions have been answered correctly
  /// Key: question index, Value: whether answer was correct
  final Map<int, bool> _correctAnswers = {};

  /// Tracks validation attempts to prevent spam
  final Map<int, int> _validationAttempts = {};

  /// Maximum allowed validation attempts per question
  static const int _maxAttemptsPerQuestion = 3;

  /// Minimum number of questions that must be answered correctly
  static const int _requiredCorrectAnswers = 2;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    // Initialize security validation tracking
    _initializeSecurityTracking();
    // Load security questions after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSecurityQuestions();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    // Dispose all answer controllers to prevent memory leaks
    for (var controller in _answerControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  /// Initializes the security validation tracking system
  /// This sets up the data structures needed to track answer validation
  void _initializeSecurityTracking() {
    _correctAnswers.clear();
    _validationAttempts.clear();
  }

  /// Loads security questions from the backend
  /// Ensures we have at least 2 questions for the validation process
  Future<void> _loadSecurityQuestions() async {
    try {
      // Request at least 2 questions for the two-question validation system
      final response =
          await context.read<SecQuestVM>().getUserSecQuestion(count: 2);

      if (response.success && response.data != null) {
        secQuestions = response.data as List<SecurityQuestion>;

        // Ensure we have at least 2 questions for security validation
        if (secQuestions.length < _requiredCorrectAnswers) {
          _showInsufficientQuestionsError();
          return;
        }

        _initializeAnswerControllers();
        setState(() {});
      } else {
        _showLoadingError();
      }
    } catch (e) {
      printty("Error loading security questions: $e");
      _showLoadingError();
    }
  }

  /// Initializes text controllers and validation tracking for each security question
  /// Sets up listeners for real-time input validation
  void _initializeAnswerControllers() {
    // Dispose existing controllers if any
    for (var controller in _answerControllers) {
      controller.dispose();
    }

    // Create new controllers for each question
    _answerControllers = List.generate(
      secQuestions.length,
      (index) => TextEditingController(),
    );

    // Initialize validation state for each answer
    _answersValid = List.generate(secQuestions.length, (index) => false);

    // Initialize tracking for each question
    for (int i = 0; i < secQuestions.length; i++) {
      _correctAnswers[i] = false;
      _validationAttempts[i] = 0;
      // Add listener for real-time input validation
      _answerControllers[i].addListener(() => _validateAnswerInput(i));
    }
  }

  /// Validates user input for a specific question (client-side validation)
  /// This only checks if the input is not empty, not the correctness of the answer
  void _validateAnswerInput(int index) {
    final isValid = _answerControllers[index].text.trim().isNotEmpty;
    if (_answersValid[index] != isValid) {
      _answersValid[index] = isValid;
      setState(() {});
    }
  }

  /// Core security validation method that handles the two-question verification process
  ///
  /// Validation Logic:
  /// 1. Validates the current question answer against the backend
  /// 2. Tracks correct answers and validation attempts
  /// 3. Prevents excessive validation attempts per question
  /// 4. Only proceeds to newEmailScreen when both questions are answered correctly
  /// 5. Shows appropriate error messages for incorrect answers
  ///
  /// Navigation Flow:
  /// - If current answer is correct and we have 2+ correct answers total: Navigate to newEmailScreen
  /// - If current answer is correct but need more answers: Move to next question
  /// - If current answer is incorrect: Show error feedback and allow retry (up to max attempts)
  /// - If max attempts exceeded: Redirect to error confirmation screen
  Future<void> _validateSecurityAnswer(int questionIndex) async {
    // Check if maximum attempts exceeded for this question
    if (_validationAttempts[questionIndex]! >= _maxAttemptsPerQuestion) {
      _showMaxAttemptsExceededError();
      return;
    }

    try {
      final secVm = context.read<SecQuestVM>();
      final currentQuestion = secQuestions[questionIndex];
      final userAnswer = _answerControllers[questionIndex].text.trim();

      // Increment attempt counter
      _validationAttempts[questionIndex] =
          (_validationAttempts[questionIndex] ?? 0) + 1;

      printty(
          "Validating question ${questionIndex + 1}, attempt ${_validationAttempts[questionIndex]}");

      // Validate answer with backend
      final response = await secVm.validateSecQuestions(
        securityQuestionId: currentQuestion.id ?? 0,
        answer: userAnswer,
      );

      if (response.success) {
        // Check if the answer was correct
        final correctAnswersCount =
            response.data?["data"]?["correct_answers"] ?? 0;
        final isCurrentAnswerCorrect = correctAnswersCount > 0;

        if (isCurrentAnswerCorrect) {
          // Mark this question as correctly answered
          _correctAnswers[questionIndex] = true;

          // Check if we have enough correct answers to proceed
          final totalCorrectAnswers =
              _correctAnswers.values.where((correct) => correct).length;

          printty(
              "Question ${questionIndex + 1} answered correctly. Total correct: $totalCorrectAnswers");

          if (totalCorrectAnswers >= _requiredCorrectAnswers) {
            // Both questions answered correctly - proceed to new email screen
            _navigateToNewEmailScreen();
          } else {
            // Need more correct answers - move to next question
            _proceedToNextQuestion();
          }
        } else {
          // Answer was incorrect
          _handleIncorrectAnswer(questionIndex);
        }
      } else {
        // API call failed
        _showValidationError(
            response.message ?? "Validation failed. Please try again.");
      }
    } catch (e) {
      printty("Error during security validation: $e");
      _showValidationError(
          "An error occurred during validation. Please try again.");
    }
  }

  /// Navigation and Flow Control Methods
  /// These methods handle the progression through security questions and final navigation

  /// Navigates to the new email screen after successful security validation
  /// This is only called when both security questions have been answered correctly
  void _navigateToNewEmailScreen() {
    printty("Security validation successful - navigating to new email screen");
    Navigator.pushNamed(context, RoutePath.newEmailScreen);
  }

  /// Proceeds to the next security question in the validation flow
  /// Called when current question is answered correctly but more questions remain
  void _proceedToNextQuestion() {
    if (_currentPageIndex < secQuestions.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      printty("Moving to next security question");
    }
  }

  /// Error Handling Methods
  /// These methods provide user feedback for various error scenarios

  /// Handles incorrect answer scenarios with appropriate user feedback
  /// Provides different messages based on remaining attempts
  void _handleIncorrectAnswer(int questionIndex) {
    final remainingAttempts =
        _maxAttemptsPerQuestion - _validationAttempts[questionIndex]!;

    if (remainingAttempts > 0) {
      // Show error with remaining attempts
      _showValidationError(
          "Incorrect answer. You have $remainingAttempts attempt${remainingAttempts > 1 ? 's' : ''} remaining for this question.");
    } else {
      // No more attempts - redirect to error screen
      _showMaxAttemptsExceededError();
    }
  }

  /// Shows error when maximum validation attempts are exceeded
  void _showMaxAttemptsExceededError() {
    Navigator.pushNamed(
      context,
      RoutePath.changeConfirmationScreen,
      arguments: ChangeConfirmationArg(
        title: "Security Validation Failed",
        subtitle: "Too many incorrect attempts",
        desc:
            "You have exceeded the maximum number of attempts for security questions. Please try again later or contact support.",
        isSuccess: false,
      ),
    );
  }

  /// Shows validation error messages to the user
  void _showValidationError(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
      snackBarType: SnackBarType.warning,
    );
  }

  /// Shows error when insufficient security questions are available
  void _showInsufficientQuestionsError() {
    Navigator.pushNamed(
      context,
      RoutePath.changeConfirmationScreen,
      arguments: ChangeConfirmationArg(
        title: "Insufficient Security Questions",
        subtitle: "Setup required",
        desc:
            "You need at least 2 security questions set up to change your email. Please set up your security questions first.",
        isSuccess: false,
      ),
    );
  }

  /// Shows error when loading security questions fails
  void _showLoadingError() {
    // This will trigger the error UI in the build method
    setState(() {
      secQuestions = [];
    });
  }

  /// UI Helper Methods
  /// These methods support the user interface and user experience

  /// Checks if the current question's answer input is valid (not empty)
  /// Used to enable/disable the continue button
  bool _isCurrentAnswerValid() {
    if (_currentPageIndex >= _answersValid.length) return false;
    return _answersValid[_currentPageIndex];
  }

  /// Gets the number of remaining attempts for the current question
  /// Used to show attempt counter to the user
  int _getRemainingAttempts(int questionIndex) {
    final attempts = _validationAttempts[questionIndex] ?? 0;
    return _maxAttemptsPerQuestion - attempts;
  }

  /// Builds the continue button with enhanced logic for different states
  /// Handles correctly answered questions, validation in progress, and button text
  Widget _buildContinueButton(SecQuestVM vm) {
    final isCurrentAnswerCorrect = _correctAnswers[_currentPageIndex] ?? false;
    final totalCorrectAnswers =
        _correctAnswers.values.where((correct) => correct).length;
    final isValidating = vm.busy(validateSecQuestionsState);
    final canProceed = _isCurrentAnswerValid() && !isValidating;

    // Determine button text based on current state
    String buttonText;
    if (isCurrentAnswerCorrect) {
      if (totalCorrectAnswers >= _requiredCorrectAnswers) {
        buttonText = 'Proceed to Email Change';
      } else {
        buttonText = 'Next Question';
      }
    } else {
      buttonText = 'Validate Answer';
    }

    return CustomBtn.withChild(
      borderRadius: BorderRadius.circular(Sizer.radius(20)),
      online: canProceed,
      onTap: canProceed
          ? () {
              if (isCurrentAnswerCorrect) {
                // Question already answered correctly
                if (totalCorrectAnswers >= _requiredCorrectAnswers) {
                  _navigateToNewEmailScreen();
                } else {
                  _proceedToNextQuestion();
                }
              } else {
                // Validate the current answer
                _validateSecurityAnswer(_currentPageIndex);
              }
            }
          : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            buttonText,
            style:
                FontTypography.text15.medium.withCustomColor(AppColors.white),
          ),
          const XBox(8),
          Icon(
            isCurrentAnswerCorrect ? Icons.arrow_forward : Icons.security,
            color: AppColors.white,
            size: 20,
          ),
          if (isValidating) const XBox(20),
          if (isValidating) const CupertinoActivityIndicator()
        ],
      ),
    );
  }

  /// Builds the UI for each security question page
  /// Enhanced with progress indication and validation feedback
  Widget _buildQuestionPage(int index) {
    final question = secQuestions[index];
    final isAnsweredCorrectly = _correctAnswers[index] ?? false;
    final remainingAttempts = _getRemainingAttempts(index);
    final hasAttempts =
        _validationAttempts[index] != null && _validationAttempts[index]! > 0;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(16),
          horizontal: Sizer.width(28),
        ),
        decoration: BoxDecoration(
          color: isAnsweredCorrectly
              ? AppColors.opacityGreen200
              : AppColors.grayFE,
          borderRadius: BorderRadius.circular(Sizer.height(12)),
          border: isAnsweredCorrectly
              ? Border.all(color: AppColors.textGreen, width: 2)
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question header with progress and status
            Align(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Question ${index + 1} of ${secQuestions.length}",
                        style: FontTypography.text12
                            .withCustomColor(AppColors.gray93),
                      ),
                      if (isAnsweredCorrectly)
                        Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: AppColors.textGreen,
                              size: Sizer.height(16),
                            ),
                            XBox(4),
                            Text(
                              "Correct",
                              style: FontTypography.text12
                                  .withCustomColor(AppColors.textGreen),
                            ),
                          ],
                        ),
                    ],
                  ),
                  YBox(8),
                  Container(
                    height: Sizer.height(1),
                    width: Sizer.width(90),
                    color: AppColors.grayEC,
                  ),
                ],
              ),
            ),
            YBox(20),

            // Question text
            Text(
              question.question ?? "Security Question",
              style: FontTypography.text22.semiBold,
            ),
            YBox(50),

            // Attempt counter (only show if user has made attempts and question not answered correctly)
            if (hasAttempts &&
                !isAnsweredCorrectly &&
                remainingAttempts > 0) ...[
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(12),
                  vertical: Sizer.height(8),
                ),
                decoration: BoxDecoration(
                  color: AppColors.opacityRed100,
                  borderRadius: BorderRadius.circular(Sizer.height(8)),
                ),
                child: Text(
                  "Attempts remaining: $remainingAttempts",
                  style:
                      FontTypography.text12.withCustomColor(AppColors.iconRed),
                ),
              ),
              YBox(16),
            ],

            // Answer input section
            Text(
              "Your answer",
              style: FontTypography.text12.withCustomColor(AppColors.gray93),
            ),
            YBox(8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grayAB),
                borderRadius: BorderRadius.circular(Sizer.height(12)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: Sizer.width(12),
                      top: Sizer.height(10),
                    ),
                    child: Icon(
                      Iconsax.message_edit,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  Expanded(
                    child: CustomTextField(
                      controller: _answerControllers[index],
                      hideBorder: true,
                      borderRadius: Sizer.height(12),
                      maxLines: 3,
                      contentPadding: EdgeInsets.only(
                        left: Sizer.width(8),
                        top: Sizer.height(20),
                      ),
                      onChanged: (val) {
                        // Validation is handled by the controller listener
                      },
                    ),
                  ),
                ],
              ),
            ),
            YBox(40),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SecQuestVM>(builder: (ctx, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Change Email',
        ),
        body: Builder(builder: (context) {
          if (vm.isBusy) {
            return const Center(child: CupertinoActivityIndicator());
          }

          if (secQuestions.isEmpty) {
            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
                vertical: Sizer.height(10),
              ),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Something went wrong, Please try again later",
                      style: FontTypography.text14.medium
                          .withCustomColor(AppColors.gray93),
                    ),
                    YBox(20),
                    CustomBtn.solid(
                      onTap: () {
                        _loadSecurityQuestions();
                      },
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      text: "Reload",
                    ),
                  ],
                ),
              ),
            );
          }
          return ListView(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                  vertical: Sizer.height(10),
                ),
                child: Text(
                  "For your security, please correctly answer $_requiredCorrectAnswers security questions to change your email address. You have up to $_maxAttemptsPerQuestion attempts per question.",
                  style:
                      FontTypography.text16.withCustomColor(AppColors.gray93),
                ),
              ),
              YBox(10),
              Container(
                height: Sizer.height(360),
                padding: EdgeInsets.only(
                  top: Sizer.height(20),
                ),
                child: PageView.builder(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    _currentPageIndex = index;
                    setState(() {});
                  },
                  itemCount: secQuestions.length,
                  itemBuilder: (context, index) {
                    return _buildQuestionPage(index);
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: Sizer.width(24),
                  right: Sizer.width(24),
                  top: Sizer.height(150),
                  bottom: Sizer.height(30),
                ),
                child: _buildContinueButton(vm),
              ),
              YBox(30),
            ],
          );
        }),
      );
    });
  }
}
