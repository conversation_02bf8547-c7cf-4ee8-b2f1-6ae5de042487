import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ApplyStatusSheet extends StatefulWidget {
  const ApplyStatusSheet({super.key});

  @override
  State<ApplyStatusSheet> createState() => _ApplyStatusSheetState();
}

class _ApplyStatusSheetState extends State<ApplyStatusSheet> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionVM>(builder: (context, vm, _) {
      return Container(
        margin: EdgeInsets.all(Sizer.radius(12)),
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(Sizer.radius(12)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Status",
                  style: AppTypography.text22.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const YBox(25),
            BuildListTileSelector(
              text: "All",
              isSelected: vm.statusFilter.isEmpty,
              onTap: () {
                vm.setStatusFilter(clear: true);
              },
            ),
            YBox(10),
            BuildListTileSelector(
              text: "Successful",
              isSelected: vm.statusFilter.contains(StatusType.success.name),
              onTap: () {
                vm.setStatusFilter(value: StatusType.success.name);
              },
            ),
            YBox(10),
            BuildListTileSelector(
              text: "Pending",
              isSelected: vm.statusFilter.contains(StatusType.pending.name),
              onTap: () {
                vm.setStatusFilter(value: StatusType.pending.name);
              },
            ),
            YBox(10),
            BuildListTileSelector(
              text: "Failed",
              isSelected: vm.statusFilter.contains(StatusType.fail.name),
              onTap: () {
                vm.setStatusFilter(value: StatusType.fail.name);
              },
            ),
            YBox(30),
            CustomBtn.solid(
              onTap: () {
                _applyFilter();
                Navigator.pop(context);
                vm.setSelectedCurrency(null);
                vm.clearDates();
              },
              // online: vm.statusFilter.isNotEmpty,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              text: "Apply Filter",
            ),
            const YBox(40),
          ],
        ),
      );
    });
  }

  _applyFilter() {
    var transactionVM = context.read<TransactionVM>();
    transactionVM.getFilteredTransactions(
      status: transactionVM.statusFilter,
    );
  }
}

class BuildListTileSelector extends StatelessWidget {
  const BuildListTileSelector({
    super.key,
    required this.text,
    required this.isSelected,
    this.onTap,
  });

  final String text;
  final bool isSelected;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(Sizer.radius(12)),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.grayFE : AppColors.transparent,
          borderRadius: BorderRadius.circular(Sizer.radius(8)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                text,
                style: AppTypography.text16.medium.copyWith(
                  color: isSelected ? AppColors.primaryBlue : AppColors.gray79,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                size: Sizer.radius(20),
                color: AppColors.primaryBlue,
              ),
          ],
        ),
      ),
    );
  }
}
