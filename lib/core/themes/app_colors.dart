import 'package:flutter/material.dart';

class AppColors {
  static const black = Colors.black;
  static const white = Colors.white;
  static const transparent = Colors.transparent;
  static const grey = Colors.grey;
  static const red = Colors.red;
  static const yellow = Colors.yellow;

  static const bgWhite = Color(0xFFFFFFFF);
  static const Color brandOrange = Color(0xffFE6600);
  static const primaryBlue = Color(0xFF0E3079);
  static const blue5B = Color(0xFF0B245B);
  static const primaryBlue90 = Color(0xFF1549B7);
  static const secondaryBlue = Color(0xFF415FFF);
  static const lightBlue = Color(0xFF2978D6);
  static const darkBlue100 = Color(0xFF334F98);
  static const sheetLightBlue = Color(0xFF8AD0EF);
  static const primaryLightBlue = Color(0xFFD8E4FF);

  static const blueD8 = Color(0xFF0653D8);
  static const blue900 = Color(0xFF00174A);
  static const blue800 = Color(0xFF004C6B);
  static const blue000 = Color(0xFF4A5C92);
  static const blue700 = Color(0xFF296EE6);
  static const blue600 = Color(0xFF405BA5);
  static const blue100 = Color(0xFFF5FAFF);
  static const blu000 = Color(0xFFF3FAFF);
  static const blue300 = Color(0xFFE4F3FF);
  static const blue200 = Color(0xFFEBF8FE);
  static const blue00 = Color(0xFFDFF4FF);
  static const blueFA = Color(0xFFF8F9FA);
  static const blueFF = Color(0xFFE1F5FF);
  static const blue34 = Color(0xFF000E34);
  static const blueFE = Color(0xFFFAFCFE);
  static const blue9FF = Color(0xFFF0F9FF);
  static const blueFD = Color(0xFFEDF2FD);
  static const blueE5 = Color(0xFF1A5BE5);
  static const blue8FF = Color(0xFFF0F8FF);
  static const blue3B = Color(0xFF23303B);
  static const blue6FF = Color(0xFFEBF6FF);
  static const blueCEF = Color(0xFF769CEF);
  static const blueOFE = Color(0xFFD0F0FE);
  static const blue9FD = Color(0xFFBEE9FD);
  static const blueEF7 = Color(0xFFBACEF7);
  static const blue9EB = Color(0xFFF3F9FB);
  static const blue7AB = Color(0xFF7087A8);
  static const blueBFF = Color(0xFFF5FBFF);
  static const blueFFE = Color(0xFFFAFFFE);
  static const blue8FB = Color(0xFFDFE8FB);
  static const blueCEA = Color(0xFF487CEA);
  static const blueDF2 = Color(0xFF8DADF2);
  static const blue5FF = Color(0xFFEBF5FF);
  static const splash = Color(0xFF004bff);

  static const darkBlue = Color(0xFF0E6682);
  static const darkBlue101 = Color(0xFFB4C5FF);
  static const numPad = Color(0xFFECEFF9);
  static const numPad2 = Color(0xFFE8F6FC);

  static const baseGreen = Color(0xFF00A55F);
  static const textGreen = Color(0xFF2D6A45);
  static const bgGreen = Color(0xFFE5FFF4);
  static const green48 = Color(0xFF2AA248);
  static const green99 = Color(0xFF13C999);
  static const greenF7 = Color(0xFFE8FDF7);
  static const greenEF = Color(0xFFEBF9EF);

  static const baseBlack = Color(0xFF0A090B);
  static const black900 = Color(0xFF1F1F1F);
  static const black600 = Color(0xFF51525A);
  static const black800 = Color(0xFF303033);
  static const textBlack = Color(0xFF121318);
  static const textBlack100 = Color(0xFF1B1B1E);
  static const black1A = Color(0xFF19191A);
  static const black22 = Color(0xFF1A1B22);
  static const mainBlack = Color(0xFF0C0D0E);

  static const baseGray = Color(0xFFE7EAF0);
  static const textGray = Color(0xFF76767F);
  static const lightTextGray = Color(0xFFAAAAB4);
  static const litGrey = Color(0xFFEEF0FF);
  static const litGrey100 = Color(0xFFF4F4F4);
  static const fillColor = Color(0xFFF4F3FA);

  static const gray700 = Color(0xFF4F4D55);
  static const gray800 = Color(0xFF3D3D3D);
  static const gray600 = Color(0xFF757680);
  static const gray500 = Color(0xFF8D91A0);
  static const gray200 = Color(0xFFEEEDF4);
  static const gray300 = Color(0xFFF8F8F8);
  static const gray100 = Color(0xFFFBF8FD);
  static const gray000 = Color(0xFFC6C6CF);
  static const gray0000 = Color(0xFFC7C6CA);
  static const grayE0 = Color(0xFFDAD9E0);
  static const grayDO = Color(0xFFC5C6D0);
  static const grayBF = Color(0xFFB9BCBF);
  static const grayE9 = Color(0xFFE7E8E9);
  static const grayEB = Color(0xFFE8E9EB);
  static const gray93 = Color(0xFF868A93);
  static const grayFE = Color(0xFFF6F8FE);
  static const grayEC = Color(0xFFEAEBEC);
  static const grayAB = Color(0xFFA0A4AB);
  static const gray79 = Color(0xFF6C7079);
  static const grayF4 = Color(0xFFE9EFF4);
  static const grayC3 = Color(0xFFBBBEC3);
  static const gray51 = Color(0xFF484B51);
  static const grayAE = Color(0xFFA4A9AE);
  static const grayF0 = Color(0xFFEFEFF0);
  static const gray5E7 = Color(0xFFE4E5E7);
  static const grayEFE = Color(0xFFEDEEFE);
  static const grayE91 = Color(0xFF8B8E91);
  static const grayF1 = Color(0xFFF1F1F1);
  static const grayF5 = Color(0xFFF4F5F5);
  static const grayADA = Color(0xFFDADADA);
  static const gray808 = Color(0xFF808080);
  static const grayFCF = Color(0xFFFCFCFD);
  static const grayAEC = Color(0xFFEAEAEC);
  static const gray88 = Color(0xFF6E7588);
  static const grayFO = Color(0xFFF0F0F0);
  static const grayBEB = Color(0xFFEBEBEB);
  static const grayFA = Color(0xFFFAFAFA);
  static const grayEFA = Color(0xFFD1DEFA);
  static const grayACB = Color(0xFFCACACB);

  static const opacityRed = Color(0xFFFFF1F0);
  static const opacityRed100 = Color(0xFFFFF8F7);
  static const opacityGreen = Color(0xFFF2FEF1);
  static const opacityGreen200 = Color(0xFFF0FFF9);
  static const opacityGreen100 = Color(0xFFF2FCF7);
  static const opacityWhite = Color(0xFFE2E2EB);

  static const iconGreen = Color(0xFF1BA70F);
  static const darkGreen = Color(0xFF00391D);
  static const iconRed = Color(0xFFFF5449);
  static const iconBadge = Color(0xFFBA1A1A);
  static const iconDarkBlue = Color(0xFF357F9C);

  static const borderColor = Color(0xFF242834);
  static const userBg = Color(0xFFFAF8FF);
  static const dividerColor = Color(0xFFF2F0F4);
  static const limitColor = Color(0xFFDBE1FF);

  static const textBlue800 = Color(0xFF26428B);
  static const textBlue700 = Color(0xFF00587B);

  static const textBlack1000 = Color(0xFF17191C);
  static const textBlack2000 = Color(0xFF46464A);
  static const textBlack900 = Color(0xFF101114);
  static const textBlack800 = Color(0xFF525256);
  static const textBlack700 = Color(0xFF5E5E62);
  static const textBlack600 = Color(0xFF24252D);
  static const iconBlack800 = Color(0xFF292D32);
  static const dottedColor = Color(0xFFE4E2E6);

  static const textGray900 = Color(0xFFACAAAE);
  static const textGray500 = Color(0xFF77767A);
  static const textBlue400 = Color(0xFF1FB7F8);
  static const textBlue500 = Color(0xFF5299B7);
  static const textGrey200 = Color(0xFF82898F);
  static const text3E = Color(0xFF3B3B3E);

  static const headerBlack = Color(0xFF121212);

  static const yellow3EC = Color(0xFFFDF3EC);
  static const yellowF5 = Color(0xFFFFFCF5);
  static const yellowCD = Color(0xFFFEF1CD);
  static const yellow77 = Color(0xFFFDDA77);
  static const yellowC3 = Color(0xFFFEEFC3);
  static const yellow87 = Color(0xFFFDDE87);
  static const yellowAEB = Color(0xFFFFFAEB);

  static const alertRed = Color(0xFFFF0000);
  static const suffixBg = Color(0xFFF1F7FE);
  static const pending = Color(0xFFB54708);
  static const failed = Color(0xFFD80027);
  static const fail30 = Color(0xFFFF3B30);
  static const sent = Color(0xFF6D0D6D);
  static const red00 = Color(0xFFCC0000);
  static const redE5 = Color(0xFFFFE5E5);
  static const redF5 = Color(0xFFFFF5F5);
  static const red34 = Color(0xFFED7834);

  static const purpleFF = Color(0xFFD6D9FF);
  static const purple1FF = Color(0xFFF0F1FF);

  static const blueCD3 = Color(0xFF175CD3);

  static const green59 = Color(0xFF34C759);
  static const green43 = Color(0xFF23A943);
  static const greenDF3 = Color(0xFFECFDF3);
  static const greenA48 = Color(0xFF027A48);

  static const mischkaGrey = Color(0xffD0D5DD);
}
