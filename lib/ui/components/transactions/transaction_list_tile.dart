import 'package:korrency/core/core.dart';

class TransactionListTile extends StatefulWidget {
  const TransactionListTile({
    super.key,
    required this.title,
    required this.subTitle,
    required this.amount,
    required this.status,
    required this.category,
    required this.onTap,
  });

  final String title;
  final String subTitle;
  final String amount;
  final String status;
  final String category;
  final VoidCallback onTap;

  @override
  State<TransactionListTile> createState() => _TransactionListTileState();
}

class _TransactionListTileState extends State<TransactionListTile> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(16)),
            decoration: BoxDecoration(
              color: AppColors.grayF5,
              border: Border.all(
                color: AppColors.grayF1,
              ),
              borderRadius: BorderRadius.circular(40),
            ),
            child: Skeleton.replace(
              replacement: Bone.circle(
                size: Sizer.radius(16),
              ),
              child: SvgPicture.asset(
                height: Sizer.height(20),
                width: Sizer.width(20),
                switch (widget.category) {
                  'conversion' => AppSvgs.arrowSwap2,
                  'transfer' => AppSvgs.send2,
                  _ => AppSvgs.walletCheck,
                },
              ),
            ),
          ),
          XBox(12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTypography.text14.semiBold,
                ),
                YBox(4),
                Text(
                  widget.subTitle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTypography.text12.copyWith(
                    color: AppColors.gray51,
                  ),
                )
              ],
            ),
          ),
          XBox(10),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                widget.amount,
                style: AppTypography.text14.semiBold,
              ),
              YBox(4),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: Sizer.height(7),
                    width: Sizer.width(7),
                    decoration: BoxDecoration(
                      color: getTextColor(widget.status),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  XBox(6),
                  Text(
                    widget.status,
                    style: AppTypography.text10.copyWith(
                      color: getTextColor(widget.status),
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }
}
