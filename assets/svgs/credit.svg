<svg width="36" height="32" viewBox="0 0 36 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_963_74654)">
<path d="M4.46297 11.4624C4.73816 10.3626 5.30786 8.91128 6.0128 7.99405C6.71774 7.07683 7.60935 6.2985 8.63671 5.7035C9.66408 5.1085 10.8071 4.70849 12.0005 4.5263C13.1939 4.34411 14.4143 4.38331 15.592 4.64167C16.7697 4.90002 17.7779 5.49655 18.7607 6.15612C19.5826 6.83515 20.2557 7.62402 20.6212 8.63041C20.9867 9.63681 21.1462 10.5655 20.9263 11.8322C20.7065 13.0989 20.4038 13.4631 19.7953 14.3984C19.9897 13.6216 20.0171 12.8253 19.8791 12.0422C19.7411 11.2591 19.4394 10.5089 18.9912 9.83434C18.5429 9.15982 18.0508 8.55649 17.3605 8.09324C16.6703 7.62999 15.9248 7.24328 15.1175 6.99559C14.3026 6.74559 13.5709 6.6207 12.6539 6.62673C11.7368 6.63275 10.9091 6.89175 10.0206 7.08869C9.29425 7.24968 8.61413 7.52605 7.81904 7.95944C6.98023 8.41665 4.68658 10.6921 4.46297 11.4624Z" fill="url(#paint0_linear_963_74654)"/>
</g>
<g filter="url(#filter1_dii_963_74654)">
<path d="M6.51441 23.9534C5.69845 23.1214 4.73795 21.8636 4.32505 20.8017C3.91215 19.7399 3.72693 18.6118 3.77996 17.4818C3.83299 16.3517 4.12324 15.242 4.63412 14.2159C5.14501 13.1897 5.86654 12.2673 6.7575 11.5012C7.64847 10.7352 8.74255 10.2907 9.87795 9.90132C10.94 9.64741 12.0152 9.57057 13.1093 9.85413C14.2035 10.1377 15.113 10.537 16.1075 11.4202C17.1019 12.3033 17.2531 12.7425 17.7362 13.7399C17.1599 13.1523 16.4723 12.6828 15.7034 12.3488C14.9344 12.0148 14.1024 11.8261 13.2546 11.7933C12.4069 11.7605 11.5973 11.8018 10.7998 12.0753C10.0024 12.3487 9.2414 12.708 8.56833 13.1934C7.889 13.6833 7.36691 14.1793 6.85606 14.8924C6.34521 15.6055 6.10793 16.3919 5.78167 17.1904C5.51496 17.8432 5.37611 18.5251 5.31116 19.3844C5.24263 20.2909 5.9603 23.3468 6.51441 23.9534Z" fill="url(#paint1_linear_963_74654)"/>
</g>
<g filter="url(#filter2_dii_963_74654)">
<path d="M19.101 29.4301C17.913 29.6268 16.2639 29.7085 15.0858 29.4634C13.9077 29.2182 12.7923 28.7577 11.8034 28.1081C10.8145 27.4585 9.97135 26.6325 9.32219 25.6774C8.67303 24.7222 8.23053 23.6566 8.01997 22.5412C7.8094 21.4259 7.99758 20.3232 8.25766 19.2176C8.60024 18.2426 9.10931 17.3526 9.95308 16.6414C10.7969 15.9302 11.6456 15.427 12.9756 15.1169C14.3056 14.8069 14.7824 14.9238 15.9401 15.0789C15.1012 15.2177 14.3087 15.508 13.5944 15.9353C12.8801 16.3627 12.2626 16.918 11.7772 17.5696C11.2918 18.2212 10.8936 18.8825 10.7109 19.659C10.5282 20.4356 10.4424 21.2295 10.5175 22.0219C10.5933 22.8216 10.759 23.5005 11.1264 24.2873C11.4937 25.0741 12.0741 25.6843 12.6176 26.3713C13.0619 26.9329 13.6012 27.4092 14.34 27.9227C15.1193 28.4643 18.2568 29.5413 19.101 29.4301Z" fill="url(#paint2_linear_963_74654)"/>
</g>
<g filter="url(#filter3_dii_963_74654)">
<path d="M30.8248 20.3474C30.5754 21.4526 30.0398 22.9152 29.3565 23.8467C28.6731 24.7781 27.7999 25.5746 26.7867 26.1905C25.7735 26.8064 24.6401 27.2297 23.4513 27.4364C22.2624 27.643 21.0414 27.6288 19.8579 27.3946C18.6745 27.1605 17.6525 26.5848 16.6546 25.9455C15.817 25.2835 15.1257 24.5086 14.7367 23.5099C14.3477 22.5113 14.1666 21.5861 14.3568 20.3152C14.5469 19.0442 14.841 18.6739 15.4275 17.7265C15.2514 18.507 15.2426 19.3037 15.3988 20.0838C15.5551 20.8639 15.8743 21.6077 16.3382 22.2729C16.8021 22.9381 17.3082 23.5312 18.0091 23.9801C18.7101 24.4291 19.4644 24.8005 20.2773 25.0316C21.0978 25.2648 21.8323 25.3746 22.7489 25.3498C23.6656 25.325 24.4871 25.0491 25.3708 24.834C26.0932 24.6581 26.7667 24.3679 27.5515 23.9183C28.3794 23.444 30.6193 21.1221 30.8248 20.3474Z" fill="url(#paint3_linear_963_74654)"/>
</g>
<g filter="url(#filter4_dii_963_74654)">
<path d="M28.4553 7.52218C29.2903 8.33746 30.2797 9.57548 30.717 10.6287C31.1544 11.6819 31.3657 12.806 31.3388 13.9368C31.3119 15.0677 31.0474 16.183 30.5604 17.2193C30.0734 18.2556 29.3734 19.1924 28.5003 19.9764C27.6273 20.7603 26.5438 21.2269 25.4176 21.6392C24.3617 21.9146 23.2885 22.0132 22.1881 21.7519C21.0876 21.4906 20.1691 21.1098 19.1544 20.2471C18.1398 19.3843 17.9785 18.9482 17.4725 17.9609C18.0622 18.5367 18.7604 18.9921 19.5369 19.3104C20.3134 19.6288 21.1497 19.8006 21.998 19.8162C22.8463 19.8317 23.6547 19.774 24.4457 19.4844C25.2366 19.1949 25.9891 18.8202 26.6508 18.3213C27.3186 17.8177 27.8291 17.3113 28.3233 16.588C28.8176 15.8647 29.0366 15.0736 29.3443 14.2687C29.5958 13.6107 29.7189 12.9261 29.7639 12.0657C29.8115 11.158 29.0233 8.11743 28.4553 7.52218Z" fill="url(#paint4_linear_963_74654)"/>
</g>
<g filter="url(#filter5_dii_963_74654)">
<path d="M14.7983 2.73595C15.9478 2.39327 17.5707 2.10711 18.7732 2.20352C19.9756 2.29992 21.1465 2.61761 22.2188 3.13842C23.2911 3.65924 24.244 4.37299 25.0229 5.23893C25.8018 6.10487 26.3916 7.10604 26.7586 8.18527C27.1255 9.26449 27.0955 10.3808 26.9946 11.5091C26.7934 12.518 26.4152 13.4635 25.6798 14.2733C24.9444 15.0832 24.1746 15.6875 22.9004 16.1603C21.6262 16.633 21.137 16.5764 19.9675 16.5668C20.7794 16.3248 21.5236 15.9385 22.1709 15.4261C22.8182 14.9136 23.3514 14.2864 23.7401 13.5802C24.1288 12.8741 24.4296 12.1691 24.5005 11.3767C24.5714 10.5843 24.5437 9.7867 24.3569 9.0107C24.1683 8.22749 23.9077 7.57525 23.432 6.84114C22.9563 6.10703 22.2944 5.5744 21.6582 4.96111C21.1382 4.45978 20.5361 4.05474 19.731 3.63778C18.8817 3.1979 15.6192 2.52076 14.7983 2.73595Z" fill="url(#paint5_linear_963_74654)"/>
</g>
<defs>
<filter id="filter0_dii_963_74654" x="2.96531" y="2.91599" width="20.1579" height="13.5791" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<filter id="filter1_dii_963_74654" x="2.27341" y="8.17576" width="17.5593" height="17.8745" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<filter id="filter2_dii_963_74654" x="6.43161" y="13.4248" width="14.7661" height="18.2871" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<filter id="filter3_dii_963_74654" x="12.7832" y="16.229" width="20.1381" height="13.4492" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<filter id="filter4_dii_963_74654" x="15.9748" y="6.02439" width="17.4629" height="17.9765" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<filter id="filter5_dii_963_74654" x="13.3008" y="0.686986" width="15.8569" height="17.9834" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feGaussianBlur stdDeviation="0.898551"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0313726 0 0 0 0 0.188235 0 0 0 0 0.380392 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_963_74654"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_963_74654" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.299517" dy="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_963_74654"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.299517" dy="-0.299517"/>
<feGaussianBlur stdDeviation="0.299517"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_963_74654" result="effect3_innerShadow_963_74654"/>
</filter>
<linearGradient id="paint0_linear_963_74654" x1="21.2782" y1="10.5201" x2="5.6756" y2="6.61607" gradientUnits="userSpaceOnUse">
<stop stop-color="#1384B4"/>
<stop offset="1" stop-color="#30A2D2"/>
</linearGradient>
<linearGradient id="paint1_linear_963_74654" x1="12.4594" y1="18.8419" x2="6.40696" y2="11.8026" gradientUnits="userSpaceOnUse">
<stop stop-color="#091735"/>
<stop offset="1" stop-color="#0F3381"/>
</linearGradient>
<linearGradient id="paint2_linear_963_74654" x1="17.696" y1="21.9883" x2="8.25218" y2="23.7712" gradientUnits="userSpaceOnUse">
<stop stop-color="#091735"/>
<stop offset="1" stop-color="#0F3381"/>
</linearGradient>
<linearGradient id="paint3_linear_963_74654" x1="22.928" y1="18.785" x2="21.1732" y2="27.6549" gradientUnits="userSpaceOnUse">
<stop stop-color="#091735"/>
<stop offset="1" stop-color="#0F3381"/>
</linearGradient>
<linearGradient id="paint4_linear_963_74654" x1="22.63" y1="12.753" x2="28.8413" y2="19.6702" gradientUnits="userSpaceOnUse">
<stop stop-color="#091735"/>
<stop offset="1" stop-color="#0F3381"/>
</linearGradient>
<linearGradient id="paint5_linear_963_74654" x1="17.2468" y1="9.93707" x2="26.3068" y2="6.8565" gradientUnits="userSpaceOnUse">
<stop stop-color="#091735"/>
<stop offset="1" stop-color="#0F3381"/>
</linearGradient>
</defs>
</svg>
