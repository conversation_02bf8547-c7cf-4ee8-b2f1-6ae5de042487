class SendMoneyParams {
  final int? sourceCurrencyId;
  final int? targetCurrencyId;
  final String? amount;
  final String? pin;
  final String? rate;
  final int? transactionPurposeId;

  final String? destinationBankUuid;
  final String? destinationBankAccountNumber;
  final String? accountName;
  final String? transferMethod;
  final String? bankName;

  final String? korrencyUsername;

  final String? interacEmail;
  final String? interacFirstName;
  final String? interacLastName;

  final int? saveBeneficiary;

  SendMoneyParams({
    this.sourceCurrencyId,
    this.targetCurrencyId,
    this.amount,
    this.pin,
    this.rate,
    this.transactionPurposeId,
    this.destinationBankUuid,
    this.destinationBankAccountNumber,
    this.accountName,
    this.transferMethod,
    this.bankName,
    this.korrencyUsername,
    this.interacEmail,
    this.interacFirstName,
    this.interacLastName,
    this.saveBeneficiary,
  });

  SendMoneyParams copyWith({
    int? sourceCurrencyId,
    int? targetCurrencyId,
    String? amount,
    String? pin,
    String? rate,
    int? transactionPurposeId,
    String? destinationBankUuid,
    String? destinationBankAccountNumber,
    String? accountName,
    String? transferMethod,
    String? bankName,
    String? korrencyUsername,
    String? interacEmail,
    String? interacFirstName,
    String? interacLastName,
    int? saveBeneficiary,
  }) {
    return SendMoneyParams(
      sourceCurrencyId: sourceCurrencyId ?? this.sourceCurrencyId,
      targetCurrencyId: targetCurrencyId ?? this.targetCurrencyId,
      amount: amount ?? this.amount,
      pin: pin ?? this.pin,
      rate: rate ?? this.rate,
      transactionPurposeId: transactionPurposeId ?? this.transactionPurposeId,
      destinationBankUuid: destinationBankUuid ?? this.destinationBankUuid,
      destinationBankAccountNumber:
          destinationBankAccountNumber ?? this.destinationBankAccountNumber,
      accountName: accountName ?? this.accountName,
      transferMethod: transferMethod ?? this.transferMethod,
      bankName: bankName ?? this.bankName,
      korrencyUsername: korrencyUsername ?? this.korrencyUsername,
      interacEmail: interacEmail ?? this.interacEmail,
      interacFirstName: interacFirstName ?? this.interacFirstName,
      interacLastName: interacLastName ?? this.interacLastName,
      saveBeneficiary: saveBeneficiary ?? this.saveBeneficiary,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'source_currency_id': sourceCurrencyId,
      'target_currency_id': targetCurrencyId,
      'amount': amount,
      'pin': pin,
      'rate': rate,
      'transaction_purpose_id': transactionPurposeId,
      'destination_bank_uuid': destinationBankUuid,
      'destination_bank_account_number': destinationBankAccountNumber,
      'account_name': accountName,
      'transfer_method': transferMethod,
      'bank_name': bankName,
      'korrency_username': korrencyUsername,
      'interac_email': interacEmail,
      'interac_first_name': interacFirstName,
      'interac_last_name': interacLastName,
      'save_beneficiary': saveBeneficiary,
    };
  }
}
