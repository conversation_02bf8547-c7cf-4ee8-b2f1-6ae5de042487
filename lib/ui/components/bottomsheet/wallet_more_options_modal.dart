// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class WalletMoreOptionsModal extends StatefulWidget {
  const WalletMoreOptionsModal({
    super.key,
    required this.wallet,
  });

  final Wallet wallet;

  @override
  State<WalletMoreOptionsModal> createState() => _WalletMoreOptionsModalState();
}

class _WalletMoreOptionsModalState extends State<WalletMoreOptionsModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: Sizer.screenWidth,
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(12)),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Y<PERSON><PERSON>(20),
                Text(
                  "More Options",
                  style: AppTypography.text18.semiBold,
                ),
                YBox(30),
                MoreOptionListTile(
                  title: 'Account statement',
                  subTitle:
                      'Fetch your account statement and have it sent to your email.',
                  svgIcon: AppSvgs.notificationStatus,
                  onTap: () {
                    BsWrapper.bottomSheet(
                      context: context,
                      widget: AccountStatementModal(
                        wallet: widget.wallet,
                      ),
                    );
                  },
                ),
                YBox(80),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class MoreOptionListTile extends StatelessWidget {
  const MoreOptionListTile({
    super.key,
    required this.title,
    required this.subTitle,
    required this.svgIcon,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String svgIcon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(12)),
            decoration: BoxDecoration(
              color: AppColors.blue100,
              borderRadius: BorderRadius.circular(Sizer.radius(4)),
            ),
            child: SvgPicture.asset(
              AppSvgs.notificationStatus,
              height: Sizer.height(20),
            ),
          ),
          XBox(16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Account statement',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlue,
                  ),
                ),
                YBox(2),
                Text(
                  'Fetch your account statement and have it sent to your email.',
                  style: AppTypography.text12.copyWith(
                    color: AppColors.gray79,
                  ),
                ),
              ],
            ),
          ),
          XBox(16),
          SvgPicture.asset(
            AppSvgs.arrowSquareRight,
            height: Sizer.height(20),
          ),
        ],
      ),
    );
  }
}
