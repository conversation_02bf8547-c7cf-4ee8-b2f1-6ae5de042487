import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class AuthorizeTransactionScreen extends StatefulWidget {
  const AuthorizeTransactionScreen({
    super.key,
    required this.args,
  });

  final OfferTypeArg args;

  @override
  State<AuthorizeTransactionScreen> createState() =>
      _AuthorizeTransactionScreenState();
}

class _AuthorizeTransactionScreenState
    extends State<AuthorizeTransactionScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<CreateOfferVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const YBox(20),
                  const ArrowBack(),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Authorize Transaction",
                    subtitle: "Enter your PIN to authorize this transaction",
                  ),
                  const YBox(30),
                  Expanded(
                    child: TransactionPIN(
                      onSubmit: (pin) {
                        vm.setPin(pin);
                      },
                    ),
                  ),
                  const YBox(50),
                  CustomBtn.solid(
                    onTap: () {
                      _completeTransaction();
                    },
                    online: vm.pin?.isNotEmpty ?? false,
                    text: "Authorize",
                  ),
                  const YBox(40),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _completeTransaction() {
    var marketplaceVM = context.read<CreateOfferVM>();
    var currencyVM = context.read<CurrencyVM>();
    marketplaceVM
        .createOffer(
      askingId: currencyVM.getCurrencyByCode("NGN")?.id ?? 0,
      tradingId: currencyVM.getCurrencyByCode("CAD")?.id ?? 0,
      offerType:
          widget.args.offerType == OfferType.sell ? "sell_offer" : "buy_offer",
    )
        .then((value) {
      if (value.success) {
        marketplaceVM.clearData();
        _moveToNextScreen();
      } else {
        _showErrorConfirmationScreen(isFailed: true, msg: value.message);
      }
    });
  }

  _moveToNextScreen() {
    Navigator.pushReplacementNamed(
      context,
      RoutePath.offerTransactionStatusScreen,
    );
  }

  _showErrorConfirmationScreen({String? msg, bool isFailed = false}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }
}
