import 'dart:convert';

import 'package:korrency/core/core.dart';

class TrustedDeviceVM extends BaseVM {
  TextEditingController otpC = TextEditingController();

  Device? _primaryDevice;
  Device? get primaryDevice => _primaryDevice;
  List<Device> _otherDevices = [];
  List<Device> get otherDevices => _otherDevices;

  bool _deleteDeviceLoading = false;
  bool get deleteDeviceLoading => _deleteDeviceLoading;

  setDeleteDeviceLoading(bool value) {
    _deleteDeviceLoading = value;
    reBuildUI();
  }

  Future<ApiResponse> getTrustedDevices() async {
    return await performApiCall(
      url: "/auth/trusted-devices",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var allDevice = trustedDeviceFromJson(json.encode(data["data"]));
        _primaryDevice = allDevice.primaryDevice;
        _otherDevices = allDevice.otherDevices ?? [];
        return ApiResponse(success: true, data: allDevice);
      },
    );
  }

  Future<ApiResponse> deleteTrustedDevice(int id) async {
    setDeleteDeviceLoading(true);
    return await performApiCall(
      url: "/auth/trusted-devices/$id",
      method: apiService.deleteWithAuth,
      onSuccess: (data) {
        setDeleteDeviceLoading(false);
        return apiResponse;
      },
      onError: (errorMessage) {
        setDeleteDeviceLoading(false);
        return ApiResponse(success: false, message: errorMessage);
      },
    );
  }

  Future<ApiResponse> requestOtp() async {
    return await performApiCall(
      url: "/auth/trusted-devices/request-otp",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return ApiResponse(success: true, data: apiResponse.data);
      },
    );
  }

  Future<ApiResponse> verifyAndSetTrustedDevice() async {
    printty('verifyAndSetTrustedDevice code ${otpC.text.trim()}');
    printty('verifyAndSetTrustedDevice url: /auth/trusted-devices/verify');
    return await performApiCall(
      url: "/auth/trusted-devices/verify",
      method: apiService.postWithAuth,
      body: {
        "code": otpC.text.trim(),
      },
      onSuccess: (data) {
        return ApiResponse(success: true, data: apiResponse.data);
      },
    );
  }

  @override
  void dispose() {
    printty("Trusted Device got Disposed");

    otpC.dispose();
    super.dispose();
  }
}
