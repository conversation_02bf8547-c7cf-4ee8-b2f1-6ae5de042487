import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendModal extends StatefulWidget {
  const SendModal({super.key});

  @override
  State<SendModal> createState() => _SendModalState();
}

class _SendModalState extends State<SendModal> {
  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, walletVm, _) {
      return Padding(
        padding: EdgeInsets.all(Sizer.radius(12)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(Sizer.width(10)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppColors.white.withOpacity(0.15),
                      border: Border.all(
                        color: AppColors.white.withOpacity(0.70),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.close,
                      size: Sizer.width(16),
                      color: AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
            const YBox(12),
            Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
              decoration: BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  YBox(20),
                  _buildListTile(
                    icon: AppSvgs.add,
                    title: "Add Money",
                    onTap: () {
                      BsWrapper.bottomSheet(
                        context: context,
                        widget: SelectWalletModal(),
                      );
                    },
                  ),
                  YBox(20),
                  _buildListTile(
                    icon: AppSvgs.send2,
                    title: "Send Money",
                    onTap: () {
                      Navigator.pushNamed(context, RoutePath.sendMoneyScreen);
                    },
                  ),
                  YBox(20),
                  _buildListTile(
                    icon: AppSvgs.arrowSwap,
                    title: "Convert Money",
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  YBox(20),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildListTile({
    required String icon,
    required String title,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(12)),
            decoration: BoxDecoration(color: AppColors.blue100),
            child: SvgPicture.asset(
              icon,
              colorFilter:
                  ColorFilter.mode(AppColors.lightBlue, BlendMode.srcIn),
            ),
          ),
          XBox(16),
          Expanded(
            child: Text(
              title,
              style: AppTypography.text14.copyWith(
                color: AppColors.primaryBlue,
              ),
            ),
          ),
          Icon(
            Iconsax.arrow_right,
            size: Sizer.radius(20),
            color: AppColors.lightBlue,
          )
        ],
      ),
    );
  }
}
