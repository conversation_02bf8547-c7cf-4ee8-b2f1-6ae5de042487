import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';

class CreateAccountScreen extends StatefulWidget {
  const CreateAccountScreen({super.key});

  @override
  State<CreateAccountScreen> createState() => _CreateAccountScreenState();
}

class _CreateAccountScreenState extends State<CreateAccountScreen>
    with TickerProviderStateMixin {
  late AnimationController _customController;
  late Animation<double> _customAnimation;

  FocusNode phoneNumFocus = FocusNode();
  // final Telephony telephony = Telephony.instance;

  @override
  void initState() {
    _customController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _customAnimation = CurvedAnimation(
      parent: _customController,
      curve: Curves.easeInOut,
    );

    // Start form animation after a delay
    Future.delayed(const Duration(milliseconds: 300), () {
      _customController.forward();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OnBoardVM>().setCounctry(CountryModel.defaultCountry);
      _resetData();
    });

    super.initState();
  }

  _resetData() {
    printty("All reset");
    context.read<DashboardVM>().resetData();
    context.read<AuthUserVM>().resetData();
    context.read<TransactionVM>().resetData();
    context.read<WalletVM>().resetData();
    context.read<InactivityVM>()
      ..setPauseAction(false)
      ..setUserHasLoggedIn(false);
  }

  // SMS Autifill
  // void listenToIncomingSMS() {
  //   printty("Listening to sms.");
  //   telephony.listenIncomingSms(
  //       onNewMessage: (SmsMessage message) {
  //         // Handle message
  //         printty("sms received : ${message.body}");
  //         // verify if we are reading the correct sms or not

  //         if (message.body!.contains("phone-auth-15bdb")) {
  //           String otpCode = message.body!.substring(0, 6);
  //           setState(() {
  //             // _otpContoller.text = otpCode;
  //             // wait for 1 sec and then press handle submit
  //             Future.delayed(const Duration(seconds: 1), () {
  //               // handleSubmit(context);
  //             });
  //           });
  //         }
  //       },
  //       listenInBackground: false);
  // }

  @override
  void dispose() {
    phoneNumFocus.dispose();
    super.dispose();
  }

  bool get btnIsValid => context.read<OnBoardVM>().phoneNumC.text.length > 6;

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            appBar: NewCustomAppbar(
              showHeaderTitle: true,
              headerText: 'Create an Account',
              onBackBtnTap: () {
                Navigator.pop(context);
              },
              rightWidget: LanguageSelector(
                onTap: () {
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: PreferredLanguageModal(),
                  );
                },
              ),
            ),
            body: FadeTransition(
              opacity: _customAnimation,
              child: SlideTransition(
                position: _customAnimation.drive(
                  Tween<Offset>(
                    begin: const Offset(0, 0.2),
                    end: const Offset(0, 0),
                  ),
                ),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                  child: ListView(
                    children: [
                      Text(
                        "Enter your phone number to begin",
                        style: AppTypography.text16
                            .copyWith(color: AppColors.gray93),
                      ),
                      const YBox(50),
                      Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: CustomTextField(
                              controller: vm.dialCodeC,
                              labelText: "Country",
                              showLabelHeader: true,
                              borderRadius: Sizer.height(12),
                              isReadOnly: true,
                              suffixIcon: Icon(
                                Iconsax.arrow_down_1,
                                color: AppColors.gray500,
                                size: Sizer.height(12),
                              ),
                              prefixIcon: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: Sizer.width(12)),
                                child: SvgPicture.asset(
                                  vm.countryModel.flag ?? "",
                                ),
                              ),
                              onTap: () async {
                                final res = await BsWrapper.bottomSheet(
                                    context: context,
                                    widget: CountryPickerModal());

                                if (res is CountryModel) {
                                  vm.setCounctry(res);
                                }
                              },
                            ),
                          ),
                          const XBox(10),
                          Expanded(
                            flex: 10,
                            child: CustomTextField(
                              controller: vm.phoneNumC,
                              focusNode: phoneNumFocus,
                              labelText: "Phone Number",
                              keyboardType: KeyboardType.phone,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(10),
                              ],
                              showLabelHeader: true,
                              borderRadius: Sizer.height(12),
                              onChanged: (val) {
                                // if (val.trim().length > 10) {
                                //   phoneC.text = val.substring(0, 10);
                                // }
                                setState(() {});
                              },
                              onSubmitted: (val) {
                                // focusNode.unfocus();
                              },
                            ),
                          ),
                        ],
                      ),
                      const YBox(40),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomCheckbox(
                            isSelected: vm.optedForProductUpdate,
                            onTap: () {
                              vm.setOptedForProduct(!vm.optedForProductUpdate);
                            },
                          ),
                          const XBox(8),
                          Expanded(
                            child: Text(
                              "Send me updates and offers",
                              style: AppTypography.text12.copyWith(
                                color: vm.optedForProductUpdate
                                    ? AppColors.baseBlack
                                    : AppColors.gray500,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          )
                        ],
                      ),
                      const YBox(16),
                      Row(
                        children: [
                          // CustomCheckbox(
                          //   isSelected: vm.acceptedTermsAndConditions,
                          //   onTap: () {
                          //     vm.setTemAndCondition(
                          //         !vm.acceptedTermsAndConditions);
                          //     // setState(() {
                          //     //   acceptedTerms = !acceptedTerms;
                          //     // });
                          //   },
                          // ),
                          // const XBox(8),
                          Expanded(
                            child: RichText(
                                text: TextSpan(
                              children: [
                                TextSpan(
                                  text:
                                      "By continuing, you agree to Korrency’s  ",
                                  style: AppTypography.text12.copyWith(
                                    color: vm.acceptedTermsAndConditions
                                        ? AppColors.baseBlack
                                        : AppColors.gray500,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                                TextSpan(
                                  text: "Terms of service",
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.lightBlue,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Navigator.pushNamed(
                                        context,
                                        RoutePath.createFreshDeskTicketWebview,
                                        arguments: WebViewArg(
                                          webURL: AppUtils
                                              .korrencyTermsAndCondition,
                                        ),
                                      );
                                    },
                                ),
                                TextSpan(
                                  text: " and ",
                                  style: AppTypography.text12.copyWith(
                                    color: vm.acceptedTermsAndConditions
                                        ? AppColors.baseBlack
                                        : AppColors.gray500,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                                TextSpan(
                                  text: "Privacy Policy",
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.lightBlue,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      Navigator.pushNamed(
                                        context,
                                        RoutePath.createFreshDeskTicketWebview,
                                        arguments: WebViewArg(
                                          webURL: AppUtils.korrencyPolicy,
                                        ),
                                      );
                                    },
                                ),
                              ],
                            )),
                          )
                        ],
                      ),
                      const YBox(230),
                      CustomBtn.withChild(
                        onTap: () {
                          _requestOtp();
                        },
                        online: btnIsValid,
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                        child: ContinueText(isOnline: btnIsValid),
                      ),
                      const YBox(30),
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, RoutePath.loginScreen);
                          vm.clearData();
                        },
                        child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: "Do you already have an Account? ",
                              style: AppTypography.text14.copyWith(
                                color: AppColors.gray500,
                                fontWeight: FontWeight.w400,
                                fontFamily: AppFont.outfit.family,
                                height: 2,
                              ),
                              children: [
                                TextSpan(
                                  text: "Log In",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.primaryBlue90,
                                    fontWeight: FontWeight.w500,
                                    height: 1.2,
                                    fontFamily: AppFont.outfit.family,
                                  ),
                                ),
                              ],
                            )),
                      ),
                      const YBox(30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _requestOtp() async {
    phoneNumFocus.unfocus();
    context.read<OnBoardVM>().requestOtp().then((value) {
      if (value.success) {
        printty(value.message);
        return _pushToVerifyPhoneNum();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _pushToVerifyPhoneNum() {
    Navigator.pushNamed(context, RoutePath.verifyPhoneNumScreen);
  }
}
