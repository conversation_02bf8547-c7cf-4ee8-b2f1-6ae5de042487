import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewPasswordScreen extends StatefulWidget {
  const NewPasswordScreen({super.key});

  @override
  State<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends State<NewPasswordScreen> {
  final FocusNode _passwordFocusNode = FocusNode();

  @override
  void dispose() {
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: "Enter Your Password",
        onBackBtnTap: () {
          context.read<PasskeyResetVM>().clearData();
          Navigator.pop(context);
        },
      ),
      body: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Before you go on, we need your password to continue",
              style: AppTypography.text16.copyWith(color: AppColors.gray93),
            ),
            // const YBox(30),
            Expanded(
              child: Consumer<PasskeyResetVM>(builder: (context, vm, _) {
                return Column(
                  children: [
                    const YBox(40),
                    CustomTextField(
                      labelText: "Password",
                      focusNode: _passwordFocusNode,
                      showLabelHeader: true,
                      borderRadius: Sizer.height(12),
                      controller: vm.oldPasswordC,
                      isPassword: true,
                      onChanged: (val) => vm.reBuildUI(),
                    ),
                    const Spacer(),
                    CustomBtn.solid(
                      onTap: () {
                        _passwordFocusNode.unfocus();
                        Navigator.pushNamed(
                            context, RoutePath.changePasswordScreen);
                      },
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      online: vm.oldPasswordC.text.trim().isNotEmpty,
                      text: "Continue",
                    ),
                    const YBox(50)
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
