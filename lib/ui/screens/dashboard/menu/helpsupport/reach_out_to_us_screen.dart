import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/screens.dart';

class ReachOutToUsScreen extends StatelessWidget {
  const ReachOutToUsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<ConfigVM>(
      builder: (context, vm, _) {
        return Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const ArrowBack(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Reach out to us",
                    subtitle: "We're here for you 24/7 by phone or email",
                  ),
                  const YBox(40),
                  imageHelper(
                    AppImages.contactUs,
                    height: Sizer.height(228),
                    width: Sizer.screenWidth,
                  ),
                  const YBox(30),
                  Container(
                    width: Sizer.screenWidth,
                    color: AppColors.white,
                    child: Column(
                      children: [
                        MenuListTileOLD(
                          title: vm.emailAddress ?? "",
                          iconData: Iconsax.sms,
                          trailingWidget: CopyWithIcon(
                            onPressed: () {
                              Clipboard.setData(ClipboardData(
                                text: vm.emailAddress ?? "",
                              ));
                              FlushBarToast.fLSnackBar(
                                message: 'Email copied',
                              );
                            },
                          ),
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.createFreshDeskTicketWebview,
                              arguments: WebViewArg(
                                appBarText: "Create Ticket",
                                webURL: AppUtils.korrencyCreateTicket,
                              ),
                            );
                          },
                        ),
                        const YBox(20),
                        MenuListTileOLD(
                          title: vm.phoneNumber ?? "",
                          iconData: Iconsax.call,
                          trailingWidget: CopyWithIcon(
                            onPressed: () {
                              Clipboard.setData(ClipboardData(
                                text: vm.phoneNumber ?? "",
                              ));

                              FlushBarToast.fLSnackBar(
                                message: 'Phone number copied',
                              );
                            },
                          ),
                          onPressed: () {
                            AppUtils.openPhoneDialler(
                                url: vm.phoneNumber ?? "");
                          },
                        ),
                      ],
                    ),
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
