import 'package:korrency/core/core.dart';

class CustomCheckRounded extends StatefulWidget {
  const CustomCheckRounded({
    super.key,
    this.isSelected = false,
    this.selectedColor,
    this.unselectedColor,
    this.borderColor,
    this.selectedBorderColor,
    this.onTap,
    this.size,
    this.borderRadius,
    this.borderWidth,
    this.animationDuration,
    this.animationCurve,
    this.padding,
    this.child,
    this.selectedChild,
    this.splashColor,
    this.highlightColor,
    this.enabled = true,
    this.focusNode,
    this.autofocus = false,
    this.semanticLabel,
  });

  /// Whether the checkbox is currently selected
  final bool isSelected;

  /// Color of the checkbox when selected
  final Color? selectedColor;

  /// Color of the checkbox when unselected
  final Color? unselectedColor;

  /// Border color when unselected
  final Color? borderColor;

  /// Border color when selected
  final Color? selectedBorderColor;

  /// Callback when the checkbox is tapped
  final VoidCallback? onTap;

  /// Size of the checkbox (width and height)
  final double? size;

  /// Border radius of the checkbox
  final double? borderRadius;

  /// Width of the border
  final double? borderWidth;

  /// Duration of the selection animation
  final Duration? animationDuration;

  /// Animation curve for the selection animation
  final Curve? animationCurve;

  /// Internal padding of the checkbox
  final EdgeInsetsGeometry? padding;

  /// Custom child widget to display when unselected
  final Widget? child;

  /// Custom child widget to display when selected
  final Widget? selectedChild;

  /// Splash color for the ink well effect
  final Color? splashColor;

  /// Highlight color for the ink well effect
  final Color? highlightColor;

  /// Whether the checkbox is enabled
  final bool enabled;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  /// Whether to autofocus the checkbox
  final bool autofocus;

  /// Semantic label for accessibility
  final String? semanticLabel;

  @override
  State<CustomCheckRounded> createState() => _CustomCheckRoundedState();
}

class _CustomCheckRoundedState extends State<CustomCheckRounded>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  // Default values
  static const double _defaultSize = 20.0;
  static const double _defaultBorderRadius = 20.0;
  static const double _defaultBorderWidth = 1.0;
  static const double _defaultPadding = 4.0;
  static const Duration _defaultAnimationDuration = Duration(milliseconds: 300);
  static const Curve _defaultAnimationCurve = Curves.easeInOut;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration ?? _defaultAnimationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: widget.animationCurve ?? _defaultAnimationCurve,
      ),
    );

    // Initialize animation state based on initial isSelected value
    if (widget.isSelected) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(CustomCheckRounded oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration if changed
    if (widget.animationDuration != oldWidget.animationDuration) {
      _controller.duration =
          widget.animationDuration ?? _defaultAnimationDuration;
    }

    // Update animation curve if changed
    if (widget.animationCurve != oldWidget.animationCurve) {
      _animation = Tween<double>(begin: 0, end: 1).animate(
        CurvedAnimation(
          parent: _controller,
          curve: widget.animationCurve ?? _defaultAnimationCurve,
        ),
      );
    }

    // Handle selection state changes
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Get default colors based on theme
  Color get _defaultSelectedColor => AppColors.primaryBlue;
  Color get _defaultUnselectedColor => Colors.transparent;
  Color get _defaultBorderColor => AppColors.gray5E7;
  Color get _defaultSelectedBorderColor => AppColors.primaryBlue;

  @override
  Widget build(BuildContext context) {
    final size = widget.size ?? _defaultSize;
    final borderRadius = widget.borderRadius ?? _defaultBorderRadius;
    final borderWidth = widget.borderWidth ?? _defaultBorderWidth;
    final padding = widget.padding ?? EdgeInsets.all(_defaultPadding);

    final selectedColor = widget.selectedColor ?? _defaultSelectedColor;
    final unselectedColor = widget.unselectedColor ?? _defaultUnselectedColor;
    final borderColor = widget.borderColor ?? _defaultBorderColor;
    final selectedBorderColor =
        widget.selectedBorderColor ?? _defaultSelectedBorderColor;

    return Semantics(
      label: widget.semanticLabel,
      checked: widget.isSelected,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        child: InkWell(
          onTap: widget.enabled ? widget.onTap : null,
          splashColor: widget.splashColor,
          highlightColor: widget.highlightColor,
          borderRadius: BorderRadius.circular(borderRadius),
          child: AnimatedContainer(
            duration: widget.animationDuration ?? _defaultAnimationDuration,
            width: size,
            height: size,
            padding: padding,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: widget.isSelected ? selectedBorderColor : borderColor,
                width: borderWidth,
              ),
              color: widget.isSelected ? null : unselectedColor,
            ),
            child: _buildContent(selectedColor, borderRadius),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(Color selectedColor, double borderRadius) {
    if (widget.child != null || widget.selectedChild != null) {
      // Use custom child widgets if provided
      return ScaleTransition(
        scale: _animation,
        child: widget.isSelected
            ? (widget.selectedChild ?? widget.child)
            : widget.child,
      );
    } else {
      // Use default checkbox appearance
      return ScaleTransition(
        scale: _animation,
        child: widget.isSelected
            ? Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(borderRadius),
                  color: selectedColor,
                ),
              )
            : null,
      );
    }
  }
}
