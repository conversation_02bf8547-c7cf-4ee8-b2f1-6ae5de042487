import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class DeactivateAccountSheet extends StatefulWidget {
  const DeactivateAccountSheet({super.key});

  @override
  State<DeactivateAccountSheet> createState() => _DeactivateAccountSheetState();
}

class _DeactivateAccountSheetState extends State<DeactivateAccountSheet> {
  final TextEditingController _passwordC = TextEditingController();

  @override
  void dispose() {
    _passwordC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)).copyWith(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          imageHelper(
            AppImages.warning,
            height: Sizer.height(100),
            width: Sizer.width(100),
          ),
          const YBox(10),
          Text(
            'Deactivate Account?',
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(4),
          Text(
            'Are you sure you want to deactivate your account?',
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          const YBox(30),
          CustomTextField(
            labelText: "Password",
            showLabelHeader: true,
            borderRadius: Sizer.height(4),
            controller: _passwordC,
            isPassword: true,
            onChanged: (val) {
              // vm.validatePassword(vm.passwordC.text);
              setState(() {});
            },
          ),
          const YBox(40),
          Consumer<AuthUserVM>(builder: (context, vm, _) {
            return CustomBtn.solid(
              onTap: () {
                vm.deactivateAccount(_passwordC.text.trim()).then((value) {
                  if (value.success) {
                    return _showComfirmationScreen(msg: value.message);
                  } else {
                    return _showComfirmationScreen(
                        msg: value.message, isFailed: true);
                  }
                });
              },
              online: _passwordC.text.trim().length > 5,
              onlineColor: AppColors.iconRed,
              text: "Deactivate",
              isLoading: vm.isBusy,
            );
          }),
          const YBox(50),
        ],
      ),
    );
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
          title: msg ?? '',
          imgPath: isFailed ? AppGifs.failure : null,
          btnText: "Continue",
          btnTap: () {
            if (isFailed) {
              Navigator.pop(context);
            } else {
              _pop();
            }
          }),
    );
  }

  _pop() {
    Navigator.pushNamedAndRemoveUntil(
      NavigatorKeys.appNavigatorKey.currentContext!,
      RoutePath.loginScreen,
      (r) => false,
    );
    StorageService.logout();
  }
}
