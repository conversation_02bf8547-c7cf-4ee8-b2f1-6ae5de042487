import 'package:korrency/core/core.dart';

class ChangeInfoArg {
  final String recipient;
  final String? dialCode;
  final bool isPhone;

  ChangeInfoArg({
    required this.recipient,
    this.dialCode,
    this.isPhone = false,
  });
}

class ChangeInfoParams {
  ChangeInfoParams({
    required this.code,
    required this.recipient,
    required this.accountChangeType,
  });

  final String code;
  final String recipient;
  final AccountChangeType accountChangeType;

  Map<String, dynamic> toJson() => {
        "code": code,
        "recipient": recipient,
        "verification_type":
            accountChangeType.verification, // email_change or phone_change
      };
}
