// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConvertCurrencyScreen extends StatefulWidget {
  const ConvertCurrencyScreen({super.key, this.convertArg});

  final ConvertArg? convertArg;

  @override
  State<ConvertCurrencyScreen> createState() => _ConvertCurrencyScreenState();
}

class _ConvertCurrencyScreenState extends State<ConvertCurrencyScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() async {
    final walletVM = context.read<WalletVM>();
    final convertMoneyVM = context.read<ConvertMoneyVM>();
    final userVM = context.read<AuthUserVM>();
    final currencyVm = context.read<CurrencyVM>();

    await convertMoneyVM.init(
      walletProvider: walletVM,
      freqDestinationCurrencyCode:
          userVM.user?.frequentDestinationCurrency?.code,

      // For coming from transaction details screen for convert again
      currencyCodesFromTransactionDetails: widget.convertArg,
    );

    // Pre-populate with ConvertArg data if provided
    if (widget.convertArg != null) {
      _prePopulateFromConvertArg(convertMoneyVM, walletVM);
    }

    await convertMoneyVM.getConversionRate();
    await fetchNewCustomerRateEligibility(
      convertMoneyVM.fromConvertWallet?.currency?.id ?? 0,
      convertMoneyVM.toConvertWallet?.currency?.id ?? 0,
    );
    await currencyVm.getCurrencyRatesLimits(
        convertMoneyVM.fromConvertWallet?.currency?.id ?? 0);
  }

  // Checks for new customer rate eligibility
  Future<void> fetchNewCustomerRateEligibility(int fromId, int toId) async {
    final convertMoneyVM = context.read<ConvertMoneyVM>();
    final rateVM = context.read<RateVm>();

    final res = await rateVM.getNewCustomerRate(fromId, toId);

    handleApiResponse(
      response: res,
      showSuccessToast: false,
      onSuccess: () {
        convertMoneyVM.checkElligibiltyForNewCustomerRate(
          rateElligibility: rateVM.showNewRateModal,
          rateAmont: rateVM
              .newCustomerRateModel?.rateData?.newCustomerRateTransactionAmount,
        );
        _showNewCustomerOfferModal();
      },
    );
  }

  void _prePopulateFromConvertArg(
      ConvertMoneyVM convertMoneyVM, WalletVM walletVM) {
    final convertArg = widget.convertArg!;

    // Set amount if provided
    if (convertArg.fromAmount != null) {
      convertMoneyVM.fromC.text = convertArg.fromAmount!;
      convertMoneyVM
          .convertAmount(double.tryParse(convertArg.fromAmount!) ?? 0);
    }

    setState(() {});
  }

  _showNewCustomerOfferModal() {
    final rateVm = context.read<RateVm>();
    if (rateVm.showNewRateModal) {
      return BsWrapper.bottomSheet(
        context: context,
        widget: const NewCustomerOfferModal(),
      );
    }
  }

  /// Returns true if the amount the user wants to send exceeds the daily exchange-rate limit.
  bool get isCurrencyLimitsExceeded {
    final currencyVM = context.read<CurrencyVM>();
    final convertVm = context.read<ConvertMoneyVM>();

    final fromAmount = double.tryParse(convertVm.fromC.text.trim()) ?? 0;
    final remainingLimit = currencyVM.currencyRatesLimits?.remainingAmount;

    // If we don’t have a limit, treat it as unlimited (return false).
    if (remainingLimit == null) return false;

    return fromAmount > remainingLimit.toDouble();
  }

  @override
  Widget build(BuildContext context) {
    var walletVM = context.read<WalletVM>();
    final currencyVM = context.watch<CurrencyVM>();
    return Consumer<ConvertMoneyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.busy(convertMoneyLoading) || vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Convert Money',
            onBackBtnTap: () {
              vm.resetData();
              Navigator.pop(context);
            },
          ),
          body: SafeArea(
            bottom: false,
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Container(
                height: Sizer.screenHeight,
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Swap your funds between wallets",
                        style: AppTypography.text16
                            .copyWith(color: AppColors.gray93),
                      ),
                      const YBox(32),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Converting from',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                          const YBox(4),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(16),
                              vertical: Sizer.height(12),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              border: Border.all(
                                color: vm.notEnoughBalanceFrom ||
                                        isCurrencyLimitsExceeded
                                    ? AppColors.red
                                    : AppColors.grayAEC,
                              ),
                              borderRadius:
                                  BorderRadius.circular(Sizer.radius(12)),
                            ),
                            child: Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CurrencyCards(
                                      isNetworkSvg: vm.fromConvertWallet
                                              ?.currency?.flag !=
                                          null,
                                      currencyCode: vm.fromConvertWallet
                                              ?.currency?.code ??
                                          "",
                                      showCurrencyCode: true,
                                      flagIconPath: vm.fromConvertWallet
                                              ?.currency?.flag ??
                                          AppImages.ngnFlag,
                                      onTap: () async {
                                        final res = await BsWrapper.bottomSheet(
                                          context: context,
                                          widget:
                                              const SelectWalletCurrencySheet(
                                            fromConvert: true,
                                          ),
                                        );

                                        if (res == true) {
                                          fetchNewCustomerRateEligibility(
                                            vm.fromConvertWallet?.currency
                                                    ?.id ??
                                                0,
                                            vm.toConvertWallet?.currency?.id ??
                                                0,
                                          );

                                          context
                                              .read<CurrencyVM>()
                                              .getCurrencyRatesLimits(vm
                                                      .fromConvertWallet
                                                      ?.currency
                                                      ?.id ??
                                                  0);
                                        }
                                      },
                                    ),
                                    const YBox(4),
                                    BalanceWidgetText(
                                        balance:
                                            vm.fromConvertWallet?.balance ??
                                                "0"),
                                  ],
                                ),
                                const Spacer(),
                                Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Container(
                                      height: Sizer.height(30),
                                      color: AppColors.white,
                                      width: Sizer.width(150),
                                      margin: EdgeInsets.only(
                                        bottom: Sizer.width(6),
                                      ),
                                      child: ConvertTextfield(
                                        hintText: "10,000.00",
                                        textAlign: TextAlign.end,
                                        color: vm.notEnoughBalanceFrom
                                            ? AppColors.red
                                            : AppColors.baseBlack,
                                        controller: vm.fromC,
                                        onChanged: (val) {
                                          //
                                          var replace = val.replaceAll(',', '');
                                          vm.convertAmount(
                                              double.tryParse(replace) ?? 0);
                                          // ..setFromAmount(replace);
                                        },
                                      ),
                                    ),
                                    YBox(4),
                                    vm.notEnoughBalanceFrom
                                        ? Text(
                                            "Balance exceeded",
                                            style:
                                                AppTypography.text12.copyWith(
                                              fontWeight: FontWeight.w300,
                                              color: AppColors.red,
                                            ),
                                          )
                                        : isCurrencyLimitsExceeded
                                            ? Text(
                                                "${currencyVM.currencyRatesLimits?.limitType ?? ""} limit exceeded",
                                                style: AppTypography.text12
                                                    .copyWith(
                                                  fontWeight: FontWeight.w300,
                                                  color: AppColors.red,
                                                ),
                                              )
                                            : InkWell(
                                                onTap: () => vm
                                                    .addAllFromWalletBalance(),
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                    horizontal: Sizer.width(8),
                                                    vertical: Sizer.height(6),
                                                  ),
                                                  decoration:
                                                      const BoxDecoration(
                                                    color: AppColors.blueFD,
                                                    borderRadius:
                                                        BorderRadius.all(
                                                      Radius.circular(8),
                                                    ),
                                                  ),
                                                  child: Text(
                                                    'Add all Funds',
                                                    style: AppTypography.text12
                                                        .copyWith(
                                                      color: AppColors.blueE5,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                    const YBox(4),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: Sizer.height(140),
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Positioned(
                              top: 0,
                              left: 16,
                              child: Column(
                                children: [
                                  SendMoneyHDivider(),
                                  BuildRateCard(text: vm.rateFormat),
                                  SendMoneyHDivider(),
                                  BuildRateCard(
                                      text: vm.transactionFee?.fee ??
                                          "0.00 CAD Transactiom fee"),
                                  SendMoneyHDivider(),
                                  InkWell(
                                    onTap: () async {
                                      await vm.swapWallets();
                                      fetchNewCustomerRateEligibility(
                                        vm.fromConvertWallet?.currency?.id ?? 0,
                                        vm.toConvertWallet?.currency?.id ?? 0,
                                      );
                                      context
                                          .read<CurrencyVM>()
                                          .getCurrencyRatesLimits(vm
                                                  .fromConvertWallet
                                                  ?.currency
                                                  ?.id ??
                                              0);
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(Sizer.radius(8)),
                                      decoration: BoxDecoration(
                                          color: AppColors.blue8FB,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          boxShadow: const [
                                            BoxShadow(
                                              color: AppColors.grayE9,
                                              blurRadius: 4,
                                              offset: Offset(0, 4),
                                            ),
                                          ]),
                                      // alignment: Alignment.center,
                                      child: Icon(
                                        Iconsax.arrow_swap,
                                        color: AppColors.primaryBlue,
                                        size: Sizer.radius(20),
                                      ),
                                    ),
                                  ),
                                  SendMoneyHDivider(height: 50),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          YBox(6),
                          Text(
                            'Converting to',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.textGray,
                            ),
                          ),
                          YBox(4),
                        ],
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: Sizer.width(16),
                          vertical: Sizer.height(26),
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.grayFE,
                          border: Border.all(color: AppColors.grayAEC),
                          borderRadius: BorderRadius.circular(Sizer.radius(12)),
                        ),
                        child: Row(
                          children: [
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CurrencyCards(
                                  isNetworkSvg: true,
                                  currencyCode:
                                      vm.toConvertWallet?.currency?.code ?? "",
                                  showCurrencyCode: true,
                                  flagIconPath: vm
                                          .toConvertWallet?.currency?.flag ??
                                      walletVM.activeWallet?.currency?.flag ??
                                      "",
                                  onTap: () async {
                                    final res = await BsWrapper.bottomSheet(
                                      context: context,
                                      widget: const SelectWalletCurrencySheet(),
                                    );

                                    if (res == true) {
                                      fetchNewCustomerRateEligibility(
                                        vm.fromConvertWallet?.currency?.id ?? 0,
                                        vm.toConvertWallet?.currency?.id ?? 0,
                                      );
                                    }
                                  },
                                ),
                                const YBox(4),
                                BalanceWidgetText(
                                    balance:
                                        vm.toConvertWallet?.balance ?? "0"),
                              ],
                            ),
                            const Spacer(),
                            Container(
                              height: Sizer.height(30),
                              width: Sizer.width(130),
                              margin: EdgeInsets.only(
                                bottom: Sizer.height(10),
                              ),
                              child: ConvertTextfield(
                                hintText: "10,000.00",
                                textAlign: TextAlign.right,
                                controller: vm.toC,
                                onChanged: (val) {
                                  var replace = val.replaceAll(',', '');
                                  vm.convertAmountTo(
                                      double.tryParse(replace) ?? 0);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      YBox(100),
                      // Container(
                      //   padding: EdgeInsets.symmetric(
                      //     horizontal: Sizer.width(14),
                      //   ),
                      //   decoration: BoxDecoration(
                      //     border: Border.all(color: AppColors.grayE9),
                      //     borderRadius: BorderRadius.circular(4),
                      //   ),
                      //   child: SizedBox(
                      //     width: Sizer.screenWidth,
                      //     child: Column(
                      //       crossAxisAlignment: CrossAxisAlignment.start,
                      //       children: [
                      //         Align(
                      //           alignment: Alignment.center,
                      //           child: Container(
                      //             width: Sizer.width(202),
                      //             padding: EdgeInsets.symmetric(
                      //               horizontal: Sizer.width(16),
                      //               vertical: Sizer.height(4),
                      //             ),
                      //             decoration: BoxDecoration(
                      //                 color: vm.setRateBannerToGreen
                      //                     ? AppColors.transparent
                      //                     : AppColors.primaryBlue,
                      //                 image: vm.setRateBannerToGreen
                      //                     ? const DecorationImage(
                      //                         image: AssetImage(
                      //                           AppImages.smGradient,
                      //                         ),
                      //                         fit: BoxFit.cover,
                      //                       )
                      //                     : null,
                      //                 borderRadius: const BorderRadius.only(
                      //                   bottomLeft: Radius.circular(8),
                      //                   bottomRight: Radius.circular(8),
                      //                 )),
                      //             child: Center(
                      //               child: Text(
                      //                 vm.rateFormat,
                      //                 style: AppTypography.text12.copyWith(
                      //                   color: AppColors.white,
                      //                   fontWeight: FontWeight.w600,
                      //                 ),
                      //               ),
                      //             ),
                      //           ),
                      //         ),
                      //         const YBox(30),
                      //         Row(
                      //           children: [
                      //             Text(
                      //               'You are converting',
                      //               style: AppTypography.text11.copyWith(
                      //                 color: AppColors.textGray,
                      //               ),
                      //             ),
                      //             const Spacer(),
                      //             Row(
                      //               mainAxisSize: MainAxisSize.min,
                      //               children: [
                      //                 Text(
                      //                   'Balance: ${AppUtils.formatAmountDoubleString(vm.fromConvertWallet?.balance ?? "0")}',
                      //                   style: AppTypography.text11.copyWith(
                      //                     color: AppColors.textGray,
                      //                   ),
                      //                 ),
                      //                 const XBox(4),
                      //                 InkWell(
                      //                   onTap: () => vm.addAllFromWalletBalance(),
                      //                   child: Container(
                      //                     padding: EdgeInsets.symmetric(
                      //                       horizontal: Sizer.width(6),
                      //                       vertical: Sizer.height(4),
                      //                     ),
                      //                     decoration: const BoxDecoration(
                      //                       color: AppColors.blueFF,
                      //                       borderRadius: BorderRadius.all(
                      //                         Radius.circular(4),
                      //                       ),
                      //                     ),
                      //                     child: Text(
                      //                       'Add All',
                      //                       style: AppTypography.text10.copyWith(
                      //                         color: AppColors.primaryBlue,
                      //                         fontWeight: FontWeight.w600,
                      //                       ),
                      //                     ),
                      //                   ),
                      //                 ),
                      //               ],
                      //             ),
                      //           ],
                      //         ),
                      //         const YBox(8),
                      //         Row(
                      //           children: [
                      //             Container(
                      //               height: Sizer.height(30),
                      //               color: AppColors.white,
                      //               width: Sizer.width(150),
                      //               child: ConvertTextfield(
                      //                 hintText: "10,000.00",
                      //                 focusNode: _fromFocusNode,
                      //                 textAlign: TextAlign.left,
                      //                 color: vm.notEnoughBalanceFrom
                      //                     ? AppColors.red
                      //                     : AppColors.baseBlack,
                      //                 controller: vm.fromC,
                      //                 onChanged: (val) {
                      //                   //
                      //                   var replace = val.replaceAll(',', '');
                      //                   vm.convertAmount(
                      //                       double.tryParse(replace) ?? 0);
                      //                   // ..setFromAmount(replace);
                      //                 },
                      //               ),
                      //             ),
                      //             const Spacer(),
                      //             _currencyFlag(
                      //                 iconPath: vm.fromConvertWallet?.currency
                      //                         ?.flag ??
                      //                     walletVM.activeWallet?.currency?.flag ??
                      //                     "",
                      //                 text:
                      //                     vm.fromConvertWallet?.currency?.code ??
                      //                         "",
                      //                 onTap: () {
                      //                   BsWrapper.bottomSheet(
                      //                     context: context,
                      //                     widget: const SelectWalletCurrencySheet(
                      //                       fromConvert: true,
                      //                     ),
                      //                   );
                      //                 }),
                      //           ],
                      //         ),
                      //         const YBox(20),
                      //         Row(
                      //           children: [
                      //             const Expanded(
                      //               child: Divider(
                      //                 color: AppColors.primaryLightBlue,
                      //               ),
                      //             ),
                      //             Center(
                      //               child: InkWell(
                      //                 onTap: () {
                      //                   vm.swapWallets();
                      //                 },
                      //                 child: Container(
                      //                   padding: EdgeInsets.all(Sizer.radius(8)),
                      //                   decoration: BoxDecoration(
                      //                     color: AppColors.blue100,
                      //                     borderRadius: BorderRadius.circular(30),
                      //                   ),
                      //                   // alignment: Alignment.center,
                      //                   child: const Icon(Iconsax.arrow_swap),
                      //                 ),
                      //               ),
                      //             ),
                      //             const Expanded(
                      //               child: Divider(
                      //                 color: AppColors.primaryLightBlue,
                      //               ),
                      //             ),
                      //           ],
                      //         ),
                      //         const YBox(20),
                      //         Column(
                      //           children: [
                      //             Row(
                      //               children: [
                      //                 Text(
                      //                   'To',
                      //                   style: AppTypography.text12.copyWith(
                      //                     color: AppColors.textGray,
                      //                   ),
                      //                 ),
                      //                 const Spacer(),
                      //                 Text(
                      //                   'Balance: ${AppUtils.formatAmountDoubleString(vm.toConvertWallet?.balance ?? "0")}',
                      //                   style: AppTypography.text12.copyWith(
                      //                     color: AppColors.textGray,
                      //                   ),
                      //                 ),
                      //               ],
                      //             ),
                      //             const YBox(16),
                      //             Row(
                      //               children: [
                      //                 SizedBox(
                      //                   height: Sizer.height(30),
                      //                   // color: AppColors.red,
                      //                   width: Sizer.width(130),
                      //                   child: ConvertTextfield(
                      //                     hintText: "10,000.00",
                      //                     focusNode: _toFocusNode,
                      //                     textAlign: TextAlign.left,
                      //                     controller: vm.toC,
                      //                     onChanged: (val) {
                      //                       var replace = val.replaceAll(',', '');
                      //                       vm.convertAmountTo(
                      //                           double.tryParse(replace) ?? 0);
                      //                     },
                      //                   ),
                      //                 ),
                      //                 const Spacer(),
                      //                 _currencyFlag(
                      //                   iconPath:
                      //                       vm.toConvertWallet?.currency?.flag ??
                      //                           walletVM.activeWallet?.currency
                      //                               ?.flag ??
                      //                           "",
                      //                   text:
                      //                       vm.toConvertWallet?.currency?.code ??
                      //                           "",
                      //                   onTap: () {
                      //                     BsWrapper.bottomSheet(
                      //                       context: context,
                      //                       widget:
                      //                           const SelectWalletCurrencySheet(),
                      //                     );
                      //                   },
                      //                 ),
                      //               ],
                      //             ),
                      //             const YBox(20),
                      //           ],
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                      const YBox(80),
                      CustomBtn.withChild(
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                        child: ContinueText(
                          isOnline:
                              vm.convertBtnEnabled && !isCurrencyLimitsExceeded,
                        ),
                        onTap: () {
                          // BsWrapper.bottomSheet(
                          //   context: context,
                          //   widget: ReviewTransactionSheet(
                          //     onConvert: () {
                          //       Navigator.pop(context);
                          //       _convert();
                          //     },
                          //   ),
                          // );

                          Navigator.pushNamed(
                              context, RoutePath.convertReviewScreen);
                        },
                        online:
                            vm.convertBtnEnabled && !isCurrencyLimitsExceeded,
                      ),
                      const YBox(50),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
}
