import 'dart:convert';

List<KorrencyUser> korrencyUserFromJson(String str) => List<KorrencyUser>.from(
    json.decode(str).map((x) => KorrencyUser.fromJson(x)));

String korrencyUserToJson(List<KorrencyUser> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class KorrencyUser {
  String? fullName;
  dynamic userName;
  String? phone;
  dynamic avatar;

  KorrencyUser({
    this.fullName,
    this.userName,
    this.phone,
    this.avatar,
  });

  factory KorrencyUser.fromJson(Map<String, dynamic> json) => KorrencyUser(
        fullName: json["full_name"],
        userName: json["user_name"],
        phone: json["phone"],
        avatar: json["avatar"],
      );

  Map<String, dynamic> toJson() => {
        "full_name": fullName,
        "user_name": userName,
        "phone": phone,
        "avatar": avatar,
      };
}
