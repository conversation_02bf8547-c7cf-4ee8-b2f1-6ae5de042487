import 'dart:convert';

import 'package:korrency/core/core.dart';

class MyOfferVM extends BaseVM {
  Offers? _selectOffer;
  Offers? get selectOffer => _selectOffer;
  List<Offers> _mySellOffers = [];
  List<Offers> get mySellOffers => _mySellOffers;
  List<Offers> _myBuyOffers = [];
  List<Offers> get myBuyOffers => _myBuyOffers;
  List<OfferDealActivity> _offerDealActivity = [];
  List<OfferDealActivity> get offerDealActivity => _offerDealActivity;
  OfferType _yourOfferType = OfferType.deal;
  OfferType get yourOfferType => _yourOfferType;

  String get offerType => _selectOffer?.type?.toLowerCase() ?? "";
  String get offerCurrencyCode => offerType == OfferConst.sellOffer
      ? _selectOffer?.askingCurrency?.code ?? ""
      : _selectOffer?.tradingCurrency?.code ?? "";

  String get offerAmount => offerType == OfferConst.sellOffer
      ? _selectOffer?.askingAmount ?? ""
      : _selectOffer?.tradingAmount ?? "";

  String get fees =>
      "${AppUtils.formatAmountDoubleString(_selectOffer?.p2pFees ?? "0")} ${_selectOffer?.tradingCurrency?.code}";

  setYourOffersType(OfferType type) {
    _yourOfferType = type;
    reBuildUI();
  }

  Future<ApiResponse> getMyOffers() async {
    return await performApiCall(
      url: "/marketplace/offers/my-offers",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var offers = offersFromJson(json.encode(data["datatable"]["data"]));
        _myBuyOffers = offers
            .where((element) => element.type?.toLowerCase() == "buy offer")
            .toList();
        _mySellOffers = offers.where((element) {
          return element.type?.toLowerCase() == "sell offer";
        }).toList();
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> viewOffer(int id) async {
    return await performApiCall(
      url: "/marketplace/offers/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var offer = Offers.fromJson(data["data"]);
        _selectOffer = offer;
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> endOffer({required int offerId}) async {
    printty(offerId, level: "End Offer");
    return await performApiCall(
      url: "/marketplace/offers/$offerId/end",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        var offer = Offers.fromJson(data["data"]);
        _selectOffer = offer;
        printty(_selectOffer, level: "End Offer");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getOfferActivity() async {
    return await performApiCall(
      url: "/marketplace/offers/activities",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _offerDealActivity =
            offerDealActivityFromJson(json.encode(data["datatable"]["data"]));
        return apiResponse;
      },
    );
  }

  clearData() {
    _myBuyOffers = [];
    _mySellOffers = [];
    _offerDealActivity = [];
    _selectOffer = null;
    _yourOfferType = OfferType.deal;

    reBuildUI();
  }
}
