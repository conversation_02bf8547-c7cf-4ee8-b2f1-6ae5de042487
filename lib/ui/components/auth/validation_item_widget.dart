import 'package:korrency/core/core.dart';

class ValidationItemWidget extends StatefulWidget {
  final String label;
  final bool isValid;
  final Function()? onTap;
  const ValidationItemWidget({
    required this.label,
    this.isValid = false,
    this.onTap,
    super.key,
  });

  @override
  State<ValidationItemWidget> createState() => _ValidationItemWidgetState();
}

class _ValidationItemWidgetState extends State<ValidationItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    // Initialize animation state based on initial isValid value
    if (widget.isValid) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ValidationItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isValid != oldWidget.isValid) {
      if (widget.isValid) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: widget.onTap,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: 20.w,
            height: 20.h,
            padding: EdgeInsets.all(Sizer.radius(4)),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
              border: Border.all(
                color: widget.isValid ? AppColors.green43 : AppColors.gray5E7,
              ),
            ),
            child: ScaleTransition(
              scale: _animation,
              child: widget.isValid
                  ? Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(Sizer.radius(20)),
                        color: AppColors.green43,
                      ),
                    )
                  : null,
            ),
          ),
        ),
        XBox(8.w),
        Expanded(
          child: AnimatedDefaultTextStyle(
            duration: const Duration(milliseconds: 300),
            style: TextStyle(
              color: widget.isValid ? AppColors.gray79 : AppColors.gray93,
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
            ),
            child: Text(widget.label),
          ),
        )
      ],
    );
  }
}
