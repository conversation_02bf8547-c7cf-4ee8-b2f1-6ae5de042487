import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CountryPickerModal extends StatefulWidget {
  const CountryPickerModal({super.key});

  @override
  State<CountryPickerModal> createState() => _CountryPickerModalState();
}

class _CountryPickerModalState extends State<CountryPickerModal> {
  final TextEditingController _searchController = TextEditingController();
  List<CountryModel> _filteredCountries = [];

  @override
  void initState() {
    super.initState();
    _filteredCountries = CountryModel.countries;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = CountryModel.countries;
      } else {
        _filteredCountries = CountryModel.countries.where((country) {
          final name = country.name?.toLowerCase() ?? '';
          final dialCode = country.dialCode?.toLowerCase() ?? '';
          final code = country.code?.toLowerCase() ?? '';

          return name.contains(query) ||
              dialCode.contains(query) ||
              code.contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: SizedBox(
        height: Sizer.screenHeight * 0.5,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    padding: EdgeInsets.all(Sizer.width(10)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppColors.white.withValues(alpha: 0.15),
                      border: Border.all(
                        color: AppColors.white.withValues(alpha: 0.70),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          "Close",
                          style: AppTypography.text14.medium.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                        XBox(4),
                        Icon(
                          Icons.close,
                          size: Sizer.width(16),
                          color: AppColors.white,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const YBox(12),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(20)),
                decoration: BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    YBox(20),
                    CustomTextField(
                      controller: _searchController,
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.asset(AppSvgs.search),
                      ),
                      hintText: "Search your country code",
                      keyboardType: KeyboardType.regular,
                      inputFormatters: [],
                      borderRadius: Sizer.height(12),
                      onSubmitted: (val) {
                        // Optional: Handle search submission
                      },
                    ),
                    YBox(10),
                    Expanded(
                      child: _filteredCountries.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: Sizer.width(48),
                                    color: AppColors.gray500,
                                  ),
                                  YBox(16),
                                  Text(
                                    "No countries found",
                                    style: AppTypography.text16.copyWith(
                                      color: AppColors.gray500,
                                    ),
                                  ),
                                  YBox(8),
                                  Text(
                                    "Try searching with a different term",
                                    style: AppTypography.text14.copyWith(
                                      color: AppColors.gray93,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.separated(
                              shrinkWrap: true,
                              padding: EdgeInsets.only(
                                top: Sizer.height(20),
                                bottom: Sizer.height(20),
                              ),
                              itemCount: _filteredCountries.length,
                              separatorBuilder: (_, __) => YBox(12),
                              itemBuilder: (context, index) {
                                return CountryPickerItem(
                                  country: _filteredCountries[index],
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CountryPickerItem extends StatelessWidget {
  const CountryPickerItem({super.key, required this.country});

  final CountryModel country;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pop(context, country);
      },
      child: Padding(
        padding: EdgeInsets.all(Sizer.radius(10)),
        child: Row(
          children: [
            SvgPicture.asset(
              country.flag ?? '',
              width: Sizer.width(24),
              height: Sizer.width(24),
            ),
            XBox(12),
            Expanded(
              child: Text(
                country.name ?? '',
                style: AppTypography.text16.copyWith(
                  color: AppColors.gray51,
                ),
              ),
            ),
            Text(
              country.dialCode ?? '',
              style: AppTypography.text16.copyWith(
                color: AppColors.gray51,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
