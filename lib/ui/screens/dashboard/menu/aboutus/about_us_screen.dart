import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';
import 'package:store_redirect/store_redirect.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({super.key});

  @override
  State<AboutUsScreen> createState() => _AboutUsScreenState();
}

class _AboutUsScreenState extends State<AboutUsScreen> {
  String? _firstPathSegment(String url) {
    try {
      final uri = Uri.parse(url);
      if (uri.pathSegments.isNotEmpty) {
        final seg = uri.pathSegments.first;
        if (seg.isNotEmpty) return seg;
      }
      return null;
    } catch (_) {
      return null;
    }
  }

  Future<void> _openWithFallback(BuildContext context,
      {Uri? appUri, required String url}) async {
    try {
      if (appUri != null && await canLaunchUrl(appUri)) {
        await launchUrl(appUri);
        return;
      }

      final httpsUri = Uri.parse(url);
      if (await canLaunchUrl(httpsUri)) {
        await launchUrl(httpsUri, mode: LaunchMode.externalApplication);
        return;
      }

      Navigator.pushNamed(
        context,
        RoutePath.createFreshDeskTicketWebview,
        arguments: WebViewArg(
          webURL: url,
        ),
      );
    } catch (_) {
      Navigator.pushNamed(
        context,
        RoutePath.createFreshDeskTicketWebview,
        arguments: WebViewArg(
          webURL: url,
        ),
      );
    }
  }
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ConfigVM>().getConfigurations();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "About Korrency",
                subtitle: "Need assistance? We're here for you!",
              ),
              const YBox(32),
              MenuListTileOLD(
                title: 'Our Story',
                iconData: Iconsax.sms,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Our Story",
                      webURL: AppUtils.korrencyAbout,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTileOLD(
                title: 'Terms and Conditions',
                iconData: Iconsax.document_1,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Terms and Conditions",
                      webURL: AppUtils.korrencyTermsAndCondition,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTileOLD(
                title: 'Privacy Policy',
                iconData: Iconsax.messages_2,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Privacy Policy",
                      webURL: AppUtils.korrencyPolicy,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTileOLD(
                title: 'Our Blog',
                iconData: Iconsax.camera,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Our Blog",
                      webURL: AppUtils.korrencyBlog,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTileOLD(
                title: 'Rate Our App',
                iconData: Iconsax.star,
                onPressed: () {
                  printty("Rate Our App");

                  StoreRedirect.redirect(
                    androidAppId: "korrency.mobile.com",
                    iOSAppId: "6495368627",
                  );
                },
              ),
              // const YBox(26),
              // MenuListTileOLD(
              //   title: 'Drop a Feedback',
              //   iconData: Iconsax.sms_edit,
              //   onPressed: () {},
              // ),
              const Spacer(),
              Consumer<ConfigVM>(builder: (context, vm, _) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    CurrencyCard(
                      svgPath: AppSvgs.fb,
                      title: "Facebook",
                      bgColor: AppColors.litGrey,
                      onTap: () async {
                        final url = vm.faceboookURL ?? "";
                        final appUri = Uri.parse(
                            'fb://facewebmodal/f?href=${Uri.encodeComponent(url)}');
                        await _openWithFallback(
                          context,
                          appUri: appUri,
                          url: url,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.ig,
                      title: "Instagram",
                      bgColor: AppColors.opacityRed100,
                      onTap: () async {
                        final url = vm.instagramURL ?? "";
                        final username = _firstPathSegment(url);
                        final appUri = (username != null)
                            ? Uri.parse('instagram://user?username=$username')
                            : null;
                        await _openWithFallback(
                          context,
                          appUri: appUri,
                          url: url,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.x,
                      title: "X (Twitter)",
                      onTap: () async {
                        final url = vm.twitterURL ?? "";
                        final username = _firstPathSegment(url);
                        final appUri = (username != null)
                            ? Uri.parse('twitter://user?screen_name=$username')
                            : null;
                        await _openWithFallback(
                          context,
                          appUri: appUri,
                          url: url,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.lk,
                      title: "LinkedIn",
                      onTap: () async {
                        final url = vm.linkedInURL ?? "";
                        await _openWithFallback(
                          context,
                          appUri: null,
                          url: url,
                        );
                      },
                    ),
                  ],
                );
              }),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}
