import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class MenuListTile extends StatelessWidget {
  const MenuListTile({
    super.key,
    this.iconData,
    required this.title,
    this.subTitle,
    required this.icon,
    this.bgColor,
    this.onPressed,
  });

  final IconData? iconData;
  final String title;
  final String? subTitle;
  final String icon;
  final Color? bgColor;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Row(
        children: [
          SvgPicture.asset(
            icon,
            height: Sizer.height(32),
            width: Sizer.width(32),
          ),
          const XBox(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.text16.medium.copyWith(
                  fontFamily: AppFont.outfit.family,
                  color: AppColors.gray51,
                ),
              ),
              if (subTitle != null)
                Text(
                  subTitle ?? '',
                  style: AppTypography.text12.copyWith(
                    fontFamily: AppFont.outfit.family,
                    color: AppColors.gray93,
                  ),
                ),
            ],
          ),
          const Spacer(),
          Icon(
            Iconsax.arrow_right_3,
            size: Sizer.radius(20),
            color: AppColors.iconBlack800,
          ),
        ],
      ),
    );
  }
}
