import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class InfoDialog extends StatelessWidget {
  const InfoDialog({
    Key? key,
    this.onConfirm,
    required this.btnText,
    required this.msg,
  }) : super(key: key);

  final VoidCallback? onConfirm;
  final String btnText;
  final String msg;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Text(
            msg,
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(30),
          CustomBtn.solid(
            height: 36,
            onlineColor: AppColors.primaryBlue,
            onTap: onConfirm ?? () => Navigator.pop(context),
            text: btnText,
          ),
        ],
      ),
    );
  }
}
