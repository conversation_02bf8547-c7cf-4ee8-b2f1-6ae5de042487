import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class CreateAlertScreen extends StatefulWidget {
  const CreateAlertScreen({super.key});

  @override
  State<CreateAlertScreen> createState() => _CreateAlertScreenState();
}

class _CreateAlertScreenState extends State<CreateAlertScreen> {
  final FocusNode _rateFocusNode = FocusNode();
  final FocusNode _minFocusNode = FocusNode();
  final FocusNode _maxFocusNode = FocusNode();
  final FocusNode _durationFocusNode = FocusNode();

  RangeValues _rangeValues = const RangeValues(0.0, 0.5);
  OfferType offerType = OfferType.sell;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      var configVM = context.read<ConfigVM>();
      var rateAlertVM = context.read<MarketRateAlertVM>();

      _rangeValues =
          RangeValues(configVM.rangeDoubleMIN, configVM.rangeDoubleMAX);
      rateAlertVM.minC.text = configVM.marketplaceMinimumAmount ?? "";
      rateAlertVM.maxC.text = configVM.marketplaceMaximumAmount ?? "";

      rateAlertVM.reBuildUI();
    });
  }

  @override
  void dispose() {
    _rateFocusNode.dispose();
    _minFocusNode.dispose();
    _maxFocusNode.dispose();
    _durationFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    RangeLabels lebels =
        RangeLabels(_rangeValues.start.toString(), _rangeValues.end.toString());
    return Consumer<MarketRateAlertVM>(builder: (context, vm, child) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            bottom: false,
            child: ListView(
              shrinkWrap: true,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ).copyWith(
                    top: Sizer.height(20),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.centerLeft,
                        child: const ArrowBack(),
                      ),
                      const YBox(30),
                      const AuthTextSubTitle(
                        title: "Create an Alert",
                        subtitle: "You will be notified when...",
                      ),
                      const YBox(20),
                      Row(
                        children: [
                          TextRow(
                            text: "Sell Offers",
                            isSelected: offerType == OfferType.sell,
                            onTap: () {
                              offerType = OfferType.sell;
                              vm.reBuildUI();
                            },
                          ),
                          const XBox(16),
                          TextRow(
                            text: "Buy Offers",
                            isSelected: offerType == OfferType.buy,
                            onTap: () {
                              offerType = OfferType.buy;
                              vm.reBuildUI();
                            },
                          )
                        ],
                      ),
                      const YBox(40),
                      CustomTextField(
                        focusNode: _rateFocusNode,
                        labelText: "Seller’s rate is below",
                        fillColor: AppColors.fillColor,
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        hintText: "CAD 1,200",
                        controller: vm.rateC,
                        keyboardType: KeyboardType.number,
                        onChanged: (val) => vm.reBuildUI(),
                        onSubmitted: (p0) {
                          _rateFocusNode.unfocus();
                          _minFocusNode.requestFocus();
                        },
                      ),
                      const YBox(40),
                      Text(
                        "Seller’s offer amount is (Optional)",
                        style: AppTypography.text15
                            .copyWith(color: AppColors.gray600),
                      ),
                      const YBox(16),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: CustomTextField(
                              focusNode: _minFocusNode,
                              labelText: "Between (CAD)",
                              fillColor: AppColors.blue100,
                              showLabelHeader: true,
                              borderRadius: Sizer.height(4),
                              hintText: "500 CAD",
                              controller: vm.minC,
                              errorText: context.read<ConfigVM>().minCheck(
                                      double.tryParse(vm.minC.text) ?? 0.0)
                                  ? "Minimum amount is out of range"
                                  : null,
                              keyboardType: KeyboardType.number,
                              onChanged: (val) => vm.reBuildUI(),
                              onSubmitted: (p0) {
                                _minFocusNode.unfocus();
                                _maxFocusNode.requestFocus();
                              },
                            ),
                          ),
                          const XBox(16),
                          Expanded(
                            child: CustomTextField(
                              focusNode: _maxFocusNode,
                              labelText: "And (CAD)",
                              fillColor: AppColors.blue100,
                              showLabelHeader: true,
                              borderRadius: Sizer.height(4),
                              hintText: "10,000 CAD",
                              controller: vm.maxC,
                              keyboardType: KeyboardType.number,
                              errorText: context.read<ConfigVM>().maxCheck(
                                      double.tryParse(vm.maxC.text) ?? 0.0)
                                  ? "Maximum amount is out of range"
                                  : null,
                              onChanged: (val) => vm.reBuildUI(),
                              onSubmitted: (p0) {
                                _maxFocusNode.unfocus();
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const YBox(24),
                Consumer<ConfigVM>(builder: (context, configVM, _) {
                  return Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(10),
                    ),
                    child: RangeSlider(
                      values: _rangeValues,
                      labels: lebels,
                      min: configVM.rangeDoubleMIN,
                      max: configVM.rangeDoubleMAX,
                      activeColor: AppColors.primaryBlue,
                      inactiveColor: AppColors.darkBlue101,
                      onChanged: (RangeValues val) {
                        printty('Range value $val');
                        _rangeValues = val;
                        vm.minC.text = val.start.round().toString();
                        vm.maxC.text = val.end.round().toString();
                        vm.reBuildUI();
                      },
                    ),
                  );
                }),
                const YBox(100),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: CustomBtn.solid(
                    height: 56,
                    onTap: () {
                      _createRateAlert();
                    },
                    online: vm.btnEnabled &&
                        !context.read<ConfigVM>().minCheck(
                              double.tryParse(vm.minC.text) ?? 0.0,
                            ) &&
                        !context.read<ConfigVM>().maxCheck(
                              double.tryParse(vm.maxC.text) ?? 0.0,
                            ),
                    text: "Create Alert",
                  ),
                ),
                const YBox(100),
              ],
            ),
          ),
        ),
      );
    });
  }

  _createRateAlert() {
    var rateAlertVM = context.read<MarketRateAlertVM>();
    rateAlertVM.createAlert(offerType: offerType.name).then((value) {
      if (value.success) {
        _showComfirmationScreen(msg: value.message);
        rateAlertVM.clearData();
      } else {
        _showErrorConfirmationScreen(msg: value.message);
      }
    });
  }

  _showErrorConfirmationScreen({String? msg}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }

  _showComfirmationScreen({String? msg}) {
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Alert created successfully",
        btnText: "View Alerts",
        btnTap: () {
          Navigator.of(NavigatorKeys.appNavigatorKey.currentContext!)
              .pushReplacementNamed(RoutePath.setAlertScreen);
        },
        secondBtnText: 'Home',
        secondBtnTap: () {
          Navigator.of(NavigatorKeys.appNavigatorKey.currentContext!)
              .pushNamedAndRemoveUntil(
                  RoutePath.dashboardNav, (route) => false);
        },
      ),
    );
  }
}
